# 数据管理工程 - DDD领域模型图

## 📊 整体领域模型架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            数据管理领域 (Data Management Domain)                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    核心子域 (Core Domain)                                  │   │
│  │                 数据管理组件 (Data Management)                           │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                  支撑子域 (Supporting Domain)                             │   │
│  │  NDS │ PM │ CM │ FM │ VMAX (提供数据源和上报数据)                         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                  通用子域 (Generic Domain)                                │   │
│  │  安全 │ 日志 │ 文件操作 │ 任务调度                                       │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🏗️ 聚合根和值对象关系图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        数据导入任务聚合 (DataImportTask)                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    DataImportTask (聚合根)                                │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │   TaskId       │  │   TaskName      │  │   TaskStatus    │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │  │                          关联对象                                       │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │  │   DataSource   │  │ExecutionStrategy│  │   DataRange     │         │   │
│  │  │  │    (值对象)     │  │    (实体)       │  │    (值对象)     │         │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │  │ImportProgress  │  │  TaskLog List  │  │   Timestamps    │         │   │
│  │  │  │    (值对象)     │  │    (实体)       │  │                 │         │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │  └─────────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        数据清除任务聚合 (DataCleanupTask)                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    DataCleanupTask (聚合根)                               │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │   TaskId       │  │   TaskName      │  │   TaskStatus    │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │  │                          关联对象                                       │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │  │TargetDataSource│  │ CleanupStrategy │  │CleanupProgress │         │   │
│  │  │  │    (值对象)     │  │    (实体)       │  │    (值对象)     │         │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │  │  TaskLog List  │  │   Timestamps    │  │ SafetyCheck     │         │   │
│  │  │  │    (实体)       │  │                 │  │    (实体)       │         │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │  └─────────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🔗 值对象详细设计

### DataSource 值对象
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            DataSource (值对象)                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │   protocol      │  │      host       │  │      port       │                 │
│  │   (String)      │  │    (String)     │  │    (Integer)    │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │   username      │  │encryptedPassword│  │      path       │                 │
│  │   (String)      │  │    (String)      │  │    (String)     │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────┐                                                           │
│  │   DataSourceType│                                                           │
│  │  (NDS/PM/CM/FM) │                                                           │
│  └─────────────────┘                                                           │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                           业务方法                                         │   │
│  │  • getConnectionInfo() : ConnectionInfo                                   │   │
│  │  • isValid() : boolean                                                     │   │
│  │  • getConnectionString() : String                                         │   │
│  │  • supportsProtocol(String protocol) : boolean                            │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### DataRange 值对象
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                             DataRange (值对象)                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │ timeRangeType   │  │    startTime     │  │     endTime      │                 │
│  │ (相对/绝对时间)  │  │ (LocalDateTime)  │  │ (LocalDateTime)  │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │  granularity    │  │   cellList      │  │  frequencyList   │                 │
│  │ (时间粒度)       │  │   (网元列表)     │  │   (频点列表)     │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                           业务方法                                         │   │
│  │  • isValidRange() : boolean                                               │   │
│  │  • getDuration() : Duration                                              │   │
│  │  • contains(LocalDateTime time) : boolean                               │   │
│  │  • overlapsWith(DataRange other) : boolean                             │   │
│  │  • splitByGranularity() : List<DataRange>                               │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🏛️ 实体详细设计

### ExecutionStrategy 实体
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         ExecutionStrategy (实体)                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │  strategyId     │  │  executionType  │  │  scheduleConfig │                 │
│  │  (StrategyId)   │  │ (立即/定时/周期)  │  │ (调度配置)       │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                 │
│  │  retryPolicy    │  │notificationConfig│  │  timeoutConfig  │                 │
│  │  (重试策略)      │  │  (通知配置)      │  │  (超时配置)      │                 │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                 │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                           业务方法                                         │   │
│  │  • shouldExecuteNow() : boolean                                          │   │
│  │  • getNextExecutionTime() : LocalDateTime                               │   │
│  │  • canRetry() : boolean                                                   │   │
│  │  • updateRetryCount() : void                                             │   │
│  │  • validateSchedule() : boolean                                          │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🔄 领域服务关系图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          领域服务层                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    DataImportService                                       │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ importData()    │  │validateDataSource│  │  previewData()  │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ processImport() │  │ monitorImport() │  │ handleErrors()  │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                   DataCleanupService                                      │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ cleanupData()   │  │performSafetyCheck│  │ generatePlan()  │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ validateScope() │  │ executeCleanup()│  │ verifyResult()  │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                  TaskScheduleService                                      │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ scheduleTask()  │  │executeScheduled()│  │ getPending()    │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │                                                                                 │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │ activate()      │  │ deactivate()    │  │ monitorStatus() │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🗄️ 仓储模式图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            仓储模式                                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                          应用服务层                                        │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                    DataImportApplicationService                        │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                   DataCleanupApplicationService                        │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                       │                                       │
│                                       ▼                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                          仓储接口层                                        │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                        TaskRepository                                 │   │
│  │  │  • findImportTaskById(TaskId) : DataImportTask                        │   │
│  │  │  • findCleanupTaskById(TaskId) : DataCleanupTask                      │   │
│  │  │  • saveImportTask(DataImportTask) : void                             │   │
│  │  │  • saveCleanupTask(DataCleanupTask) : void                           │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                      ScheduleRepository                               │   │
│  │  │  • findById(ScheduleId) : TaskSchedule                               │   │
│  │  │  • save(TaskSchedule) : void                                         │   │
│  │  │  • findActiveSchedules() : List<TaskSchedule>                       │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                       │                                       │
│                                       ▼                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                          基础设施层                                        │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                     TaskRepositoryImpl                                │   │
│  │  │  (JPA/Hibernate/MyBatis实现)                                           │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                   ScheduleRepositoryImpl                              │   │
│  │  │  (JPA/Hibernate/MyBatis实现)                                           │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🎯 限界上下文关系图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        限界上下文映射                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                  数据管理上下文                                           │
│  │                  (Data Management Context)                              │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │DataImportTask  │  │DataCleanupTask │  │  DataSource     │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │           │                      │                      │               │   │
│  │           └──────────────────────┼──────────────────────┘               │   │
│  │                                  │                                      │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                    共享内核 (Shared Kernel)                           │   │
│  │  │          TaskId, TaskStatus, TaskType, Timestamp                   │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                       │                                       │
│                  ┌─────────────────────┼─────────────────────┐                 │
│                  │                     │                     │                 │
│                  ▼                     ▼                     ▼                 │
│  ┌─────────────────────────┐ ┌─────────────────────────┐ ┌─────────────────────────┐ │
│  │   任务调度上下文       │ │   数据源上下文         │ │   监控告警上下文       │ │
│  │(Task Scheduling Context)│ │ (Data Source Context) │ │ (Monitoring Context)   │ │
│  │                         │ │                         │ │                         │ │
│  │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │
│  │ │   TaskSchedule     │ │ │ │   ConnectionInfo   │ │ │ │      Alert          │ │ │
│  │ └─────────────────────┘ │ │ └─────────────────────┘ │ │ └─────────────────────┘ │ │
│  │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │
│  │ │ ExecutionStrategy  │ │ │ │   DataSourceConfig  │ │ │ │      Monitor         │ │ │
│  │ └─────────────────────┘ │ │ └─────────────────────┘ │ │ └─────────────────────┘ │ │
│  └─────────────────────────┘ └─────────────────────────┘ └─────────────────────────┘ │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                      防腐层 (Anti-Corruption Layer)                      │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │   NDS Adapter   │  │   PM Adapter    │  │   CM Adapter    │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │   FM Adapter    │  │  VMAX Adapter   │  │External Systems │         │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 📊 事件驱动架构图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        事件驱动架构                                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                        事件发布者                                          │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                      DataImportTask                                   │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │   │
│  │  │  │   start()       │  │   complete()    │  │     fail()      │     │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                     DataCleanupTask                                  │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │   │
│  │  │  │  startCleanup() │  │ completeCleanup()│  │ failCleanup()  │     │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                       │                                       │
│                                       ▼                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                        事件总线                                           │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │  │                      Event Bus                                       │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │   │
│  │  │  │   publish()     │  │   subscribe()   │  │   unsubscribe() │         │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │   │
│  │  └─────────────────────────────────────────────────────────────────────┘   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                       │                                       │
│                  ┌─────────────────────┼─────────────────────┐                 │
│                  │                     │                     │                 │
│                  ▼                     ▼                     ▼                 │
│  ┌─────────────────────────┐ ┌─────────────────────────┐ ┌─────────────────────────┐ │
│  │   事件订阅者           │ │   事件订阅者           │ │   事件订阅者           │ │ │
│  │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │
│  │ │   Monitoring        │ │ │   Logging Service    │ │ │   Notification       │ │ │
│  │ │   Service           │ │ │                         │ │ │   Service            │ │ │
│  │ └─────────────────────┘ │ │ └─────────────────────┘ │ │ └─────────────────────┘ │ │
│  │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │ ┌─────────────────────┐ │ │
│  │ │   Analytics         │ │ │   Audit Service      │ │ │   Reporting          │ │ │
│  │ │   Service           │ │ │                         │ │ │   Service            │ │ │
│  │ └─────────────────────┘ │ │ └─────────────────────┘ │ │ └─────────────────────┘ │ │
│  └─────────────────────────┘ └─────────────────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

这些图表展示了数据管理工程的完整DDD领域模型设计，包括：

1. **整体架构**: 清晰的领域划分和层次结构
2. **聚合设计**: 核心聚合根及其关联对象
3. **值对象**: 不可变的业务概念
4. **实体**: 具有生命周期的业务对象
5. **领域服务**: 跨聚合的业务逻辑
6. **仓储模式**: 数据访问抽象
7. **限界上下文**: 明确的边界和集成方式
8. **事件驱动**: 松耦合的系统交互

这个设计为系统的可维护性、可扩展性和业务对齐度提供了坚实的基础。