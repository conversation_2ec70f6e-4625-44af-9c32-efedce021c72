taskid=$1
emslist=$2
u31lte=$3
umelte=$4
umenr=$5
downloadpath=`cat /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf|grep "downloadpath"|awk -F "=" '{ print $2 }'`

dbpath=`readlink -f /home/<USER>/ngi-gpdb/ext/dm/bin/NetMAXPLAT_DM.db`
echo "dataReuse.sh: downloadpath: $downloadpath dbpath: ${dbpath}"

cmd="rm -rf $downloadpath/CMBACKUP/NetMAXPLAT_DM.db;  
     cp ${dbpath} $downloadpath/CMBACKUP/"
echo $cmd
bash -c "$cmd"

count=`sqlite3 $downloadpath/CMBACKUP/NetMAXPLAT_DM.db "select count(*) from dm_download_state where task_id='${taskid}'"`
echo "dataReuse.sh: total count for ${taskid}: ${count}"
if [ ${count} = 0 ]
then
    echo "dataReuse.sh: no new data...exit"
    exit
fi
#emslist=$2
for ems in ${emslist[@]}
do
    ip=`echo $ems|awk -F ":" '{ print $1 }'`
    port=`echo $ems|awk -F ":" '{ print $2 }'`
    echo "dataReuse.sh: ems: ${ems} ip: $ip port: $port"
    if [ "${umelte}" = "true" ]
    then
        count=`sqlite3 $downloadpath/CMBACKUP/NetMAXPLAT_DM.db "select count(*) from dm_download_state where model in ('LTEUMECM') and ftp_ip='$ip' and ftp_port='$port' and task_id='${taskid}'"`
        if [ ${count} = 0 ]
        then
            backup="${downloadpath}/CMBACKUP/UMECM4G/UME_${ip}_${port}/"
            if [ -d "${backup}" ]
            then
                echo "dataReuse.sh: LTEUMECM is null...using ${backup} backup data"
                cp -av ${backup}/* /home/<USER>/ngi-gpdb/ext/dm/emsdata/UME/cm/
            fi	
        fi
    fi
    if [ "${umenr}" = "true" ]
    then
        count=`sqlite3 $downloadpath/CMBACKUP/NetMAXPLAT_DM.db "select count(*) from dm_download_state where model in ('5GUMECM') and ftp_ip='$ip' and ftp_port='$port' and task_id='${taskid}'"`
        if [ ${count} = 0 ]
        then
            backup="${downloadpath}/CMBACKUP/UMECM5G/UME_${ip}_${port}/"
            if [ -d "${backup}" ]
            then
                echo "dataReuse.sh: 5GUMECM is null...using ${backup} backup data"
                cp -av ${backup}/* /home/<USER>/ngi-gpdb/ext/dm/emsdata/NR/UME/cm/
            fi
        fi
    fi
done
if [ "${u31lte}" = "true" ]
then
    count=`sqlite3 $downloadpath/CMBACKUP/NetMAXPLAT_DM.db "select count(*) from dm_download_state where model in ('LTEU31CM') and task_id='${taskid}'"`
    if [ ${count} = 0 ]
    then
        backup="${downloadpath}/CMBACKUP/U31CM4G/U31_127.0.0.1_22/"
        if [ -d "${backup}" ]
        then
            echo "dataReuse.sh: LTEU31CM is null...using ${backup} backup data"
            cp -av ${backup}/* /home/<USER>/ngi-gpdb/ext/dm/emsdata/cm/
        fi
    fi
fi
rm -rf $downloadpath/CMBACKUP/NetMAXPLAT_DM.db

