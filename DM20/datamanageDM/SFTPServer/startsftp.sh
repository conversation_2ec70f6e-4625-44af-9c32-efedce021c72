#!/bin/bash
CURRDIR=$(pwd)

cd ../jdk
export JAVA_HOME=$(pwd)
cd $CURRDIR

portok=0

for cp in $@
do
  if [ $cp == "-p" ]; then
    portok=1
    break
  fi
done
if [ $portok -eq 0 ]; then
  echo "Usage: startsftp.sh -p [port] ..."
  exit
fi
str=$"\n"
#cmd="nohup $CURRDIR/bin/sshd.sh $@"
#$cmd >/dev/null 2>&1 &
#$cmd 2>SftpError.log &
#nohup $CURRDIR/bin/sshd.sh $@ 2>SftpError.log &

if [ -f SftpError.log ];then
    VERSION_CONTROL=numbered cp -b SftpError.log SftpError.log.bak
    ls -tr SftpError.log.bak* | head -n -5 | xargs rm -f
fi

nohup $CURRDIR/bin/sshd.sh $@ >SftpError.log 2>&1 &
sstr=$(echo -e $str)
echo $sstr
