handlers=java.util.logging.FileHandler, java.util.logging.ConsoleHandler
java.util.logging.ConsoleHandler.level=INFO
java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter
 
java.util.logging.FileHandler.level=INFO
java.util.logging.FileHandler.pattern=../logs/SFTPServer.log
 
# Write 10MB before rotating this file
java.util.logging.FileHandler.limit=10000000
 
# Number of rotating files to be used
java.util.logging.FileHandler.count=10
java.util.logging.FileHandler.formatter=java.util.logging.SimpleFormatter
 
.level=INFO
