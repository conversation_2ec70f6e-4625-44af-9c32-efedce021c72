#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZTE OKR系统自动化脚本 - 使用Playwright (修复版本)
用于自动登录并操作PBC部分
"""

import asyncio
import datetime
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, BrowserContext

class ZTEOKRAutomationPlaywright:
    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.playwright = None
        self.base_url = "https://unipos.zte.com.cn:18080/#/oct/okr"
        
    async def setup_browser(self):
        """设置Playwright浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--window-size=1920,1080"
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            print("Playwright浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"浏览器初始化失败: {e}")
            return False
    
    async def login(self):
        """登录到OKR系统"""
        try:
            print(f"正在访问: {self.base_url}")
            await self.page.goto(self.base_url, wait_until="networkidle")
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 查找用户名输入框
            username_input = await self.page.wait_for_selector("input[name='username'], input[type='text']", timeout=10000)
            if username_input:
                await username_input.fill("")
                await username_input.type(self.username)
                print("用户名输入完成")
            else:
                print("未找到用户名输入框")
                return False
            
            # 查找密码输入框
            password_input = await self.page.wait_for_selector("input[name='password'], input[type='password']", timeout=5000)
            if password_input:
                await password_input.fill("")
                await password_input.type(self.password)
                print("密码输入完成")
            else:
                print("未找到密码输入框")
                return False
            
            # 查找登录按钮并点击
            login_button = await self.page.wait_for_selector("button:has-text('登录'), button:has-text('Login'), input[type='submit']", timeout=5000)
            if login_button:
                await login_button.click()
                print("登录按钮已点击")
            else:
                print("未找到登录按钮")
                return False
            
            # 等待登录完成
            await asyncio.sleep(5)
            
            # 检查登录后的页面状态
            current_url = self.page.url
            print(f"登录后当前URL: {current_url}")
            
            # 如果不在OKR页面，尝试导航到OKR
            if "okr" not in current_url.lower():
                print("登录成功，但不在OKR页面，尝试导航到OKR...")
                await self.navigate_to_okr()
            
            print("登录成功！")
            return True
                
        except Exception as e:
            print(f"登录过程中出现错误: {e}")
            return False
    
    async def navigate_to_okr(self):
        """导航到OKR页面"""
        try:
            print("正在尝试导航到OKR页面...")
            
            # 尝试多种方式导航到OKR
            okr_urls = [
                "https://unipos.zte.com.cn:18080/#/oct/okr",
                "https://unipos.zte.com.cn:18080/#/okr",
                "https://unipos.zte.com.cn:18080/#/oct"
            ]
            
            for url in okr_urls:
                try:
                    print(f"尝试访问: {url}")
                    await self.page.goto(url, wait_until="networkidle")
                    await asyncio.sleep(3)
                    
                    current_url = self.page.url
                    if "okr" in current_url.lower():
                        print(f"成功导航到OKR页面: {current_url}")
                        return True
                except Exception as e:
                    print(f"访问 {url} 失败: {e}")
                    continue
            
            # 如果直接导航失败，尝试在页面上查找OKR相关链接
            print("尝试在页面上查找OKR链接...")
            okr_selectors = [
                "a:has-text('OKR')",
                "a:has-text('okr')",
                "span:has-text('OKR')",
                "div:has-text('OKR')",
                "li:has-text('OKR')",
                "[href*='okr']",
                "[href*='oct']"
            ]
            
            for selector in okr_selectors:
                try:
                    okr_link = await self.page.wait_for_selector(selector, timeout=3000)
                    if okr_link:
                        print(f"找到OKR链接: {selector}")
                        await okr_link.click()
                        await asyncio.sleep(3)
                        
                        current_url = self.page.url
                        if "okr" in current_url.lower():
                            print(f"通过链接成功导航到OKR页面: {current_url}")
                            return True
                except:
                    continue
            
            print("无法导航到OKR页面，将在当前页面继续操作")
            return False
            
        except Exception as e:
            print(f"导航到OKR页面时出现错误: {e}")
            return False
    
    async def find_pbc_section(self):
        """查找PBC部分"""
        try:
            print("正在查找PBC部分...")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 尝试多种可能的PBC元素定位方式
            pbc_selectors = [
                "span:has-text('PBC')",
                "div:has-text('PBC')",
                "a:has-text('PBC')",
                "li:has-text('PBC')",
                "button:has-text('PBC')",
                "[data-testid*='pbc']",
                "[class*='pbc']",
                "text=PBC",
                "text=pbc"
            ]
            
            pbc_element = None
            for selector in pbc_selectors:
                try:
                    pbc_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if pbc_element:
                        print(f"找到PBC元素: {selector}")
                        break
                except:
                    continue
            
            if pbc_element:
                await pbc_element.click()
                print("PBC部分已点击")
                await asyncio.sleep(3)
                return True
            else:
                print("未找到PBC部分，尝试截图保存以便调试...")
                # 截图保存以便调试
                try:
                    await self.page.screenshot(path="pbc_not_found.png")
                    print("截图已保存为: pbc_not_found.png")
                except Exception as e:
                    print(f"截图保存失败: {e}")
                
                # 尝试查找页面上的所有文本内容，帮助定位PBC
                print("正在分析页面内容...")
                try:
                    page_text = await self.page.text_content("body")
                    if "PBC" in page_text:
                        print("页面中包含PBC文本，但未找到可点击元素")
                    else:
                        print("页面中未找到PBC相关文本")
                except Exception as e:
                    print(f"页面内容分析失败: {e}")
                
                return False
                
        except Exception as e:
            print(f"查找PBC部分时出现错误: {e}")
            return False
    
    async def select_previous_month(self):
        """选择上一个月"""
        try:
            print("正在查找月份选择器...")
            
            # 获取当前月份
            current_month = datetime.datetime.now().month
            previous_month = current_month - 1 if current_month > 1 else 12
            
            # 月份名称映射（包括多种可能的格式）
            month_names = {
                1: ["一月", "1月", "Jan", "January", "01"],
                2: ["二月", "2月", "Feb", "February", "02"],
                3: ["三月", "3月", "Mar", "March", "03"],
                4: ["四月", "4月", "Apr", "April", "04"],
                5: ["五月", "5月", "May", "May", "05"],
                6: ["六月", "6月", "Jun", "June", "06"],
                7: ["七月", "7月", "Jul", "July", "07"],
                8: ["八月", "8月", "Aug", "August", "08"],
                9: ["九月", "9月", "Sep", "September", "09"],
                10: ["十月", "10月", "Oct", "October", "10"],
                11: ["十一月", "11月", "Nov", "November", "11"],
                12: ["十二月", "12月", "Dec", "December", "12"]
            }
            
            previous_month_names = month_names[previous_month]
            current_month_names = month_names[current_month]
            
            print(f"当前月份: {current_month_names[0]}, 选择月份: {previous_month_names[0]}")
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 尝试多种月份选择器
            month_selectors = []
            for month_name in previous_month_names:
                month_selectors.extend([
                    f"span:has-text('{month_name}')",
                    f"div:has-text('{month_name}')",
                    f"option:has-text('{month_name}')",
                    f"li:has-text('{month_name}')",
                    f"a:has-text('{month_name}')",
                    f"button:has-text('{month_name}')"
                ])
            
            # 添加数字格式的选择器
            month_selectors.extend([
                f"[data-value='{previous_month}']",
                f"[data-month='{previous_month}']",
                f"[value='{previous_month}']",
                f"text={previous_month}"
            ])
            
            month_element = None
            for selector in month_selectors:
                try:
                    month_element = await self.page.wait_for_selector(selector, timeout=2000)
                    if month_element:
                        print(f"找到月份元素: {selector}")
                        break
                except:
                    continue
            
            if month_element:
                await month_element.click()
                print(f"已选择月份: {previous_month_names[0]}")
                await asyncio.sleep(2)
                return True
            else:
                print("未找到月份选择器，尝试截图保存以便调试...")
                # 截图保存以便调试
                try:
                    await self.page.screenshot(path="month_selector_not_found.png")
                    print("截图已保存为: month_selector_not_found.png")
                except Exception as e:
                    print(f"截图保存失败: {e}")
                
                # 尝试查找页面上的所有月份相关元素
                print("正在分析页面上的月份元素...")
                try:
                    all_elements = await self.page.query_selector_all("*")
                    month_elements = []
                    
                    for element in all_elements[:100]:  # 限制检查前100个元素
                        try:
                            text = await element.text_content()
                            if text and any(month_name in text for month_names_list in month_names.values() for month_name in month_names_list):
                                month_elements.append(text.strip())
                        except:
                            continue
                    
                    if month_elements:
                        print(f"页面上找到的月份相关文本: {month_elements[:10]}")  # 显示前10个
                    else:
                        print("页面上未找到月份相关文本")
                except Exception as e:
                    print(f"月份元素分析失败: {e}")
                
                return False
                
        except Exception as e:
            print(f"选择月份时出现错误: {e}")
            return False
    
    async def run_automation(self):
        """运行完整的自动化流程"""
        try:
            if not await self.setup_browser():
                return False
            
            if not await self.login():
                return False
            
            if not await self.find_pbc_section():
                print("PBC部分查找失败，但继续尝试月份选择...")
            
            if not await self.select_previous_month():
                print("月份选择失败")
                return False
            
            print("自动化流程执行完成！")
            return True
            
        except Exception as e:
            print(f"自动化流程执行失败: {e}")
            return False
        finally:
            if self.page:
                print("按回车键关闭浏览器...")
                try:
                    input()
                except:
                    pass
                await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                try:
                    await self.page.close()
                except:
                    pass
            if self.context:
                try:
                    await self.context.close()
                except:
                    pass
            if self.browser:
                try:
                    await self.browser.close()
                except:
                    pass
            if self.playwright:
                try:
                    await self.playwright.stop()
                except:
                    pass
            print("浏览器已关闭")
        except Exception as e:
            print(f"清理资源时出现错误: {e}")

async def main():
    """主函数"""
    print("=== ZTE OKR系统自动化脚本 (Playwright版本) ===")
    print("请确保已安装Playwright: pip install playwright")
    print("首次运行需要安装浏览器: playwright install")
    print()
    
    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("用户名和密码不能为空！")
        return
    
    # 创建自动化实例
    automation = ZTEOKRAutomationPlaywright(username, password)
    
    try:
        # 运行自动化流程
        success = await automation.run_automation()
        
        if success:
            print("所有操作已完成！")
        else:
            print("部分操作失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断了操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        await automation.cleanup()

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
