#!/bin/bash

#发起备份
function do_backup() {
    if [ -f $BACKUP_DIR_FILE ]
    then
       #echo "Backup file exists in the backup directory, file directory is $BACKUP_DIR_FILE."  
       is_same_file $1
       if [ $? == 0 ]
       then
          #echo "DB file and backup file data are different."           
          get_history_backup_file     
          #echo "History backup file index is: ${HISTORY_VERSION_NUMBER_ARRAY[@]}."            
          sort_history_backup_file
          #echo "Sort order of history backup file index is: ${HISTORY_VERSION_NUMBER_ARRAY[@]}."    
          rename_history_backup_file     
          #echo "Final sorted order of history backup file index is: ${HISTORY_VERSION_NUMBER_ARRAY[@]}."   
          delete_history_backup_file   
          #echo "Delete history file end."           
          compress_backup_file $1  
          #echo "Put the compress db file in the backup directory."   
          #echo "Back up file successfully."          
       #else
          #echo "DB file and backup file data are same, no need to backup file." 
          #echo "No need to back up file cause by the same file."
       fi
    else
       #echo "No backup file exists in the backup directory, file directory is $BACKUP_DIR_FILE."  
       if [ ! -d $BACKUP_DIR ]
       then 
           mkdir $BACKUP_DIR
           #echo "Create backup directory sucessfully. backup directory is $BACKUP_DIR."
       fi
       compress_backup_file $1
       #echo "Put the compress db file in the backup directory."
       #echo "Back up file successfully."  
    fi
}

#将DB安装包放在备份目录下
function compress_backup_file(){  
    if [ -f $BACKUP_DIR_FILE ]
    then 
       rm $BACKUP_DIR_FILE
    fi   
    zip -j -q -P $BACKUP_DIR_FILE_PASSWORD $BACKUP_DIR_FILE $1    
}

#判断DB安装包和备份文件是否相等
function is_same_file(){
    uuid=`uuidgen`;
    tempDir=$BACKUP_DIR"tmp"$uuid"/"
    mkdir $tempDir  
    unzip -o -q -P $BACKUP_DIR_FILE_PASSWORD -d $tempDir $BACKUP_DIR_FILE
    diff $tempDir""$DB_FILE $1 > /dev/null
    if [ $? != 0 ]
    then 
       rm -rf $tempDir          
       return 0
    else 
       rm -rf $tempDir 
       return 1
    fi
}

#将最近一次的历史文件重命名
function rename_history_backup_file() {   
    if [ ${#HISTORY_VERSION_NUMBER_ARRAY[@]} == 0 ]       
    then 
       index=0
    else
       index=${HISTORY_VERSION_NUMBER_ARRAY[${#HISTORY_VERSION_NUMBER_ARRAY[@]}-1]}
    fi
    newindex=$[$index + 1]
    newfilename=$BACKUP_DIR_FILE".~"$newindex"~"  
    mv $BACKUP_DIR_FILE $newfilename    
    HISTORY_VERSION_NUMBER_ARRAY[${#HISTORY_VERSION_NUMBER_ARRAY[*]}]=$newindex    
}


#得到所有历史备份文件名中的编号（名称为version_bak.zip.~n~，获取里面的n编号）
function get_history_backup_file(){
	for file in `ls $BACKUP_DIR` 
	do     
       if [ -f $BACKUP_DIR"/"$file ]
	   then		  
		   prefixname=${file:0:17}
		   if [ $prefixname = $BACKUP_FILE".~" ]
		   then
              tempvalue=${file#*~}
              filenumberindex=${tempvalue%~*}
			  HISTORY_VERSION_NUMBER_ARRAY[${#HISTORY_VERSION_NUMBER_ARRAY[*]}]=$filenumberindex
		   fi
		fi  
	done
}


#将所有历史备份文件的尾缀编号按照升序排列(冒泡排序)
function sort_history_backup_file(){            
    for ((i=1;i<${#HISTORY_VERSION_NUMBER_ARRAY[@]};i++))  
    do     
      for ((j=0;j<${#HISTORY_VERSION_NUMBER_ARRAY[@]}-i;j++))
      do       
        left=${HISTORY_VERSION_NUMBER_ARRAY[$j]}       
        k=$[$j + 1]
        right=${HISTORY_VERSION_NUMBER_ARRAY[$k]}       
        if [ $left -gt $right ]
        then        
          tempvar=$left          
          HISTORY_VERSION_NUMBER_ARRAY[$j]=$right       
          HISTORY_VERSION_NUMBER_ARRAY[$k]=$tempvar
        fi
      done
    done
}

#删除早期的历史备份数据
function delete_history_backup_file() {
   index=0             
   if [ ${#HISTORY_VERSION_NUMBER_ARRAY[*]} -gt $SAVE_HISTORY_VERSION_NUMBER ]
   then   
      index=$[ ${#HISTORY_VERSION_NUMBER_ARRAY[*]} - $SAVE_HISTORY_VERSION_NUMBER - 1 ]   
      for ((i = index; i >= 0; i--)) 
      do    
         newfilename=$BACKUP_DIR_FILE".~"${HISTORY_VERSION_NUMBER_ARRAY[i]}"~"
         if [ -f $newfilename ]
         then 
         rm -f $newfilename       
         #echo "Delete history file successfully, file directory is $newfilename."        
         fi    
      done
    fi
}

#开始备份
function begin_backup() {
    #echo "Current directory path is: $CD_CURRENT_DIR_PATH"
    cd $CD_CURRENT_DIR_PATH
    if [ -f $UPDATE_DIR_FILE ]
    then
       #echo "DB file exists in the update directory, file directory is $UPDATE_DIR_FILE."
       do_backup $UPDATE_DIR_FILE
    else
       #echo "No db file exists in the update directory, file directory is $UPDATE_DIR_FILE."
       if [ -f $INSTALL_DIR_FILE ]
       then 
          #echo "DB file exists in the install directory, file directory is $INSTALL_DIR_FILE."
          do_backup $INSTALL_DIR_FILE
      #else 
          #echo "No db file exists in the install directory, file directory is $INSTALL_DIR_FILE."
          #echo "No need to back up file cause by no db file."
      fi
    fi
}

#安装目录
INSTALL_DIR="/home/<USER>/ngi-gpdb/ext/dm/"
#DB安装包名称
DB_FILE="datamanage-newcode.zip"
#安装目录下DB安装包名称
INSTALL_DIR_FILE=$INSTALL_DIR""$DB_FILE

#升级目录
UPDATE_DIR="/home/<USER>/ngi-gpdb/ext/dm/bin/Update/Server/"
#升级目录下的DB安装包名称
UPDATE_DIR_FILE=$UPDATE_DIR""$DB_FILE

#备份目录
BACKUP_DIR="/home/<USER>/ngi-gpdb/ext/dm/versionbak/"
#备份包名称
BACKUP_FILE="version_bak.zip"
#备份目录下的备份包名称
BACKUP_DIR_FILE=$BACKUP_DIR""$BACKUP_FILE

#保存历史文件的版本数,暂时只备份上一个版本的文件
SAVE_HISTORY_VERSION_NUMBER=1

#历史文件编号数组
HISTORY_VERSION_NUMBER_ARRAY=()

BACKUP_DIR_FILE_PASSWORD=$'\x70\x75\x6b\x63\x61\x42\x53\x4f\x50\x69\x6e\x55\x40\x45\x54\x5a\x5f\x32\x30\x32\x32'

#得到当前目录
CURRENT_DIR_PATH=`dirname $0`
#切回到当前目录
CD_CURRENT_DIR_PATH=`cd $CURRENT_DIR_PATH/; pwd`

#main
echo "Start datamanage-newcode backup process ..."
if [ "$USER" == "gpadmin" ]
then
    begin_backup
else
    #只有gpadmin用户才能操作，其他用户不能操作
    echo "Must be gpadmin to run this script."
fi
echo "Finish datamanage-newcode backup process ..."
   
   
   