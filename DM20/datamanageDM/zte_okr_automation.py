#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZTE OKR系统自动化脚本
用于自动登录并操作PBC部分
"""

import time
import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class ZTEOKRAutomation:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.driver = None
        self.base_url = "https://unipos.zte.com.cn:18080/#/oct/okr"
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # chrome_options.add_argument("--headless")  # 无头模式，取消注释可隐藏浏览器窗口
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("浏览器驱动初始化成功")
        except Exception as e:
            print(f"浏览器驱动初始化失败: {e}")
            return False
        return True
    
    def login(self):
        """登录到OKR系统"""
        try:
            print(f"正在访问: {self.base_url}")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找用户名输入框
            username_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(self.username)
            print("用户名输入完成")
            
            # 查找密码输入框
            password_input = self.driver.find_element(By.NAME, "password")
            password_input.clear()
            password_input.send_keys(self.password)
            print("密码输入完成")
            
            # 查找登录按钮并点击
            login_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '登录') or contains(text(), 'Login')]")
            login_button.click()
            print("登录按钮已点击")
            
            # 等待登录完成
            time.sleep(5)
            
            # 检查是否登录成功
            if "okr" in self.driver.current_url.lower():
                print("登录成功！")
                return True
            else:
                print("登录可能失败，请检查页面状态")
                return False
                
        except TimeoutException:
            print("页面加载超时")
            return False
        except Exception as e:
            print(f"登录过程中出现错误: {e}")
            return False
    
    def find_pbc_section(self):
        """查找PBC部分"""
        try:
            print("正在查找PBC部分...")
            
            # 尝试多种可能的PBC元素定位方式
            pbc_selectors = [
                "//span[contains(text(), 'PBC')]",
                "//div[contains(text(), 'PBC')]",
                "//a[contains(text(), 'PBC')]",
                "//li[contains(text(), 'PBC')]",
                "//button[contains(text(), 'PBC')]"
            ]
            
            pbc_element = None
            for selector in pbc_selectors:
                try:
                    pbc_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    print(f"找到PBC元素: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if pbc_element:
                pbc_element.click()
                print("PBC部分已点击")
                time.sleep(3)
                return True
            else:
                print("未找到PBC部分，请手动检查页面结构")
                return False
                
        except Exception as e:
            print(f"查找PBC部分时出现错误: {e}")
            return False
    
    def select_previous_month(self):
        """选择上一个月"""
        try:
            print("正在查找月份选择器...")
            
            # 获取当前月份
            current_month = datetime.datetime.now().month
            previous_month = current_month - 1 if current_month > 1 else 12
            
            # 月份名称映射
            month_names = {
                1: "一月", 2: "二月", 3: "三月", 4: "四月", 5: "五月", 6: "六月",
                7: "七月", 8: "八月", 9: "九月", 10: "十月", 11: "十一月", 12: "十二月"
            }
            
            previous_month_name = month_names[previous_month]
            print(f"当前月份: {month_names[current_month]}, 选择月份: {previous_month_name}")
            
            # 尝试多种月份选择器
            month_selectors = [
                f"//span[contains(text(), '{previous_month_name}')]",
                f"//div[contains(text(), '{previous_month_name}')]",
                f"//option[contains(text(), '{previous_month_name}')]",
                f"//li[contains(text(), '{previous_month_name}')]"
            ]
            
            month_element = None
            for selector in month_selectors:
                try:
                    month_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    print(f"找到月份元素: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if month_element:
                month_element.click()
                print(f"已选择月份: {previous_month_name}")
                time.sleep(2)
                return True
            else:
                print("未找到月份选择器，请手动选择")
                return False
                
        except Exception as e:
            print(f"选择月份时出现错误: {e}")
            return False
    
    def run_automation(self):
        """运行完整的自动化流程"""
        try:
            if not self.setup_driver():
                return False
            
            if not self.login():
                return False
            
            if not self.find_pbc_section():
                return False
            
            if not self.select_previous_month():
                return False
            
            print("自动化流程执行完成！")
            return True
            
        except Exception as e:
            print(f"自动化流程执行失败: {e}")
            return False
        finally:
            if self.driver:
                print("按回车键关闭浏览器...")
                input()
                self.driver.quit()
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    print("=== ZTE OKR系统自动化脚本 ===")
    print("请确保已安装Chrome浏览器和ChromeDriver")
    print()
    
    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("用户名和密码不能为空！")
        return
    
    # 创建自动化实例
    automation = ZTEOKRAutomation(username, password)
    
    try:
        # 运行自动化流程
        success = automation.run_automation()
        
        if success:
            print("所有操作已完成！")
        else:
            print("部分操作失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断了操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        automation.close()

if __name__ == "__main__":
    main()
