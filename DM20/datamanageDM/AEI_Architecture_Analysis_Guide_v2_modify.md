# AEI架构度量分析与优化指南 v3.0 

### 🔍 环境自适应检测
在开始分析前，请使用环境检测器自动适配您的系统环境，根据不同的操作系统和Shell环境，自动生成对应的执行脚本。

## 📋 Given - 前提条件

### 🎭 角色定义 (Roles)
您是一位资深架构专家，具备以下专业能力：
- 熟练掌握架构基础知识
- 精通软件开发方法论
- 擅长架构治理方法
- 拥有丰富的编码经验
- 精通DDD（领域驱动设计）
- 善于分析和解决架构度量问题
- **新增**: 具备Java版本兼容性处理经验
- **新增**: 熟悉Maven项目管理和编译流程

### 🌍 应用场景 (Scenarios)

#### 术语定义

##### AEI组件级度量
**AEI（Architecture Evaluation Index，架构评估指数）** 是一套用于评估软件架构质量的度量体系。

###### AEI度量的主要类型

**1. 结构复杂度度量**
- **组件耦合度（Coupling）**: 衡量组件间的依赖关系
- **组件内聚度（Cohesion）**: 衡量组件内部元素的相关性
- **循环依赖度（Cyclic Dependencies）**: 检测组件间的循环依赖

**2. 可维护性度量**
- **代码重复度（Code Duplication）**: 重复代码的比例
- **技术债务（Technical Debt）**: 代码质量问题累积
- **可测试性（Testability）**: 代码的可测试程度

**3. 可扩展性度量**
- **模块化程度（Modularity）**: 系统的模块化水平
- **接口稳定性（Interface Stability）**: 接口变更频率
- **组件独立性（Component Independence）**: 组件的独立部署能力

### 🎯 目标 (Purposes)
通过识别和消除架构度量弱项，进行针对性重构，提升代码的组件级架构度量得分，**最终目标达到800分**。

### 📏 准则 (Oracles)

#### 输出格式要求
- 尽量生成机器可读的Markdown文档
- 结构化组织内容
- 使用标准Markdown语法

#### 通用要求
- 按子主题组织内容
- 保持逻辑清晰
- 便于后续处理和分析

## 📥 When - 输入条件

### 🔧 变量定义
在开始执行前，请根据实际情况设置以下变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `${PROJECT_ROOT}` | 要分析的Java工程根目录 | `DM20/datamanageDM` |
| `${PROJECT_NAME}` | 项目名称 | `DataManage` |
| `${JAR_PATH}` | AEI分析工具jar包路径 | `${PROJECT_ROOT}/aei-analyzer-1.0-SNAPSHOT.jar` |
| `${WORK_DIR}` | 执行命令的工作目录 | 包含`${PROJECT_ROOT}`的父目录 |

### 输入内容 (Input)

#### 1. 代码工程
- **MVN工程**: 重点关注`${PROJECT_ROOT}/src`中的代码以及相关测试
- **测试关注原因**: 代码修改过程中需要考虑对测试的影响
- **项目结构**: 标准的Maven项目结构

#### 2. 分析工具
- **aei-analyzer-1.0-SNAPSHOT.jar**: 依赖分析的jar包
  - **位置**: 必须放在项目根目录下 `${PROJECT_ROOT}/aei-analyzer-1.0-SNAPSHOT.jar`
  - **路径示例**: `DM20/datamanageDM/aei-analyzer-1.0-SNAPSHOT.jar`
  - **⚠️ 重要变更**: 不再放在target目录，避免Maven清理时丢失
- **执行方式**: 直接使用java命令执行jar包
  - **命令格式**: `java -jar ${JAR_PATH} <分析模式> ${PROJECT_ROOT}`
  - **参数顺序**: 分析模式在前，项目路径在后
  - **⚠️ 重要**: 确保参数顺序正确，否则会执行失败

#### 3. 参考资料
- **AEI架构度量提升思路**: 相关的优化策略和方法
- **工程设计文档**: `${PROJECT_NAME}_Architecture_Design.md`

## 🔄 Then - 执行流程

### 操作步骤

#### 步骤1: 生成组件设计说明文档
**目标**: 输出组件的设计说明文档，以Markdown形式保存

**执行前准备**:
- 确认`${PROJECT_ROOT}`目录存在
- 了解项目的基本架构和业务功能

**提示词模板**:
```
请仔细分析下面的${PROJECT_NAME}工程（位于${PROJECT_ROOT}），并输出这些工程中关于每个包和类以及接口的设计文档，
并且最好能够体现他们之间的关联。输出到一个文件中，格式要求是markdown格式，
因为我需要把该工程的说明传给AI，以便AI可以更好的理解。

[根据实际项目情况描述业务功能，例如：]
该工程是一个前后端一体（有界面有后台）的服务，可以通过界面输入一些参数
（一些文件的路径，这些文件在sftp上面），然后这些参数信息会被封装成参数。
系统会启动定时任务，把相关的参数封装起来调用scala的入库服务，
scala会通过sftp上获取文件，然后加载之后存储到数据库中。
```

**输出文件**: `01_Component_Design_Documentation.md`

#### 步骤2: 执行组件度量分析 (优化版)
**目标**: 执行AEI分析工具生成组件度量结果

**执行要点**:
1. 确认jar包位置: `${PROJECT_ROOT}/aei-analyzer-1.0-SNAPSHOT.jar`
2. 确认项目路径: `${PROJECT_ROOT}`
3. 确认当前工作目录包含项目根目录

**核心命令**:
```bash
# 通用格式
java -jar ${JAR_PATH} full ${PROJECT_ROOT}

# 具体示例
java -jar DM20/datamanageDM/aei-analyzer-1.0-SNAPSHOT.jar full DM20/datamanageDM
```

**重要提示**:
- **参数顺序**: `full`(分析模式) 在前，`${PROJECT_ROOT}`(项目路径) 在后
- **路径格式**: Windows环境建议使用反斜杠 `\`
- **结果位置**: 分析结果保存在 `AEI-Result/YYYYMMDD_HHMMSS/` 目录下
- **工作目录**: 在包含`${PROJECT_ROOT}`的父目录下执行

**输出文件**: `02_AEI_Analysis_Results.md`

#### 步骤3: 分析度量结果并生成改进建议 (优化版)
**目标**: 根据组件度量分析结果得到提升AEI度量的修改建议

**结果文件位置**:
- **结果目录**: `AEI-Result/YYYYMMDD_HHMMSS/`
- **主要文件**:
  - `metrics-report.txt` - 度量报告
  - `refactoring-plan.md` - 重构计划
  - `dependency-graph.dot` - 依赖关系图

**分析提示词模板**:
```
请分析以下AEI组件度量分析结果，并生成改进建议：

1. **度量报告分析**：
   - 高耦合度组件识别和问题分析
   - 低内聚性组件识别和改进方向
   - 循环依赖检测和解决方案

2. **问题分类**：
   - 按AEI度量类型分类问题（耦合度、内聚性、循环依赖等）
   - 按严重程度排序（高、中、低优先级）
   - 按影响范围分类（包级、类级、方法级）

3. **具体建议**：
   - 指出具体的问题类和包
   - 提供具体的重构建议
   - 给出预期的改进效果

4. **实施计划**：
   - 短期目标（1-2周）
   - 中期目标（1个月）
   - 长期目标（3个月）

请将分析结果整理成结构化的Markdown文档。
```

**输出文件**: `03_AEI_Improvement_Recommendations.md`

#### 步骤4: 执行代码重构
**目标**: 加载上一步生成的需要修改的Markdown文件，识别需要修改的类进行重构

**⚠️ 重构注意事项 (基于实践经验)**:

##### 4.1 Java版本兼容性问题
**常见错误及解决方案**:

1. **Optional.isEmpty() 方法问题**
   ```java
   // ❌ 错误 (Java 11+才有)
   if (optional.isEmpty()) { ... }
   
   // ✅ 正确 (Java 8兼容)
   if (!optional.isPresent()) { ... }
   ```

2. **List.of() 方法问题**
   ```java
   // ❌ 错误 (Java 9+才有)
   return List.of();
   
   // ✅ 正确 (Java 8兼容)
   return new ArrayList<>();
   ```

##### 4.2 API适配问题
**现有类API与新接口不匹配的处理**:

1. **方法签名不匹配**
   ```java
   // 现有API可能返回void，新接口要求返回int
   // 解决方案：适配器模式
   @Override
   public int execute(String sql) {
       try {
           existingApi.execute(sql); // void方法
           return 0; // 假设成功
       } catch (Exception e) {
           return -1; // 失败
       }
   }
   ```

2. **参数类型不匹配**
   ```java
   // 现有API要求Vector，新接口使用泛型T
   // 解决方案：类型检查和转换
   @Override
   public <T> int query(String sql, T result, boolean containColHeader) {
       if (result instanceof Vector) {
           return existingApi.query(sql, (Vector) result, containColHeader);
       } else {
           log.warn("Unsupported result type: {}", result.getClass());
           return -1;
       }
   }
   ```

##### 4.3 编译验证要求
**每次重构后必须执行**:
```bash
# 使用Maven进行编译验证
mvn clean compile -q

# 如果编译失败，必须修复后再继续
# 不允许带着编译错误进行下一步
```

##### 4.4 Import语句管理
**常见import错误**:
1. **包路径错误**: 确保新创建的类的包路径正确
2. **循环import**: 避免新包之间的循环依赖
3. **未使用import**: 及时清理未使用的import语句

**输出文件**: `04_Code_Refactoring_Process.md`

#### 步骤5: 验证重构效果 (优化版)
**目标**: 重构完成后再次生成AEI度量分析结果，确认修改效果

**重构验证要点**:
1. **Maven编译验证**: `mvn clean compile`
2. **备份旧结果**: 保留重构前的分析结果
3. **重新执行AEI分析**: 使用相同命令重新分析
4. **对比结果**: 比较重构前后的度量指标

**手动验证检查清单**:
- ✅ 确认所有代码编译通过: `mvn clean compile`
- ✅ 确认jar包位置正确: `${PROJECT_ROOT}\aei-analyzer-1.0-SNAPSHOT.jar`
- ✅ 确认没有语法错误和运行时异常
- ✅ 确认重构没有破坏现有功能
- ✅ 确认新的AEI分析结果已生成

**输出文件**: `05_Post_Refactoring_Analysis.md`

#### 步骤6: 生成完整报告
**目标**: 提炼整个分析过程，输出完整的从问题分析到解决的报告

**输出文件**: `06_Complete_Analysis_Report.md`

### 执行方式
**要求**: 请一步一步按序执行，每个步骤完成后再进行下一步

## 📤 Output - 输出要求

### 输出要求 (Requirements)
1. **步骤关联**: 下一步的执行参考之前输出的内容或文件
2. **逐步执行**: 请一步一步按照**操作步骤**进行操作
3. **文件保存**: 每个步骤的结果保存到一个Markdown文件中
4. **文件命名**: 文件名要与具体的步骤和编号关联
5. **编译验证**: 每次代码修改后必须通过Maven编译验证

### 输出内容 (Output Content)
记录每个操作步骤的过程，形成完整的MD文档，方便回溯和追踪整个分析优化过程。

### 文件清单
| 步骤 | 文件名格式 | 内容描述 | 示例文件名 |
|------|------------|----------|------------|
| 1 | `01_Component_Design_Documentation.md` | 组件设计说明文档 | `01_Component_Design_Documentation.md` |
| 2 | `02_AEI_Analysis_Results.md` | AEI度量分析结果 | `02_AEI_Analysis_Results.md` |
| 3 | `03_AEI_Improvement_Recommendations.md` | 改进建议和问题分类 | `03_AEI_Improvement_Recommendations.md` |
| 4 | `04_Code_Refactoring_Process.md` | 代码重构过程记录 | `04_Code_Refactoring_Process.md` |
| 5 | `05_Post_Refactoring_Analysis.md` | 重构后验证结果 | `05_Post_Refactoring_Analysis.md` |
| 6 | `06_Complete_Analysis_Report.md` | 完整分析报告 | `06_Complete_Analysis_Report.md` |

**文件存放位置**: 建议在`${PROJECT_ROOT}`目录下创建这些文档文件

---

## 🛠️ 故障排除与最佳实践

### 重要说明：基于实践经验的改进

#### JAR包管理最佳实践
- ✅ **位置**: 项目根目录 `${PROJECT_ROOT}/aei-analyzer-1.0-SNAPSHOT.jar`
- ✅ **备份**: 建议多位置保留备份
- ✅ **版本管理**: 纳入版本控制

#### 编译验证强制要求
**每次代码修改后必须执行**: `mvn clean compile -q`

**编译错误预防清单**:
- [ ] Java版本兼容性 (避免Java 9+特性)
- [ ] API方法签名匹配
- [ ] import语句正确
- [ ] 泛型类型转换
- [ ] 异常处理逻辑

#### 常见编译错误及解决方案

**Java版本兼容性错误**:
- `Optional.isEmpty()` → `!optional.isPresent()`
- `List.of()` → `new ArrayList<>()`
- `var` → 使用具体类型

**API适配错误**:
- 方法返回类型不匹配 → 使用适配器包装
- 参数类型不匹配 → 类型检查和转换

**泛型类型错误**:
- 类型转换 → 类型检查 + 安全转换
- 泛型擦除 → 使用@SuppressWarnings注解

#### 重构策略优化

**基于实践的重构策略**:
1. **渐进式重构**: 先接口→适配器→替换调用
2. **编译驱动开发**: 每步都要编译通过
3. **向后兼容优先**: 保持现有API，通过适配器桥接

### 调试技巧
- **编译错误定位**: `mvn compile -X`
- **依赖冲突检查**: `mvn dependency:tree`

---

## 🚀 开始执行

现在可以按照上述改进的步骤开始执行AEI架构度量分析与优化流程。每个步骤都会生成对应的文档，确保整个过程可追溯和可重现。

**执行前请务必检查故障排除部分，特别是编译验证要求！**
