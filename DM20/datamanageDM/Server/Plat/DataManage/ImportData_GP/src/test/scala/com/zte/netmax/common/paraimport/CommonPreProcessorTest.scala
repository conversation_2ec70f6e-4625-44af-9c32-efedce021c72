package com.zte.netmax.common.paraimport

import com.zte.netmax.common.{ScalaTestMockSupport, datetime}
import com.zte.netmax.common.datadownload.SourceInfo
import com.zte.netmax.common.paraimport.impl.{CommonPreProcessor, HeadTailSplitter, ScanResultParser, SheetFilesMaker}
import com.zte.netmax.common.path.PathUtils

import scala.util.Success

class CommonPreProcessorTest extends ScalaTestMockSupport{
  "CommonPreProcessor" should "process correct" in {
    val sourceInfo = stub[SourceInfo]
    val sourceDownloader = mock[sourceInfo.SourceDownloader]
    val file1Time = datetime.strptime("2018-01-01 01:00:00")
    val file2Time = datetime.strptime("2018-01-02 01:00:00")
    val scanFile1 = ScanFile("file1",file1Time,"dir")
    val scanFile2 = ScanFile("file2",file2Time,"dir")
    val scanResult = ScanResult("m",sourceInfo,Seq(scanFile1,scanFile2))
    (sourceDownloader.down _).expects("dir","file1",PathUtils.join("taskDir","file1")).returns(Success({}))
    (sourceDownloader.down _).expects("dir","file2",PathUtils.join("taskDir","file2")).returns(Success({}))
    (sourceInfo.mkSourceDownloader _).when().returns(sourceDownloader)
    val sheetFilesMaker = mock[SheetFilesMaker]
    val env = Map(("key","value"))
    (sheetFilesMaker.acceptFile _).expects(PathUtils.join("taskDir","file1")).returns(true)
    (sheetFilesMaker.mkSheetFiles _).expects(PathUtils.join("taskDir","file1"),"taskDir",env).returns(Seq("file1.1","file1.2"))
    (sheetFilesMaker.acceptFile _).expects(PathUtils.join("taskDir","file2")).returns(true)
    (sheetFilesMaker.mkSheetFiles _).expects(PathUtils.join("taskDir","file2"),"taskDir",env).returns(Seq("file2.1"))
    val scanResultParser = mock[ScanResultParser]
    (scanResultParser.ignoreSheet _).expects(new org.scalamock.function.FunctionAdapter5( (_,_,_,_,_) => true)).anyNumberOfTimes().returns(false)
    (scanResultParser.parseScanFile _).expects(sourceInfo,scanFile1,Seq("file1.1","file1.2"))
      .returns(("filetype","network","omcKey","version"))
    (scanResultParser.parseScanFile _).expects(sourceInfo,scanFile2,Seq("file2.1"))
      .returns(("filetype","network","omcKey","version"))
    (scanResultParser.parseTableName _).expects("taskID",scanFile1,"file1.1","network","omcKey","version")
      .returns(("fileDate","sheetName1","tableName1"))
    (scanResultParser.parseTableName _).expects("taskID",scanFile1,"file1.2","network","omcKey","version")
      .returns(("fileDate","sheetName2","tableName2"))
    (scanResultParser.parseTableName _).expects("taskID",scanFile2,"file2.1","network","omcKey","version")
      .returns(("fileDate","sheetName3","tableName3"))
    val headTailSplitter = stub[HeadTailSplitter]
    (headTailSplitter.splitHeadTail _).when("file1.1").returns(("file1.1",Nil))
    (headTailSplitter.splitHeadTail _).when("file1.2").returns(("file1.2",Nil))
    (headTailSplitter.splitHeadTail _).when("file2.1").returns(("file2.1",Nil))

    val commonPreProcessor:PreProcessor = new CommonPreProcessor(scanResultParser,sheetFilesMaker,headTailSplitter)
    val processResult = commonPreProcessor.process("taskID","taskDir",Seq(scanResult),env)
    processResult.files.size should be (2)
    val ori1 = processResult.files(0)
    ori1.originFilename should be ("file1")
    ori1.fileType should be ("filetype")
    ori1.network should be ("network")
    ori1.omcKey should be ("omcKey")
    ori1.version should be ("version")
    ori1.failedReason should be (None)
    ori1.csvFiles.size should be (2)
    val csv1 = ori1.csvFiles(0)
    csv1.csvFilename should be ("file1.1")
    csv1.fileDate should be ("fileDate")
    csv1.sheetName should be ("sheetName1")
    csv1.tableName should be ("tableName1")
    val csv2 = ori1.csvFiles(1)
    csv2.csvFilename should be ("file1.2")
    csv2.fileDate should be ("fileDate")
    csv2.sheetName should be ("sheetName2")
    csv2.tableName should be ("tableName2")
    val ori2 = processResult.files(1)
    ori2.csvFiles.size should be (1)
    val csv3 = ori2.csvFiles(0)
    csv3.csvFilename should be ("file2.1")
    csv3.fileDate should be ("fileDate")
    csv3.sheetName should be ("sheetName3")
    csv3.tableName should be ("tableName3")
  }
}
