package com.zte.netmax.umts.datadownload.pm

import com.zte.netmax.common.datadownload._
import com.zte.netmax.common.{LogSupport, dataimport}
import com.zte.netmax.common.path.{CopyMove, PathUtils}
import com.zte.netmax.umts.datadownload.pm.arrayimpl.{PMFileDaySelector,UMTSPMUtil,KPIFromFiles}
import com.zte.netmax.umts.datadownload
import com.zte.netmax.common.datadownload.gucommon.VirtualFilename
import com.zte.netmax.common.subprocess.ProcessJob

class UMTSPMDecompressProcessJob(files:Seq[(FileInfo,Option[OriginInfo])],sourceInfo:SourceInfo,downId:String,tmpDir:String) extends BaseDecompressProcessJob(files) with LogSupport{
  protected def decompressOne(fileInfo:FileInfo,originInfo:OriginInfo):Seq[DecompressInfo] = {
    if(originInfo.locals.nonEmpty){
      val vFileName = VirtualFilename.parse(fileInfo)
      val refDir = PathUtils.join(datadownload.UMTS,UMTSPMDownloader.DIR,s"${sourceInfo.host}_${vFileName.timeStr}_$downId")
      val destDir = PathUtils.join(dataimport.DATAROOT,refDir)
      CopyMove.mkdirs(destDir)
      val dayKpiFile = s"${sourceInfo.host}_${PMFileDaySelector.getStartDayStr(vFileName.timeStr)}_DayKpiFile.csv"
      val hourKpiFile = s"${sourceInfo.host}_${PMFileDaySelector.getStartDayStr(vFileName.timeStr)}_HourKpiFile.csv"
      val files = UMTSPMUtil.unzip(originInfo.locals.map(_._2.local),tmpDir)(UMTSPMUtil.isBaseFile)
      KPIFromFiles.processFiles(files,PathUtils.join(destDir,dayKpiFile),PathUtils.join(destDir,hourKpiFile))
      val fileTime = vFileName.timeStr + "000000"
      Seq(dayKpiFile,hourKpiFile).map(x => {
        DecompressInfo(x,refDir,datadownload.DATATYPE_PM,fileTime)
      })
    }else{
      Nil
    }
  }
  override def timeout:Option[Int] = Some(UMTSPMDownloader.TIMEOUT * 60)
}

class UMTSPMDownloader extends BaseBatchDownloader{
  protected def getFreeDownList(context:DownloadContext):FreeDownList = {
    context.getFreeDownList(UMTSPMDownloader.MODEL,Some(1))
  }
  protected def getLocalPaths(sourceInfo:SourceInfo,fileInfos:Seq[FileInfo],tmpDir:String):Seq[(FileInfo,Option[OriginInfo])] = {
    val sortList = fileInfos.sortBy(_.filename)
    val scanResult:Seq[(FileInfo,Option[OriginInfo])] = sortList.map(x => (x, getPMFileList(sourceInfo,x,tmpDir)))
    val searchName:Option[String] = scanResult.reverse.find(x => x._2.exists(_.locals.nonEmpty)).map(_._1.filename)
    scanResult.map(x => {
      if(searchName.exists(_ == x._1.filename)){
        x
      }else{
        (x._1,x._2.map(y => {OriginInfo(Nil,y.net_key)}))
      }
    })
  }
  protected def getDecompressProcessJob(files:Seq[(FileInfo,DownResultOption)],sourceInfo:SourceInfo,downId:String, tmpDir: String):ProcessJob = {
    new UMTSPMDecompressProcessJob(files,sourceInfo,downId,tmpDir)
  }
//  protected def decompress(downId:String,sourceInfo:SourceInfo,fileInfo:FileInfo,originInfo:OriginInfo,tmpDir:String):DecompressResult = {
//    val vFileName = VirtualFilename.parse(fileInfo)
//    val refDir = PathUtils.join(datadownload.UMTS,UMTSPMDownloader.DIR,s"${sourceInfo.host}_${vFileName.timeStr}_$downId")
//    val destDir = PathUtils.join(dataimport.DATAROOT,refDir)
//    CopyMove.mkdirs(destDir)
//    val dayKpiFile = s"${sourceInfo.host}_${PMFileDaySelector.getStartDayStr(vFileName.timeStr)}_DayKpiFile.csv"
//    val hourKpiFile = s"${sourceInfo.host}_${PMFileDaySelector.getStartDayStr(vFileName.timeStr)}_HourKpiFile.csv"
//    val files = UMTSPMUtil.unzip(originInfo.locals.map(_._2.local),tmpDir)(UMTSPMUtil.isBaseFile)
//    KPIFromFiles.processFiles(files,PathUtils.join(destDir,dayKpiFile),PathUtils.join(destDir,hourKpiFile))
//    val fileTime = vFileName.timeStr + "000000"
//    Seq(dayKpiFile,hourKpiFile).map(x => {
//      DecompressInfo(x,refDir,datadownload.DATATYPE_PM,fileTime)
//    })
//  }

  private def getPMFileList(sourceInfo:SourceInfo,fileInfo:FileInfo,tmpDir:String): Option[OriginInfo] = {
    val files = sourceInfo.mkFileLister.list(fileInfo.path).toOption
    files.map(x => {
      val vFileName = VirtualFilename.parse(fileInfo)
      val selector = new PMFileDaySelector(vFileName.timeStr)
      val filesWithFileTime = x.map(selector.accept(UMTSPMUtil.isBaseFile)).zip(x)
      val filesHasTime = filesWithFileTime.filter(_._1.nonEmpty)
      val filters = UMTSPMUtil.filterFilesByFullOrAppend(filesHasTime)
      val locals = filters.map(f => {
        val local = PathUtils.join(tmpDir,f._2)
        (fileInfo.withFilename(f._2),LocalInfo(local,f._1.get))
      })
      OriginInfo(locals,sourceInfo.host)
    })
  }

}

object UMTSPMDownloader{
  val MODEL = "UMTSPMKPI"
  val DIR = "PMKPI"
  val TIMEOUT = 60 * 5
}

//object PMTest extends App{
//  val context = new TestDownloadContext()
//  val downloader = new UMTSPMDownloader()
//  downloader.down(context)
//}
