#!/bin/bash
#执行并校验gpssh执行结果
# Param1: host type(h,f)
# Param2: ip or file
# Param3: cmd
# Call example: gpsshResultCheck "h" "${gpMasterIP}" "${cmd}"
function gpsshResultCheck()
{
    echo "Start executing gpssh..."
    if [ $# -lt 3 ];then
        echo "Error: invalid parameters, exitting"
        return 1
    fi
    type=$1
    hosts=$2
    cmd=$3
    #dba_user="gpadmin"
    echo "Input parameter for gpssh...${1} ${2} ${3}"
    for((i=1;i<=20;++i))
       do
         #ret=`su - ${dba_user} -c "gpssh -${type} ${hosts} -v -e '${cmd}'"`
         if [ "$user" == "gpadmin" ]
         then
             ret=`gpssh -${type} ${hosts} -v -e "${cmd}" 2>&1`
         else
             ret=`su - ${dba_user} -c "gpssh -${type} ${hosts} -v -e '${cmd}' 2>&1"`
         fi
         echo "After gpssh execution, check result..."
         resultCheck=$(echo ${ret}|grep "Traceback")
         if [[ "$resultCheck" != "" ]]
         then
             if [ $i -lt 20 ];then
                 echo "Result check failed for executing gpssh, begin retry ($i)..."
                 sleep 10
             else
                 echo "ERROR: execute ${cmd} in gpssh failed, exitting..."
                 exit 1
             fi
         else
             echo "Result check for gpssh success."
             break;
         fi
    done
}

dba_user=gpadmin;
DIRNAME=`dirname $0`
current_path=`cd $DIRNAME/; pwd`
#current_path=`pwd`;
gp_path=/home/<USER>
user=`whoami`;

#修改数据库密码
#if [ -f "/home/<USER>/dm_hosts" ]
#then
#    echo "Running on TCF...checking code..."
#    CURRENT_CODE_IN_CONF=`cat conf/common_dataimport.conf | grep gp_pwd|grep -v gp_pwd_GU|head -1|awk -F'"' '{print $2}'`
#    echo "CURRENT_CODE_IN_CONF: ${CURRENT_CODE_IN_CONF}"
#    if [ ! "${CURRENT_CODE_IN_CONF}" = "1dJCcElSy14SkdLpy1eo+w==" ]
#    then
#        echo "Update code in conf/common_dataimport.conf"
#        sed -i "s/${CURRENT_CODE_IN_CONF}/1dJCcElSy14SkdLpy1eo+w==/g" conf/common_dataimport.conf
#    fi
#    CURRENT_CODE_IN_JSON=`cat ./conf/db/gpdb.json | grep password| head -1 |awk -F'"' '{print $4}'`
#    echo "CURRENT_CODE_IN_JSON: ${CURRENT_CODE_IN_JSON}"
#    if [ ! "${CURRENT_CODE_IN_JSON}" = "1dJCcElSy14SkdLpy1eo+w==" ]
#    then
#        echo "Update code in ./conf/db/gpdb.json"
#        sed -i "s/${CURRENT_CODE_IN_JSON}/1dJCcElSy14SkdLpy1eo+w==/g" ./conf/db/gpdb.json
#    fi
#fi

if [ "$user" == "gpadmin" ]
then
    #gpadmin用户启动服务
    #echo ${user};
    cd ${current_path};
    chmod +x  StartDatamanageNew.sh
    ./StartDatamanageNew.sh $1;
    if [ -f "${gp_path}/tmp_hosts_seg.cc" ]; then
        cmd="cd ${current_path}; chmod +x ${current_path}/bin/DataManage/startstandalone.sh;  ${current_path}/bin/DataManage/startstandalone.sh $1";
        gpsshResultCheck "f" "/home/<USER>/tmp_hosts_seg.cc" "${cmd}"
    elif [ -f "${gp_path}/paas_hosts_seg.cc" ]
    then
        cmd="cd ${current_path}; chmod +x ${current_path}/bin/DataManage/startstandalone.sh; ${current_path}/bin/DataManage/startstandalone.sh $1";
        gpsshResultCheck "f" "/home/<USER>/paas_hosts_seg.cc" "${cmd}"
    fi
    datapath=`cat conf/common_dataimport.conf | grep downloadpath| head -1 |awk -F"=" '{print $2}'`
    uploadpath=$datapath"/uploadfile"
    #echo "upload path: ${uploadpath}"
    if [ ! -d $uploadpath ]; then
        echo "Creating ${uploadpath}"
        mkdir $uploadpath
    fi
    touch "${uploadpath}/Empty"
else
    #非gpadmin用户启动服务，先切换为gpadmin用户在执行启动脚本
    chown -R gpadmin:gpadmin ${current_path}
    su - ${dba_user} -c "cd ${current_path}; chmod +x  StartDatamanageNew.sh; ./StartDatamanageNew.sh $1; "
    if [ -f "${gp_path}/tmp_hosts_seg.cc" ]; then
        cmd="chmod +x  ${current_path}/bin/DataManage/startstandalone.sh; ${current_path}/bin/DataManage/startstandalone.sh $1";
        gpsshResultCheck "f" "/home/<USER>/tmp_hosts_seg.cc" "${cmd}"
    fi
    if [ -f "${gp_path}/paas_hosts_seg.cc" ]
    then
        cmd="cd ${current_path}; chmod +x ${current_path}/bin/DataManage/startstandalone.sh; ${current_path}/bin/DataManage/startstandalone.sh $1";
        gpsshResultCheck "f" "/home/<USER>/paas_hosts_seg.cc" "${cmd}"
    fi
    datapath=`cat conf/common_dataimport.conf | grep downloadpath| head -1 |awk -F"=" '{print $2}'`
    uploadpath=$datapath"/uploadfile"
    #echo "upload path: ${uploadpath}"
    if [ ! -d $uploadpath ]; then
        echo "Creating ${uploadpath}"
        mkdir $uploadpath
    fi
    touch "${uploadpath}/Empty"
fi 

#清除CM队列锁
rm -rf /home/<USER>/ngi-gpdb/ext/dm/CMJobQueue/queue/*
#清除CM任务记录
/home/<USER>/ngi-gpdb/ext/dm/clearCMrecord.sh



echo "start complete!!";