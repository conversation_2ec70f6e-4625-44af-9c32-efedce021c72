#!/bin/sh

#netmax/platform/datamanage/DM20/datamanageDM
NowPath=$( cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

#SourcePath=netmax/platform/datamanage
#TargetPath=netmax/netmax/trunk
SourcePath=../..
TargetPath=../../../../netmax/trunk

#�洢��ͻ����
mkdir "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/CreateDB/NETMAX.sh" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/CreateDB/set_options.sh" "${SourcePath}/storage"
#cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/tab/tab_region.sql" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp/install_sp.sh" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/UMTS/sp/sp_pub_part.sql" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp/addfun_string_agg.sql" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp/sp_dm_add_partition_by_table.sql" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp/sp_plat_datasource.sql" "${SourcePath}/storage"
cp -rf "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp/sp_plat_vmax_datasource.sql" "${SourcePath}/storage"

#ɾ��DM���ļ�
rm -rf "${SourcePath}/DM20/DB/NETMAXDB"

mkdir -p "${SourcePath}/DM20/DB/NETMAXDB/Greenplum" 
mkdir "${SourcePath}/DM20/DB/NETMAXDB/InitData" 
mkdir "${SourcePath}/DM20/DB/NETMAXDB/SybaseIQ" 

#�����û�NetAMX-DB����
cp -rf "${TargetPath}/src/DB/Greenplum/." "${SourcePath}/DM20/DB/NETMAXDB/Greenplum" 
cp -rf "${TargetPath}/src/DB/InitData/." "${SourcePath}/DM20/DB/NETMAXDB/InitData" 
cp -rf "${TargetPath}/src/DB/SybaseIQ/." "${SourcePath}/DM20/DB/NETMAXDB/SybaseIQ" 

#�лس�ͻ����
cp -rf "${SourcePath}/storage/NETMAX.sh" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/CreateDB" 
cp -rf "${SourcePath}/storage/set_options.sh" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/CreateDB"
#cp -rf "${SourcePath}/storage/tab_region.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/tab" 
cp -rf "${SourcePath}/storage/install_sp.sh" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp" 
cp -rf "${SourcePath}/storage/sp_pub_part.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/UMTS/sp" 
cp -rf "${SourcePath}/storage/addfun_string_agg.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp" 
cp -rf "${SourcePath}/storage/sp_dm_add_partition_by_table.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp" 
cp -rf "${SourcePath}/storage/sp_plat_datasource.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp" 
cp -rf "${SourcePath}/storage/sp_plat_vmax_datasource.sql" "${SourcePath}/DM20/DB/NETMAXDB/Greenplum/schema/Plat/sp" 

#ͬ��ant�����ݹ���
cp -rf "${TargetPath}/vendor/server/ant" "${SourcePath}/DM20/datamanageDM/dockerfile/"

