[UserKPI]
ColumnInfo=StartTime,EndTime,gNBId,cellId,DLPRBAvgUseRatio,ULPRBAvgUseRatio,DLUserExperienceRate,ULUserExperienceRate,Subnetwork,ManagedElement,nrPhysicalCellDUId,gNBplmn,masterOperatorId

[ConnectUserNum]
ColumnInfo=StartTime,EndTime,gNBId,cellId,SARRCMaxConnNum,NSARRCMaxConnNum,Subnetwork,ManagedElement,gNBplmn

[PDCPkpi]
ColumnInfo=StartTime,EndTime,gNBId,cellId,DLPDCPAvgThroughput,ULPDCPAvgThroughput,Subnetwork,ManagedElement,gNBplmn

[DLHARQRetrDTXRat]
ColumnInfo=StartTime,EndTime,Subnetwork,ManagedElement,masterOperatorId,nrPhysicalCellDUId,DLDTXRatio,DLHARQretransmissionratio,DLMACBLER,DLResidualErrorPacketsNumber,ULCellBLER,DLCellBLER,ULDTXRatio,ULHARQretransmissionratio,ULMACBLER

[CellKPI]
ColumnInfo=StartTime,EndTime,Subnetwork,ManagedElement,cellId,gNBId,gNBplmn,CellAvailabilityRatio,CellInServiceDuration,CellOutServiceDurationByInterfaceTransmissionInterruption,CellOutServiceDurationByDUFailures,CellOutServiceDurationByManualOAM,CellOutServiceDurationTotal,CellOutServiceDurationByEnergySaving,CellOutServiceDurationByOther

[DUPhysicalCellQuater]
ColumnInfo=StartTime,EndTime,Subnetwork,ManagedElement,masterOperatorId,nrPhysicalCellDUId,PucchAvgInterferencePwrLevel

[CellCapacityKPIQuarter]
ColumnInfo=StartTime,EndTime,Subnetwork,ManagedElement,cellId,gNBId,gNBplmn,CellAvgActiveUEs,RRCConnectAvgActiveUEs,CellMaxActiveUEs,RRCConnectMaxActiveUEs,PdschOccupiedPRBs,PdschoAvailablePRBs,PuschOccupiedPRBs,PuschoAvailablePRBs,DLPDCPVOlume,ULPDCPVOlume
