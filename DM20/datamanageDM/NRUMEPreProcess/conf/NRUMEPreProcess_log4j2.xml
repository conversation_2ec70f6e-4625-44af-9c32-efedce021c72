<?xml version="1.0" encoding="UTF-8"?>    
<configuration status="info">    
  <appenders>    
    <Console name="Console" target="SYSTEM_OUT">    
      <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>    
      <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>    
    </Console>      
    <RollingFile name="NRUMEPreProcessFile" fileName="../log/NRUMEPreProcess.log"
                 filePattern="../log/NRUMEPreProcess.%d{yyyy-MM-dd}.log">
        <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/> 
        <TimeBasedTriggeringPolicy /> 
    </RollingFile>    
  </appenders>    
  <loggers>    
	<logger name="com.zte.cmpreprocess" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
    <logger name="com.zte.NRUMEPreProcess" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
	<logger name="com.zte.umepmpreprocess" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
	<logger name="com.zte.paramterinfo" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
	<logger name="com.zte.quartzjob" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
	<logger name="com.zte.util" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
	<logger name="org.apache.sshd" level="info" additivity="false">
        <appender-ref ref="NRUMEPreProcessFile"/>
    </logger>
    <root level="info">    
      <appender-ref ref="Console"/>    
    </root>    
  </loggers>    
</configuration> 