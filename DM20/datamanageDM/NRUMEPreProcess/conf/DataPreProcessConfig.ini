[management]
#the max count of source csv files in every processing
FileCount=500
#the max size of one file in every processing, unit: MB
FileSize=200
#the source files path, Please do not modify
ExtTablePath=/home/<USER>/gpdb/ext/dm/emsdata
#the result files path, to be loaded to database
ResultTablePath=../../data/NR/rundata/otherdata
#the cycle of scaning EMS source files, unit: min
ProcessPeriod=120	
PmProcessPeriod=30
#the Port of SFTP
SFTPPort=4222
#isPaas
isPaas=false
#sleep time, unit(s)
SleepTime=300


#tdd/fdd file merge map
#target file name=source file1,source file2
[CMExportTemplateMergeMap] 
#BWP
BWP=BWP
#CarrierDL
CarrierDL=CarrierDL
#CarrierUL
CarrierUL=CarrierUL
#CellDefiningSSB
CellDefiningSSB=CellDefiningSSB
#CQIMeasureCfg
CQIMeasureCfg=CQIMeasureCfg
#CSIIMCfg
CSIIMCfg=CSIIMCfg
#EnDCPDCP
EnDCPDCP=EnDCPDCP
#ExternalNRCellCU
ExternalNRCellCU=ExternalNRCellCU
#FrequencyBandList
FrequencyBandList=FrequencyBandList
#GNBCUCPFunction
GNBCUCPFunction=GNBCUCPFunction
#GNBCUUPFunction
GNBCUUPFunction=GNBCUUPFunction
#GNBDUFunction
GNBDUFunction=GNBDUFunction
#HARQ
HARQ=HARQ
#InterFReselection
InterFReselection=InterFReselection
#IntraFReselection
IntraFReselection=IntraFReselection
#NRCarrier
NRCarrier=NRCarrier
#NRCellDU
NRCellDU=NRCellDU
#NRCellRelation
NRCellRelation=NRCellRelation
#NRInterFMeasObject
NRInterFMeasObject=NRInterFMeasObject
#NRIntraFMeasObject
NRIntraFMeasObject=NRIntraFMeasObject
#NRPhysicalCellDU
NRPhysicalCellDU=NRPhysicalCellDU
#PBCH
PBCH=PBCH
#PDCCHConfig
PDCCHConfig=PDCCHConfig
#PDSCHConfig
PDSCHConfig=PDSCHConfig
#PDCCHAdaptive
PDCCHAdaptive=PDCCHAdaptive
#PDSCH
PDSCH=PDSCH
#PhyResourceConfigforBWPDL
PhyResourceConfigforBWPDL=PhyResourceConfigforBWPDL
#PhyResourceConfigforBWPUL
PhyResourceConfigforBWPUL=PhyResourceConfigforBWPUL
#PlmnIdList
PlmnIdList=PlmnIdList
#Qam256
Qam256=Qam256
#RA
RA=RA
#RachConfigGeneric
RachConfigGeneric=RachConfigGeneric
#SCSSpecificCarrierListUL
SCSSpecificCarrierListUL=SCSSpecificCarrierListUL
#Sctp
Sctp=Sctp
#SearchSpace
SearchSpace=SearchSpace
#SRSConfig
SRSConfig=SRSConfig
#X2ApToENB
X2ApToENB=X2ApToENB
#SCSSpecificCarrierListDL
SCSSpecificCarrierListDL=SCSSpecificCarrierListDL
#NRCellCU
NRCellCU=NRCellCU
#SubBeamInfoforNCDSSB
SubBeamInfoforNCDSSB=SubBeamInfoforNCDSSB
#EPSFallbackCtrl
EPSFallbackCtrl=EPSFallbackCtrl
#CPList
CPList=CPList
#PowerControlDL
PowerControlDL=PowerControlDL
#SubBeamInfo
SubBeamInfo=SubBeamInfo
#EPSFallbackLTEFreqMeasConfig
EPSFallbackLTEFreqMeasConfig=EPSFallbackLTEFreqMeasConfig
#MeasObjEUTRA
MeasObjEUTRA=MeasObjEUTRA
#EutranFreqRelation
EutranFreqRelation=EutranFreqRelation
#EutranFreq
EutranFreq=EutranFreq
#SSBlock
SSBlock=SSBlock
#EnDCCtrl
EnDCCtrl=EnDCCtrl
#AntConfig
AntConfig=AntConfig
#TddConfig
TddConfig=TddConfig
#RfLink
RfLink=RfLink
#IrRruTxRxGroup
IrRruTxRxGroup=IrRruTxRxGroup
#ReplaceableUnit
ReplaceableUnit=ReplaceableUnit
#BpPoolFunction
BpPoolFunction=BpPoolFunction
#AauTxRxGroup
AauTxRxGroup=AauTxRxGroup
#SectorFunction
SectorFunction=SectorFunction
#NRSectorCarrier
NRSectorCarrier=NRSectorCarrier
#RfPort
RfPort=RfPort
#PrruTxRxGroup
PrruTxRxGroup=PrruTxRxGroup
#DaauTxRxGroup
DaauTxRxGroup=DaauTxRxGroup
#EutranCellRelation
EutranCellRelation=EutranCellRelation
#ExternalEutranCellTDD
ExternalEutranCellTDD=ExternalEutranCellTDD
#ExternalEutranCellFDD
ExternalEutranCellFDD=ExternalEutranCellFDD
#PlmnInfoCU
PlmnInfoCU=PlmnInfoCU
#PlmnInfoDU
PlmnInfoDU=PlmnInfoDU
#CACU
CACU=CACU
#NRFreq
NRFreq=NRFreq
#AppService
AppService=AppService

[ITBBUPlatFileMergeConfig]
#Ip
Ip=Ip
[CMExportTemplateConfig]
BWP=ldn,ManagedElement,bwpContext,numberOfRBs,subcarrierSpacing
CarrierDL=ldn,ManagedElement,nrbandwidth,frequency,pointAfrequencyDL,SubNetwork,nrbandwidth,frequencyBandList
CarrierUL=ldn,ManagedElement,nrbandwidth,frequency,pointAfrequencyUL
CellDefiningSSB=ldn,ManagedElement,PCI,ssbFrequency,SubNetwork
CQIMeasureCfg=ldn,ManagedElement,reportQuantity
CSIIMCfg=ldn,ManagedElement,csiIMEnable
EnDCPDCP=ManagedElement,qci,rlcMode,SnSizeDl,SnSizeUl
ExternalNRCellCU=ldn,ManagedElement,cellLocalId,gNBId,moId,freqBandListDL,freqBandListUL,refNRFreq,SubNetwork,pLMNId,pLMNIdList
FrequencyBandList=ldn,ManagedElement,freqBandIndicator
GNBCUCPFunction=ManagedElement,gNBId,RrcVersion,SubNetwork,ldn,pLMNId
GNBCUUPFunction=gNBId
GNBDUFunction=ManagedElement,gNBId,moId,pLMNId,RrcVersion,SubNetwork,ldn,gNBDUName
HARQ=ldn,ManagedElement,pdschHARQACKCodebook
InterFReselection=ldn,ManagedElement,deriveSSBIndexFromCell
IntraFReselection=ldn,ManagedElement,deriveSSBIndexFromCell
NRCarrier=ldn,ManagedElement,NRCarrierId,SubNetwork,ManagedElementType,moId,nrCarrierType,refNRSectorCarrier
NRCellDU=ldn,ManagedElement,cellLocalId,configuredEpsTAC,epsTacSwch,moId,refNRPhysicalCellDU,refPlmnIdList,SubNetwork,nrPhysicalCellDUId,userLabel,masterOperatorId
NRCellRelation=ldn,ManagedElement,refExternalNRCellCU,refNRCellCU,caSupport,SubNetwork,coverType
NRInterFMeasObject=ldn,ManagedElement,deriveSSBIndexFromCell
NRIntraFMeasObject=ldn,ManagedElement,deriveSSBIndexFromCell
NRPhysicalCellDU=ldn,ManagedElement,nrPhysicalCellDUId,refNRCarrier,SubNetwork,powerPerRERef,duplexMode,nrCellScene
PBCH=ldn,ManagedElement,ssbSubcarrierSpacing,ssPBCHBlockPower,SubNetwork
PDCCHConfig=ldn,ManagedElement,cceAdaptMod
PDSCHConfig=ldn,ManagedElement,uePDSCHDmrsType
PDCCHAdaptive=ldn,ManagedElement,cceAdaptMod
PDSCH=ldn,ManagedElement,uePDSCHDmrsType
PhyResourceConfigforBWPDL=ldn,ManagedElement,refBWP
PhyResourceConfigforBWPUL=ldn,ManagedElement,refBWP
PlmnIdList=ldn,ManagedElement,PLMNId,SubNetwork
Qam256=ldn,ManagedElement,qam256EnableUl,qam256EnableDl,SubNetwork
RA=ldn,ManagedElement,msg1SubcarrierSpacing,prachRootSequenceIndex,preambleNumOfRa,restrictedSetConfig,l839,l139
RachConfigGeneric=ldn,ManagedElement,msg1FDM,msg1FrequencyStart,prachConfigIndex,zeroCorrelationZoneConfig
SCSSpecificCarrierListUL=ldn,ManagedElement,subcarrierSpacing,carrierBandwidth,SubNetwork
Sctp=assoType,localPort,moid,refIp,remoteIp,remotePort,SubNetwork,ManagedElement,ldn
SearchSpace=ldn,ManagedElement,nrofCandidatesAggregationLevel1,nrofCandidatesAggregationLevel16,nrofCandidatesAggregationLevel2,nrofCandidatesAggregationLevel4,nrofCandidatesAggregationLevel8,searchSpaceTypeList,ManagedElementType,SubNetwork
SRSConfig=ldn,ManagedElement,portNum
X2ApToENB=ManagedElement,sctpInfo,SubNetwork,ldn,moId
SCSSpecificCarrierListDL=ldn,ManagedElement,subcarrierSpacing,carrierBandwidth,SubNetwork
NRCellCU=SubNetwork,ManagedElement,ldn,moId,cellLocalId,plmnIdList,nrPhysicalCellDUId,masterOperatorId,ssbFrequency
SubBeamInfoforNCDSSB=SubNetwork,ManagedElement,ldn,subBeamIndex,subBeamPowerFator
EPSFallbackCtrl=SubNetwork,ManagedElement,ldn,epsfbSwitch,epsfbLtePriMethod,epsfbLteMethod
Ip=ldn,ManagedElement,ipAddress,SubNetwork,moId
CPList=SubNetwork,ManagedElement,ldn,powerPerRERef
PowerControlDL=SubNetwork,ManagedElement,ldn,sssOffsetRE,pssPwrOffset,pbchPwrOffset,pdschPwrOffset
SubBeamInfo=SubNetwork,ManagedElement,ldn,moId,subBeamIndex,subBeamPowerFator,azimuth,tilt,subBeamValid,beamWidthH
EPSFallbackLTEFreqMeasConfig=SubNetwork,ManagedElement,ldn,refMeasObjEUTRA,epsfbPriority,epsfbBlindPriority
MeasObjEUTRA=SubNetwork,ManagedElement,ldn,refEutranFreqRelation
EutranFreqRelation=SubNetwork,ManagedElement,ldn,refEutranFreq
EutranFreq=SubNetwork,ManagedElement,ldn,frequency,freqBand
SSBlock=SubNetwork,ManagedElement,ldn
EnDCCtrl=ldn,ManagedElement,Subnetwork,moId,asPSCellSwch
AntConfig=ldn,ManagedElement,Subnetwork,moId,dlAntNum,ulAntNum
TddConfig=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,dlULTransmissionPeriodicity1,frameType1,nrofDownlinkSymbols1,nrofUplinkSymbols1,gpNum,frameType2Present,dlULTransmissionPeriodicity2,frameType2,nrofUplinkSymbols2,nrofDownlinkSymbols2,dlCommPdcPdsMaxLen,flexibleSymbol4GP
RfLink=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refRfPort,refAntennaPort,ulAttenuation,userLabel
IrRruTxRxGroup=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refReplaceableUnit,antGroupNo,refTxRfLink,refRxRfLink,userLabel
ReplaceableUnit=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refSubRack,slotNo,name,functionMode,refReplaceableUnit,hwFunction
BpPoolFunction=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refReplaceableUnit,redundancyModeSwitch,userLabel
AauTxRxGroup=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refReplaceableUnit
SectorFunction=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refTxRfLink,refRxRfLink,refAauTxRxGroup,refIrRruTxRxGroup,refPrruTxRxGroup,refPrruTxRxGroup2,freqBand,availableSectorPower,configuredOutputPower,userLabel,refDaauTxRxGroup
NRSectorCarrier=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,userLabel,txDirection,configuredMaxTxPower,configuredChannelPowerOffset,antType8T,maximumTransmissionPower,refSectorFunction,refBpPoolFunction,rfAppMode
RfPort=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,portMode
PrruTxRxGroup=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refReplaceableUnit,antGroupNo,usedRxChannel,usedTxChannel,userLabel,referencePointLatitude,referencePointLongitude,referencePointAltitude,relativeCartesianXValue,relativeCartesianYValue,relativeCartesianZValue
DaauTxRxGroup=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refReplaceableUnit,antGroupNo,usedRxChannel,usedTxChannel,userLabel
EutranCellRelation=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refExternalEutranCellTDD,refExternalEutranCellFDD
ExternalEutranCellTDD=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,eNBId,cellLocalId,pLMNIdList
ExternalEutranCellFDD=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,eNBId,cellLocalId,pLMNIdList
PlmnInfoCU=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,plmnId
PlmnInfoDU=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,refPlmnIdList
CACU=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,caSCellBlindAddSwch,caHoWithSCellAddSwch,dssBandAdaptCASwch
NRFreq=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,ssbFrequency
AppService=ManagedElementType,SubNetwork,ManagedElement,ldn,moId,tcpOrderSwitch,dlTcpAccelerationSwitch,tcpSynTransOptSwitch,tcpFastRecoverySwitch,tcpTstpRemoveSwitch,tcpSynAckIdentifySwitch,ulTcpAccelerationSwitch,appFunctionPara1,appFunctionSwitch,draIdentifyPolicy,outerDraAddr