#!/bin/bash
# Program : start NRUMEPreProcess module

DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
MODE=${1:-"run"}

#������������ģʽ�Ȳ���run,Ҳ����debug,�˳��ű�
if [ "$MODE" != "run" ]&&[ "$MODE" != "debug" ]; then
   echo "$MODE is not valid,please input \"run\" or \"debug\""
   exit 1
fi

echo Mode=$MODE

OSNAME=`uname`
echo OS=$OSNAME

echo HOME=$HOME

#ͨ������ϵͳ���жϾ���jdkλ��
JAVA_HOME=""
JRE_HOME=""

# if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../CDT/jdk/linux/" ] ; then
   # JAVA_HOME=`cd $HOME/../../CDT/jdk/linux/; pwd`
# fi
if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../jdk/" ] ; then
   JAVA_HOME=`cd $HOME/../../jdk/; pwd`
fi

if [ $OSNAME = "SunOS" ]&&[ -d "$HOME/../../CDT/jdk/solaris/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/solaris/; pwd`
fi

if [ $OSNAME = "AIX" ]&&[ -d "$HOME/../../CDT/jdk/aix/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/aix/; pwd`
fi

#���û���ҵ�JAVA_HOME,ֱ���˳�

if [ -z $JAVA_HOME ] ; then
   echo "can not find JAVA_HOME"
   exit 1
fi

#����JRE_HOME
JRE_HOME=$JAVA_HOME/jre

echo JAVA_HOME=$JAVA_HOME
echo JRE_HOME=$JRE_HOME

#��solaris��4λϵͳ�������⴦��
if [ $OSNAME != "SunOS" ]; then
    #����Runģʽ�µ�JVM
    _RUNJAVA="$JRE_HOME/bin/java"

    #����Debugģʽ�µ�JVM
    _RUNJDB="$JAVA_HOME/bin/java"
else
    #����Runģʽ�µ�JVM
    _RUNJAVA="$JRE_HOME/bin/sparcv9/java"

    #����Debugģʽ�µ�JVM
    _RUNJDB="$JAVA_HOME/bin/sparcv9/java"
fi



#����JVM��JVM����
JAVA_OPTS="$JAVA_OPTS -Xms2048M -Xmx20480M"
if [ "$MODE" = "run" ] ; then
   JAVA=${_RUNJAVA}
else
   JAVA=${_RUNJDB}
   #JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,address=10001,server=y,suspend=n"
fi

#����javaִ��Ȩ��
chmod a+x $JAVA
echo JAVA=$JAVA

#��ȡ��װ·��
gp_path=/home/<USER>
gpConfigFile=${gp_path}/host.cfg
gpPaaSpathfile=${gp_path}/greenplumpath.cc

#����pmcm����Ŀ¼
emsDir="${HOME}/../../emsdata"
[ ! -d "$emsDir" ] && mkdir "$emsDir"
[ ! -d "$emsDir/NR/UME/cm" ] && mkdir -p "$emsDir/NR/UME/cm"
[ ! -d "$emsDir/NR/UME/pm" ] && mkdir -p "$emsDir/NR/UME/pm"
otherdatapath="$HOME/../../data/NR/rundata/otherdata"
[ ! -d "$otherdatapath" ]  && mkdir -p "$otherdatapath"
sourcedatapath="${HOME}/../sourcedata"
[ ! -d "$sourcedatapath" ] && mkdir "$sourcedatapath"

#����emsdata������·��
cmpmdatatmp=${HOME%/*}
cmpmdataPath=${cmpmdatatmp%/*}/emsdata
echo ${cmpmdataPath}
confignowpath=$(grep  "^ExtTablePath" ${HOME}/../conf/DataPreProcessConfig.ini | tail -n 1 | awk -F'=' '{ print $2 }')
if [ "${cmpmdataPath}" != "${confignowpath}" ]
then
sed -i "s|ExtTablePath=.*|ExtTablePath=${cmpmdataPath}|"     ${HOME}/../conf/DataPreProcessConfig.ini
fi


#����PID�ļ�
NRUMEPREPROCESS_PID="$HOME/NRUMEPreProcess.pid"
echo NRUMEPREPROCESS_PID=$NRUMEPREPROCESS_PID
#���ý�������ļ�
NRUMEPREPROCESS_OUT="$HOME/NRUMEPreProcess.out"

#�������Ƿ��Ѿ�����
if [ ! -z "$NRUMEPREPROCESS_PID" ]; then
    if [ -f "$NRUMEPREPROCESS_PID" ]; then
      if [ -s "$NRUMEPREPROCESS_PID" ]; then
        echo "Existing PID file found during start."
        if [ -r "$NRUMEPREPROCESS_PID" ]; then
          PID=`cat "$NRUMEPREPROCESS_PID"`
          ps -p $PID >/dev/null 2>&1
          if [ $? -eq 0 ] ; then
            echo "NRUMEPreProcess appears to still be running with PID $PID. Start aborted."
            exit 1
          else
            echo "Removing/clearing stale PID file."
            rm -f "$NRUMEPREPROCESS_PID" >/dev/null 2>&1
            if [ $? != 0 ]; then
              if [ -w "$NRUMEPREPROCESS_PID" ]; then
                cat /dev/null > "$NRUMEPREPROCESS_PID"
              else
                echo "Unable to remove or clear stale PID file. Start aborted."
                exit 1
              fi
            fi
          fi
        else
          echo "Unable to read PID file. Start aborted."
          exit 1
        fi
      else
        rm -f "$NRUMEPREPROCESS_PID" >/dev/null 2>&1
        if [ $? != 0 ]; then
          if [ ! -w "$NRUMEPREPROCESS_PID" ]; then
            echo "Unable to remove or write to empty PID file. Start aborted."
            exit 1
          fi
        fi
      fi
    fi
  fi
  
#���������־
touch "$NRUMEPREPROCESS_OUT"

#��������
cd "$HOME"
echo $HOME
chmod a+x ./NRUMEPreProcess.jar

eval LC_ALL=zh_CN \"$JAVA\" ${CLASS_PATH:-' '} ${JAVA_OPTS:-' '} -jar NRUMEPreProcess.jar >> "$NRUMEPREPROCESS_OUT" 2>&1 "&"

#��¼�����PID��
if [ ! -z "$NRUMEPREPROCESS_PID" ]; then
   echo $! > "$NRUMEPREPROCESS_PID"
fi
