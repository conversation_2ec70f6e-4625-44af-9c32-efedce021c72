#!/bin/bash
# Program : start NRUMEPreProcess module

DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
MODE=${1:-"run"}

#如果输入的运行模式既不是run,也不是debug,退出脚本
if [ "$MODE" != "run" ]&&[ "$MODE" != "debug" ]; then
   echo "$MODE is not valid,please input \"run\" or \"debug\""
   exit 1
fi

echo Mode=$MODE

OSNAME=`uname`
echo OS=$OSNAME

echo HOME=$HOME
#通过操作系统的判断决定jdk位置
JAVA_HOME=""
JRE_HOME=""

# if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../CDT/jdk/linux/" ] ; then
   # JAVA_HOME=`cd $HOME/../../CDT/jdk/linux/; pwd`
# fi
if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../jdk/" ] ; then
   JAVA_HOME=`cd $HOME/../../jdk/; pwd`
fi

if [ $OSNAME = "SunOS" ]&&[ -d "$HOME/../../CDT/jdk/solaris/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/solaris/; pwd`
fi
if [ $OSNAME = "AIX" ]&&[ -d "$HOME/../../CDT/jdk/aix/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/aix/; pwd`
fi

#如果没有找到JAVA_HOME,直接退出
if [ -z $JAVA_HOME ] ; then
   echo "can not find JAVA_HOME"
   exit 1
fi

#设置JRE_HOME
JRE_HOME=$JAVA_HOME/jre

echo JAVA_HOME=$JAVA_HOME
echo JRE_HOME=$JRE_HOME

#对solaris皿4位系统进行特殊处理
if [ $OSNAME != "SunOS" ]; then
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/java"

    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/java"
else
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/sparcv9/java"
    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/sparcv9/java"
fi

#设置JVM和JVM参数
JAVA_OPTS="$JAVA_OPTS -Xms2048M -Xmx20480M"
if [ "$MODE" = "run" ] ; then
   JAVA=${_RUNJAVA}
else
   JAVA=${_RUNJDB}
   #JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,address=10001,server=y,suspend=n"
fi

#赋予java执行权限
chmod a+x $JAVA
echo JAVA=$JAVA

#获取安装路径
gp_path=/home/<USER>
gpConfigFile=${gp_path}/host.cfg
gpPaaSpathfile=${gp_path}/greenplumpath.cc

#创建pmcm推送目录
emsDir="${HOME}/../../emsdata"
[ ! -d "$emsDir" ] && mkdir "$emsDir"
[ ! -d "$emsDir/NR/UME/cm" ] && mkdir -p "$emsDir/NR/UME/cm"
[ ! -d "$emsDir/NR/UME/pm" ] && mkdir -p "$emsDir/NR/UME/pm"
otherdatapath="$HOME/../../data/NR/rundata/otherdata"
[ ! -d "$otherdatapath" ]  && mkdir -p "$otherdatapath"
sourcedatapath="${HOME}/../sourcedata"
[ ! -d "$sourcedatapath" ] && mkdir "$sourcedatapath"

#设置emsdata的下载路径
cmpmdatatmp=${HOME%/*}
cmpmdataPath=${cmpmdatatmp%/*}/emsdata
echo ${cmpmdataPath}
confignowpath=$(grep  "^ExtTablePath" ${HOME}/../conf/DataPreProcessConfig.ini | tail -n 1 | awk -F'=' '{ print $2 }')
if [ "${cmpmdataPath}" != "${confignowpath}" ]
then
sed -i "s|ExtTablePath=.*|ExtTablePath=${cmpmdataPath}|"     ${HOME}/../conf/DataPreProcessConfig.ini
fi

#设置进程输出文件
#NRUMEPREPROCESS_OUT="$HOME/NRUMEPreProcess_CM.out"

#创建输出日志
#touch "$NRUMEPREPROCESS_OUT"

#启动程序
cd "$HOME"
echo $HOME

chmod a+x ./NRUMEPreProcess.jar
#eval LC_ALL=zh_CN \"$JAVA\" ${CLASS_PATH:-' '} ${JAVA_OPTS:-' '} -cp NRUMEPreProcess.jar com.zte.quartzjob.NRCMExportFileProcessJob >> "$NRUMEPREPROCESS_OUT" 2>&1
eval LC_ALL=zh_CN \"$JAVA\" ${CLASS_PATH:-' '} ${JAVA_OPTS:-' '} -cp NRUMEPreProcess.jar com.zte.quartzjob.NRCMExportFileProcessJob
