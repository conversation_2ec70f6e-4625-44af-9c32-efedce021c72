#!/bin/bash
# Program : shutdown NRUMEPreProcess module

#only root can run this script


DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`

NRUMEPREPROCESS_PID="$HOME/NRUMEPreProcess.pid"
echo NRUMEPREPROCESS_PID=$NRUMEPREPROCESS_PID

if [ -z "$NRUMEPREPROCESS_PID" ]; then
    echo "Shutdown failed: \$NRUMEPREPROCESS_PID not set"
else
    if [ -f "$NRUMEPREPROCESS_PID" ]; then
        PID=`cat "$NRUMEPREPROCESS_PID"`
        echo "Shutting down NRUMEPreProcess with the PID: $PID"
        kill -9 $PID
        rm -f "$NRUMEPREPROCESS_PID" >/dev/null 2>&1
        if [ $? != 0 ]; then
            echo "NRUMEPreProcess has been shut down but the PID file could not be removed."
        fi
		echo "NRUMEPreProcess has been shut down successfully."
    fi
fi
