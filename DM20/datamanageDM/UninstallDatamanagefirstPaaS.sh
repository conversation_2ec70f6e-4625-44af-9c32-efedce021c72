#!/bin/bash
#su - gpadmin 
dba_user=gpadmin;
current_path=`pwd`;
ngi_tools=/usr/local/ngi_tools



uninstalldatamanage(){
echo ${dba_user};
echo ${gp_path};
echo ${current_path};
#su - ${dba_user}
gpssh -f ${ngi_tools}/host_gp -v -e 'cd ${current_path}; chmod +x  ./StopDatamanageNew.sh; ./StopDatamanageNew.sh;   rm -rf *;'

echo "uninstall complete!!";
}

uninstall_gpdb(){
   echo ${gp_path};
   #pkill -9 -u $dba_user;  
   gpssh -f ${ngi_tools}/host_gp -v -e 'cd ${current_path}; chmod +x  ./StopDatamanageNew.sh; ./StopDatamanageNew.sh;'
   gpstop -a -M fast;
   gpssh -f ${ngi_tools}/host_gp -v -e 'cd  ${gp_path}; rm -rf *;';
}

uninstallServer(){
read -t 100 -p "Do you want to  remove the $dba_user database.[N/Y]:" CONTINUE_INSTALL
        if [ "$CONTINUE_INSTALL" == "Y" ] || [ "$CONTINUE_INSTALL" == "y" ] 
        then
            uninstall_gpdb 
        else
            uninstalldatamanage
        fi
}

uninstallServer