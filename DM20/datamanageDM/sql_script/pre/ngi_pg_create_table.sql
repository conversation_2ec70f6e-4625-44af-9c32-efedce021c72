CREATE TABLE ngi_task_table(
taskId                 text                                          NOT NULL,
taskName 			   text                                          NOT NULL,
taskStatus             text                                          NOT NULL,
creator                text,
creatTime              text,
startTime              text,
endTime                text,
strategy			   text,
taskTypeId             text                                          NOT NULL,
taskParam              text                                          NOT NULL,
PRIMARY KEY (taskid)
);

CREATE TABLE ngi_tasklog_table(
taskLogId              text                                         NOT NULL,
taskId                 text											NOT NULL,
taskLogName 		   text                                         NOT NULL,
starttime              text,
executetime            text,
result                 text,
process                text,
state                  text,
PRIMARY KEY (tasklogid)
);

CREATE TABLE task_schedule_table
(
  taskId text  not null,
  jobName text,
  jobGroup text,
  triggerName text,
  triggerGroup text,
  PRIMARY KEY (taskId)
);



create table ngi_tasktype_table
(
tasktypeid    text not null,
tasktype_en   text,
tasktype_zh   text
);


CREATE TABLE ngi_virtual_drive_tasklog_table (
	tasklogid serial,
	taskname text NOT NULL,
	PRIMARY KEY (tasklogid)
) ;

delete from ngi_tasktype_table;