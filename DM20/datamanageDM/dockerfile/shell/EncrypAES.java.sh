//bin/true; classPath=".:datamanage-newcode-1.0-SNAPSHOT.jar"
//bin/true; className=$(basename $0 .java.sh); \cp $0 ${className}.java
//bin/true; /openjdk/bin/javac -cp "$classPath" -encoding utf-8 "${className}.java" && /openjdk/jre/bin/java -cp "$classPath" $className "$@"
//bin/true; exit

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import org.apache.commons.codec.binary.Base64;
import java.util.Arrays;

public class EncrypAES {
    public static void main(String[] args) throws Exception {
        String result = EncrypAES.encryptStr(args[0]);
        System.out.println(result);
    }

    private static String encryptStr(String sourceStr) throws Exception {
        byte[] keyByteArr = Arrays.copyOf("69e44f48da8cf339dd22c579e7a73eae".getBytes("UTF-8"), 16);
        SecretKeySpec skeySpec = new SecretKeySpec(keyByteArr, "AES");
        IvParameterSpec iv = new IvParameterSpec("8d22c5w1p2ea73ea".getBytes("UTF-8"));
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
        byte[] encrypted = cipher.doFinal(sourceStr.getBytes("UTF-8"));
        return new String(Base64.encodeBase64(encrypted), "UTF-8");
    }
}