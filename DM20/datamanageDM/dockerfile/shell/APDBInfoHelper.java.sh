//bin/true; #此脚本为合法的shell脚本，同时也是合法的java类文件
//bin/true; #以//bin/true开头的都是shell语句，linux系统shell会自动将多个连续斜线去重，等价于/bin/true命令
//bin/true; #这样写的目的是为了让java编译器解析此文件时，将shell语句部分当成是注释不编译
//bin/true; #脚本原理是用shell语句调用java编译器，实时编译运行此文件，并获得输出结果
//bin/true; classPath=".:datamanage-newcode-1.0-SNAPSHOT.jar" #设置java编译和运行的依赖库路径
//bin/true; className=$(basename $0 .java.sh); \cp $0 ${className}.java #将当前文件拷贝为以.java结尾，以便进行编译
//bin/true; /openjdk/bin/javac -cp "$classPath" -encoding utf-8 "${className}.java" && /openjdk/jre/bin/java -cp "$classPath" $className $* | sed -n '/^APToken:/s/^APToken://p' #编译并运行此java程序，并对输出结果进行处理
//bin/true; exit    #shell脚本退出

import com.alibaba.fastjson.JSONPath;
import com.zte.ums.zenap.util.cipher.api.CipherException;
import com.zte.ums.zenap.util.cipher.keycenter.api.CenterCipherService;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

/**
 * 获取并解密AP数据库密码
 * 流程参考 https://i.zte.com.cn/#/shared/ee6e3a1534c6478ab944c0664639253f/wiki/page/5a3a34054bd4477f95a1bea05a370253/view
 */
@Slf4j
public class APDBInfoHelper {

    private static String msbIP = System.getenv("OPENPALETTE_MSB_IP");
    private static String msbPort = System.getenv("OPENPALETTE_MSB_PORT");
    private static String namespace = System.getenv("OPENPALETTE_NAMESPACE");

    public static void main(String[] args) {
        String token = APDBInfoHelper.getDBToken("");
        if(args.length > 0){
            token = APDBInfoHelper.getDBToken(args[0]);
        }
        System.out.format("APToken:%s%n", token);
    }
    
    /**
     * 获取AP数据库密码
     * @return
     */
    public static String getDBToken(String type) {
        String result = "";
        if(null == msbIP || null == msbPort || null == namespace){
            log.error("Either msbIP, msbPort or namespace is null");
            return result;
        }
        //获取op-proxy信息，由于AP公共服务与ngi应用不在一个租户下，必须通过op-proxy才能查询AP公共服务信息
        String opProxyServiceUrl = String.format("http://%s:%s/api/microservices/v1/services/op-proxy?namespace=%s", msbIP, msbPort, namespace);
        String opProxyJsonStr = getURLResponse(opProxyServiceUrl);
        if("".equals(opProxyJsonStr)) {
            log.error(String.format("Query op-proxy info failed, url is %s", opProxyServiceUrl));
            return result;
        }
        String opProxyIP = String.format("%s", JSONPath.read(opProxyJsonStr, "$[0].nodes[0].ip_v4"));
        /* Started by AICoder, pid:42c84x318308213147c909355001af053df38954 */
        if ("null".equals(opProxyIP)) {
            opProxyIP = String.format("%s", JSONPath.read(opProxyJsonStr, "$[0].nodes[0].ip"));
        }
        /* Ended by AICoder, pid:42c84x318308213147c909355001af053df38954 */
        String opProxyPort = String.format("%s", JSONPath.read(opProxyJsonStr, "$[0].nodes[0].port"));
        //获取AP公共服务信息
        String gpdbServiceUrl = String.format("http://%s:%s/opapi/csmworker/v1/tenants/%s/csresources?resource_name=ngi-gpdb", opProxyIP, opProxyPort, namespace);
        String gpdbJsonStr = getURLResponse(gpdbServiceUrl);
        if("".equals(gpdbJsonStr)) {
            log.error(String.format("Query ngi-gpdb info failed, url is %s", gpdbServiceUrl));
            return result;
        }
        if("GPIP".equals(type)){
            return String.format("%s", JSONPath.read(gpdbJsonStr, "$.resources[0].cs_resource[?(@.name='OPENPALETTE_GP_ADDRESS')][0].value"));
        }
        //获取secretkey_id和加密密码
        String secretkey_id = String.format("%s", JSONPath.read(gpdbJsonStr, "$.resources[0].secretkey_id"));
        String cipherToken = String.format("%s", JSONPath.read(gpdbJsonStr, "$.resources[0].cs_resource[?(@.name='OPENPALETTE_GP_PASSWORD')][0].value"));
        //调用zenap提供的解密接口，解密密码
        try {
            result = CenterCipherService.getInstance().decrypt(secretkey_id, cipherToken);
        } catch (CipherException e) {
            log.error(String.format("decrypt error, secretkey_id=%s, cipherToken=%s", secretkey_id, cipherToken), e);
        }
        return result;
    }

    /**
     * 使用GET方法获取url请求返回的内容
     * @param url
     * @return http response content
     */
    private static String getURLResponse(String url) {
        String response = "";
        InputStream connInputStream = null;
        try {
            URL urlObj = new URL(url);
            URLConnection urlConn = urlObj.openConnection();
            HttpURLConnection httpUrlConn = (urlConn instanceof HttpURLConnection) ? (HttpURLConnection)urlConn : null;
            if(null == httpUrlConn){
                return response;
            }
            httpUrlConn.setRequestMethod("GET");
            httpUrlConn.setRequestProperty("Accept", "application/json");
            int retCode = httpUrlConn.getResponseCode();
            if(retCode != 200) {
                log.error("ResponseCode: "+retCode);
                return response;
            }
            connInputStream = httpUrlConn.getInputStream();
            response = getStringFromInputStream(connInputStream);
        } catch (IOException e) {
            log.error("Exception occurs: ", e);
        } finally {
            if(connInputStream != null) {
                try {
                    connInputStream.close();
                } catch (IOException ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
        }
        return response;
    }

    /**
     * 读取输入流，输出字符串
     * @param inputStream
     * @return stream content string
     */
    private static String getStringFromInputStream(InputStream inputStream) {
        String result = "";
        if(null == inputStream) {
            return result;
        }
        try(BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            StringBuilder sb = new StringBuilder();
            while((line = br.readLine()) != null){
                sb.append(line);
            }
            result = sb.toString();
        } catch (IOException e) {
            log.error("getStringFromInputStream exception: ", e);
        }
        return result;
    }
}
