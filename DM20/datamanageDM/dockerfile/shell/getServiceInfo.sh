#!/bin/sh

function ShowUsage()
{
    echo "Usage:"
    echo "  ./$(basename $0) serviceName"
    echo "Example:"
    echo "  ./$(basename $0) ngi-dm-web"
}

if [ $# -eq 0 ];then
    ShowUsage
    exit
fi

serviceName=$1

# 常见服务名
# greenplum-master-srv-ngi
# ngi-dm-web-28001
# ngi-dm-service-28001
# ngi-dm-web
# ngi-dm-sftp
# ngi-netmax-server
# ngi-common-service-ms
# ngi-novisualization
# user-inner
busybox wget -q -O - --header 'Accept: application/json' "http://$OPENPALETTE_MSB_IP:$OPENPALETTE_MSB_PORT/api/microservices/v1/services/$serviceName/version/v1?namespace=ranoss" | grep ^

# 列出所有的服务
# busybox wget -qO - --header 'Accept: application/json' http://$OPENPALETTE_MSB_IP:$OPENPALETTE_MSB_PORT/api/msdiscover/v1/services?namespace=default | grep ^ | sed 's/,/,\n/g;s/\[/\[\n/g;s/\]/\n]/g;s/{/{\n/g;s/}/\n}/g'
# busybox wget -qO - --header 'Accept: application/json' http://$OPENPALETTE_MSB_IP:$OPENPALETTE_MSB_PORT/api/msdiscover/v1/services?namespace=ranoss | grep ^ | sed 's/,/,\n/g;s/\[/\[\n/g;s/\]/\n]/g;s/{/{\n/g;s/}/\n}/g'
# 或
# busybox wget -qO - --header 'Accept: application/json' http://$OPENPALETTE_MSB_IP:$OPENPALETTE_MSB_PORT/api/microservices/v1/services?namespace=default | grep ^ | sed 's/,/,\n/g;s/\[/\[\n/g;s/\]/\n]/g;s/{/{\n/g;s/}/\n}/g'
# busybox wget -qO - --header 'Accept: application/json' http://$OPENPALETTE_MSB_IP:$OPENPALETTE_MSB_PORT/api/microservices/v1/services?namespace=ranoss | grep ^ | sed 's/,/,\n/g;s/\[/\[\n/g;s/\]/\n]/g;s/{/{\n/g;s/}/\n}/g'
