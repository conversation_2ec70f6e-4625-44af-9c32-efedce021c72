<project name="datamanage" basedir=".">
    <scp file="datamanager-updatePaas.zip"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <scp file="upgradedm.sh"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <scp file="coordinator.sh"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <sshexec host="GPIP_PLACEHOLDER" username="gpadmin"
        keyfile="/home/<USER>/getkey.result"
        port="22"
        trust="true"
        command="chmod +x *.sh;./coordinator.sh upgrade"/>
</project>
