<project name="datamanage"  basedir=".">
    <sql driver="org.postgresql.Driver"
        url="**************************************************************"
        userid="netmax"
        DENSEHORSE="GPPWD_CURRENT"
        output="/home/<USER>/getConfigPassword.result"
        print="yes"
        showheaders="false"
        showtrailers="false">
        <transaction>
            DROP EXTERNAL TABLE if exists ext_getConfigPassword;
        </transaction>
        <transaction>
            CREATE EXTERNAL WEB TABLE ext_getConfigPassword(cipherPwd text) EXECUTE '[ -f /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf ] &amp;&amp; sed -nr ''/gp_pwd/s|^.*gp_pwd="(.*)".*$|\1|p'' /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf; [ -f /home/<USER>/ngi-gpdb/ext/dm/conf/db/gpdb.json ] &amp;&amp; sed -nr ''/password/s|^.*password":[ ]*"(.*)".*$|\1|p'' /home/<USER>/ngi-gpdb/ext/dm/conf/db/gpdb.json; true'
            ON HOST
            FORMAT 'TEXT';
        </transaction>
        <transaction>
            SELECT distinct cipherPwd FROM ext_getConfigPassword;
        </transaction>
        <classpath>
            <pathelement location="/home/<USER>/ant/lib/postgresql-9.3-1102.jdbc4.jar"/>
        </classpath>
    </sql>
</project>
