if [ $1 == "deploy" ];then
    ./deploydm.sh >/home/<USER>/dmInstall_$(date +"%F_%T").log 2>&1
elif [ $1 == "deployNetMAX" ];then
    ./deployNetMAX.sh >/home/<USER>/NetMAXInstall_$(date +"%F_%T").log 2>&1
elif [ $1 == "upgrade" ];then
    ./upgradedm.sh >/home/<USER>/dmUpgrade_$(date +"%F_%T").log 2>&1
elif [ $1 == "upgradeNetMAX" ];then
    if [ -f /home/<USER>/ngi-gpdb/ext/NetMAX/Server/conf/VersionInfoCfg.xml ];then
        ./upgradeNetMAX.sh >/home/<USER>/NetMAXUpgrade_$(date +"%F_%T").log 2>&1
    else
        ./deployNetMAX.sh >/home/<USER>/NetMAXInstall_$(date +"%F_%T").log 2>&1
    fi
fi