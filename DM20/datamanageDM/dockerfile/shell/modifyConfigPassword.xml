<project name="datamanage"  basedir=".">
    <sql driver="org.postgresql.Driver"
        url="**************************************************************"
        userid="netmax"
        DENSEHORSE="GPPWD_CURRENT"
        output="/home/<USER>/modifyConfigPassword.result"
        print="yes"
        showheaders="false"
        showtrailers="false">
        <transaction>
            DROP EXTERNAL TABLE if exists ext_modifyConfigPassword;
        </transaction>
        <transaction>
            CREATE EXTERNAL WEB TABLE ext_modifyConfigPassword(cipherPwdModify text) EXECUTE 'echo "GPPWD_NEW_CIPHER" >/home/<USER>/.tcfAPToken; [ -f /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf ] &amp;&amp; sed -i -r ''/gp_pwd/s|^(.*gp_pwd=").*(".*)$|\1GPPWD_NEW_CIPHER\2|'' /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf; [ -f /home/<USER>/ngi-gpdb/ext/dm/conf/db/gpdb.json ] &amp;&amp; sed -i -r ''/password/s|^(.*password":[ ]*").*(".*)$|\1GPPWD_NEW_CIPHER\2|'' /home/<USER>/ngi-gpdb/ext/dm/conf/db/gpdb.json; echo "GPPWD_NEW_CIPHER"'
            ON HOST
            FORMAT 'TEXT';
        </transaction>
        <transaction>
            SELECT * FROM ext_modifyConfigPassword;
        </transaction>
        <classpath>
            <pathelement location="/home/<USER>/ant/lib/postgresql-9.3-1102.jdbc4.jar"/>
        </classpath>
    </sql>
</project>
