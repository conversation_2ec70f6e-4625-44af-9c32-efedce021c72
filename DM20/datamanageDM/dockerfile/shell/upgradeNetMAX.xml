<project name="datamanage" basedir=".">
    <scp file="ZTE_UniPOS_NetMAX_Server.zip"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <scp file="upgradeNetMAX.sh"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <scp file="deployNetMAX.sh"
        todir="gpadmin@GPIP_PLACEHOLDER:/home/<USER>/"
        keyfile="/home/<USER>/getkey.result"
        trust="true"/>
    <sshexec host="GPIP_PLACEHOLDER" username="gpadmin"
        keyfile="/home/<USER>/getkey.result"
        port="22"
        trust="true"
        command="chmod +x *.sh;./coordinator.sh upgradeNetMAX"/>
</project>
