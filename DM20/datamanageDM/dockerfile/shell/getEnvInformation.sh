#!/bin/bash

if [ $USER != root ];then
    echo "ERROR: Must root to run this script!"
    exit 1
fi

echo ">>> Database cluster information:"
gpMaster=$(kubectl get nodes -l 'RAN_gp_master_cluster1=true' -o custom-columns=NAME:.metadata.name | tail -n 1)
kubectl get nodes -l 'RAN_gp_segment_cluster1=true' | sed -e "/$gpMaster/{s/<none>/Master/;b}" -e 's/<none>/Slave /'

echo ""
echo ">>> NGI pod information:"
insNum=$(kubectl -n ranoss get pod | grep ngi-dm-service | grep -oP '(?<=ngi-dm-service-datamanage-ms-)[0-9]*(?=-)')
kubectl -n ranoss get pods -o wide -l "name in ($(echo echo {ngi-common-service-ngi-common-service,ngi-dm-service-datamanage,ngi-gis3d-mapserver-mapserver,ngi-novisualization-ngi-novisualization}-ms-$insNum | sed 's/ /,/g'))"

ngiPodName=$(kubectl -n ranoss get pods -l "name in ($(echo ngi-common-service-ngi-common-service-ms-$insNum | sed 's/ /,/g'))" -o custom-columns=NAME:.metadata.name | tail -n 1)
ngiPodNode=$(kubectl -n ranoss get pods -l "name in ($(echo ngi-common-service-ngi-common-service-ms-$insNum | sed 's/ /,/g'))" -o custom-columns=NODE:.status.hostIP | tail -n 1)
dmPodName=$(kubectl -n ranoss get pods -l "name in ($(echo ngi-dm-service-datamanage-ms-$insNum | sed 's/ /,/g'))" -o custom-columns=NAME:.metadata.name | tail -n 1)
dmPodNode=$(kubectl -n ranoss get pods -l "name in ($(echo ngi-dm-service-datamanage-ms-$insNum | sed 's/ /,/g'))" -o custom-columns=NODE:.status.hostIP | tail -n 1)

echo ""
echo ">>> NGI log path:"
echo "$ngiPodNode:/paasdata/op-tenant/ranoss/otcp/logs/ngi/ngi-common-service/$ngiPodName"

echo ""
echo ">>> Useful command:"
echo "kubectl -n ranoss exec -it $ngiPodName -- sh"
echo "kubectl -n ranoss exec -it $dmPodName -- sh"
echo "kubectl cp ranoss/${dmPodName}:getEnvInformation.sh getEnvInformation.sh"

echo ""