#!/bin/bash

source /home/<USER>/.bash_profile
MSBIP="MSBIP_PLACEHOLDER"
#MSB IP
MSBPORT="MSBPORT_PLACEHOLDER"

#GP数据库master的端口
gpMasterIP=$(head -n 1 /home/<USER>/paas_hosts_master.cc)
greenplumpath=$(head -n 1 /home/<USER>/greenplumpath.cc)
echo "gpMasterIP: $gpMasterIP, greenplumpath: $greenplumpath"


#校验gpssh执行结果
# Param1: host type "f" or "h"
# Param2: host file or host ip
# Param3: cmd
function gpsshResultCheck()
{
    echo "Start executing gpssh..."
    if [ $# -lt 3 ];then
        echo "Error: invalid parameters, exitting"
        return 1
    fi
    type=$1
    hosts=$2
    cmd=$3
    #dba_user="gpadmin"
    echo "Input parameter for gpssh...${1} ${2} ${3}"
    for((i=1;i<=20;++i))
       do
         ret=`gpssh -${type} ${hosts} -v -e "${cmd}" 2>&1`
         echo "${ret}"
         echo "After gpssh execution, check result..."
         resultCheck=$(echo ${ret}|grep -E "Traceback|could not synchronize with original prompt")
         if [[ "$resultCheck" != "" ]]
         then
             if [ $i -lt 20 ];then
                 echo "Result check failed for executing gpssh, begin retry ($i)..."
                 sleep 10
             else
                 echo "ERROR: execute ${cmd} in gpssh failed, exitting..."
                 exit 1
             fi
         else
             echo "Result check for gpssh success."
             break;
         fi
    done

}

#校验gpscp执行结果
# Param1: hostsfile or hostslist(start with "," eg. ",************,************,************")
# Param2: filepath on remote
# Param3: file md5
# Param4: user
# Call example: gpscpResultCheck F/home/<USER>/dm_hosts /home/<USER>/gp_OSCheck.sh b28651c797d044f2523343235481efd5 gpadmin
function gpscpResultCheck()
{
    if [ $# -lt 4 ];then
        return 1
    fi
    hostParam=$1
    remoteFilePath=$2
    md5Check=$3
    user=$4

    declare -i hostNum=9999
    if [ "${hostParam:0:1}" == "," ];then
        hostNum=$(awk 'BEGIN{FS=","}{print NF-1}' <<<"$hostParam")
        hostsOption=${hostParam//,/ -h }
        cmd="gpssh $hostsOption \"md5sum '$remoteFilePath'\""
    else
        hostNum=$(awk 'BEGIN{hostsLine=""}{hostsLine=hostsLine" "$1}END{print hostsLine}' "$hostParam" | awk '{print NF}')
        cmd="gpssh -f '$hostParam' \"md5sum '$remoteFilePath'\""
    fi
    echo "Check command:  $cmd"
    echo "Expected MD5 is $md5Check"

    declare -i targetMd5Count=0
    if [ "$user" == "gpadmin" ];then
        targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
    else
        targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
    fi
    echo "Check result: $targetMd5Count/$hostNum"

    if [ $targetMd5Count -ge $hostNum ];then
        return 0
    else
        return 1
    fi
}

#NetMAX安装包
netmax_package=ZTE_UniPOS_NetMAX_Server.zip

#分发NetMAX安装包  
netmaxUpgrade()
{
    echo "****** Build SSH: scp ${netmax_package}"  
    #分发NetMAX的压缩包
    for((i=1;i<=20;++i))
    do
      gpscp -f /home/<USER>/dm_hosts /home/<USER>/${netmax_package} =:/home/<USER>/ngi-gpdb/ext/${netmax_package}
      echo "Check result..."
      md5=$(md5sum /home/<USER>/${netmax_package} | awk '{print $1}')
      gpscpResultCheck "/home/<USER>/dm_hosts" "/home/<USER>/ngi-gpdb/ext/${netmax_package}" "$md5" "gpadmin"
      ret=$?
      if [ $ret -eq 0 ];then
         echo "Result check success."
		 rm -vf /home/<USER>/${netmax_package}
         break
      elif [ $i -lt 20 ];then
         echo "Result check failed, begin retry ($i)..."
         sleep 10
      else
         echo "ERROR: Copy /home/<USER>/${netmax_package} to other hosts failed, exiting..."
         exit 1
      fi
    done
	
    #解压NetMAX安装包
    echo "****** Build SSH: unzip ${netmax_package}"
    netmaxdirlink="/home/<USER>/ngi-gpdb/ext/NetMAX"
    netmaxdir="${greenplumpath}/NetMAX"
    netmaxpackagedir="${greenplumpath}/UpgradeNetMAX"
    
    cmd="rm -rf $netmaxpackagedir; mkdir -p $netmaxpackagedir; unzip -oq /home/<USER>/ngi-gpdb/ext/${netmax_package} -d $netmaxpackagedir; chmod 755 $netmaxpackagedir -R; chmod +x $netmaxpackagedir/UpgradeNetMAX.sh; echo 'Unzip package complete'"
    gpsshResultCheck "f" "/home/<USER>/dm_hosts" "${cmd}"
    
    cmd="cd $netmaxpackagedir; ./UpgradeNetMAX.sh; chmod +x $netmaxdirlink/Server/bin/*.sh; rm -rf $netmaxpackagedir; rm -vf /home/<USER>/ngi-gpdb/ext/${netmax_package}; echo 'Upgrade NetMAX complete'"    
    gpsshResultCheck "f" "/home/<USER>/dm_hosts" "${cmd}"
	
    versionFileMd5=`md5sum $netmaxdirlink/Server/conf/VersionInfoCfg.xml`
	gpscpResultCheck "/home/<USER>/dm_hosts" "$netmaxdirlink/Server/conf/VersionInfoCfg.xml" "$versionFileMd5" "gpadmin"
	echo '****NetMAX Upgrade OK'
}
netmaxUpgrade

# tcf环境使用AP公共服务最新密码
if [ -s /home/<USER>/.tcfAPToken ];then
    echo "Found /home/<USER>/.tcfAPToken file, begin to sync cipher DB password in config file..."
    psql -d netmaxdb -c 'select * from ext_modifyConfigPassword'
fi

# 保证没有777权限
chmod -R g-w,o-w /home/<USER>/ngi-gpdb/ext/NetMAX/

#启动数据管理
netmaxStart(){ 
    echo '**** NetMAXInstance  start***'
    cmd="cd /home/<USER>/ngi-gpdb/ext/NetMAX/Server/bin; chmod u+x ./startServer.sh; ./startServer.sh"

    gpsshResultCheck "f" "/home/<USER>/dm_hosts" "${cmd}"
    echo '**** NetMAXInstance  start OK'
}
netmaxStart
