#!/bin/bash

#输入为1个参数，0表示安装，1表示升级
if [ $# != 1 ] 
then 
    echo "Please check input param number."   
    exit 1
fi 

#用户名默认为gpadmin
user="gpadmin"

#SSD磁盘路径配置文件(存放热表路径)
ssdVolumePathFile="/home/<USER>/SSDVolumePath.cfg"

#SAS磁盘路径配置文件(存放温表路径)
sasVolumePathfile="/home/<USER>/greenplumpath_origin.cc"

#gpadmin家目录
homeDir="/home/<USER>/"

#热表空间路径
hotTableSpacePath=""

#温表空间路径
warmTableSpacePath=""

#存放所有NR导入表的JAR包路径
nrImportTableJarPath="/home/<USER>/ngi-gpdb/ext/dm/bin/DataManage/ImportData_5G.jar"

#存放所有LTE导入表的JAR包路径
lteImportTableJarPath="/home/<USER>/ngi-gpdb/ext/dm/bin/DataManage/ImportData_LTE.jar"

#存放所有NR导入表的XML文件名称
nrXmlFileName="g5file2sp.xml"

#存放所有LTE导入表的XML文件名称
lteXmlFileName="ltefile2sp.xml"

# 校验gpscp执行结果
# Param1: hostsfile or hostslist(start with "," eg. ",************,************,************")
# Param2: filepath on remote
# Param3: file md5
# Param4: user
# Call example: gpscpResultCheck /home/<USER>/dm_hosts /home/<USER>/gp_OSCheck.sh b28651c797d044f2523343235481efd5 gpadmin
function gpscpResultCheck()
{
    if [ $# -lt 4 ];then
        return 1
    fi
    hostParam=$1
    remoteFilePath=$2
    md5Check=$3
    user=$4

    declare -i hostNum=9999
    if [ "${hostParam:0:1}" == "," ];then
        hostNum=$(awk 'BEGIN{FS=","}{print NF-1}' <<<"$hostParam")
        hostsOption=${hostParam//,/ -h }
        cmd="gpssh $hostsOption \"md5sum '$remoteFilePath'\""
    else
        hostNum=$(awk 'BEGIN{hostsLine=""}{hostsLine=hostsLine" "$1}END{print hostsLine}' "$hostParam" | awk '{print NF}')
        cmd="gpssh -f '$hostParam' \"md5sum '$remoteFilePath'\""
    fi
    echo "Check command:  $cmd"
    echo "Expected MD5 is $md5Check"

    declare -i targetMd5Count=0
    if [ "$user" == "gpadmin" ];then
        targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
    else
        targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
    fi
    echo "Check result: $targetMd5Count/$hostNum"

    if [ $targetMd5Count -ge $hostNum ];then
        return 0
    else
        return 1
    fi
}

#校验gpssh执行结果
# Param1: host type "f" or "h"
# Param2: host file or host ip
# Param3: cmd
function GPSSHResultCheck()
{
    echo "Start executing gpssh."
    if [ $# -lt 3 ]
    then
        echo "Please check input param number."
        return 1
    fi
    type=$1
    hosts=$2
    cmd=$3 
    echo "GPSSH cmd: gpssh -${type} ${hosts} -v -e '${cmd}' 2>&1"
    for((i=1;i<=20;++i))
       do       
         ret=`gpssh -${type} ${hosts} -v -e "${cmd}" 2>&1`         
         echo "${ret}"
         echo "After gpssh execution, check result."
         resultCheck=$(echo "${ret}"|grep "Traceback")           
         if [[ "$resultCheck" != "" ]]
         then
             if [ $i -lt 20 ]
             then
                 echo "Result check failed for executing gpssh, begin retry ($i)."
                 sleep 10
             else
                 echo "Execute ${cmd} in gpssh failed, exitting."
                 exit 1
             fi
         else
             echo "Result check for gpssh success."
             break
         fi
    done
}

#执行安装过程
InstallProcess(){
   if [[ -f $ssdVolumePathFile ]] && [[ -f $sasVolumePathfile ]] 
   then
      hotTableSpacePath=($(awk '{print $1}' ${ssdVolumePathFile}))
      warmTableSpacePath=($(awk '{print $1}' ${sasVolumePathfile}))
      echo "Hot table space path:  $hotTableSpacePath"
      echo "Warm table space path:  $warmTableSpacePath"
      if [[ $hotTableSpacePath ]] && [[ $warmTableSpacePath ]] 
      then   
        hotTableSpacePath=$hotTableSpacePath/hotdataspace
        warmTableSpacePath=$warmTableSpacePath/warmdataspace
        cat /home/<USER>/paas_hosts_master.cc /home/<USER>/paas_hosts_seg.cc >hosts_gp 2>/dev/null
        gpIPFile="hosts_gp"
        #在所有GP节点上创建表空间的挂载路径
        CreateTableSpaceDirPath
        #创建热表空间和温表空间
        CreateTableSpace
        #设置热表空间为默认表空间
        SetDefaultTableSpace
        #修改所有导入的NR表的默认表空间
        SetImportTableTableSpace $nrImportTableJarPath $nrXmlFileName
        #修改所有导入的LTE表的默认表空间
        SetImportTableTableSpace $lteImportTableJarPath $lteXmlFileName      
      fi
   fi      
}
 
# 检查所有GP节点配置的SSD路径有效性
function CheckSSDCondition()
{
    if [ -f SSDVolumePath.cfg ];then
        ssdPath=$(head -n 1 SSDVolumePath.cfg)
        echo "SSD Path: $ssdPath"
        cat /home/<USER>/paas_hosts_master.cc /home/<USER>/paas_hosts_seg.cc >hosts_gp 2>/dev/null
        # 检测ssdPath目录在所有节点上是否存在，且是挂载点目录
        echo "Begin to check if path '$ssdPath' exist on all GP hosts and is a mount point..."
        for((i=0; i<20; ++i))
        do
            retMsg=$(gpssh -f hosts_gp "mountpoint $ssdPath" 2>&1)
            retCode=$?
            gpsshErrorMsg=$(echo "$retMsg" | grep -E "Traceback|could not synchronize with original prompt")
            if [ "$gpsshErrorMsg" ];then
                echo "gpssh execute failed, begin to retry($((i+1)))..."
                sleep 5
                continue
            fi
            # 只有当返回码为0时，表示所有节点的ssdPath目录都是挂载点目录；否则至少有一个节点不满足，输出返回信息
            if [ $retCode -eq 0 ];then
                ssdCondition="ok"
                echo "$retMsg"
                echo "Check OK!"
            else
                ssdCondition="fail"
                echo "Check fail, see the following detail infomation:"
                echo "$retMsg"
            fi
            break
        done

        if [ "$ssdCondition" == "ok" ];then
            # 重置结果
            ssdCondition="fail"
            # 尝试用sudo修改ssdPath目录所有者为gpadmin(cPaaS环境可用)
            cmd="sudo -n chown gpadmin:gpadmin $ssdPath"
            GPSSHResultCheck "f" hosts_gp "$cmd"
            # 升级需要检测ssdPath目录对gpadmin用户具有操作权限
            echo "Begin to check if path '$ssdPath' have enough permission on all GP hosts..."
            for((i=0; i<20; ++i))
            do
                retMsg=$(gpssh -f hosts_gp "ls -ld $ssdPath && test -d $ssdPath && test -r $ssdPath && test -w $ssdPath && test -x $ssdPath" 2>&1)
                retCode=$?
                gpsshErrorMsg=$(echo "$retMsg" | grep -E "Traceback|could not synchronize with original prompt")
                if [ "$gpsshErrorMsg" ];then
                    echo "gpssh execute failed, begin to retry($((i+1)))..."
                    sleep 5
                    continue
                fi
                # 只有当返回码为0时，表示所有节点的ssdPath目录权限都没问题；否则至少有一个节点不满足，输出返回信息
                if [ $retCode -eq 0 ];then
                    ssdCondition="ok"
                    echo "$retMsg"
                    echo "Check OK!"
                else
                    ssdCondition="fail"
                    echo "Check fail, see the following detail infomation:"
                    echo "$retMsg"
                fi
                break
            done
        fi
        
        if [ "$ssdCondition" == "ok" ];then
            # 重置结果
            ssdCondition="fail"
            # 检测ssdPath挂载点在所有节点上的可用空间
            echo "Begin to check if path '$ssdPath' have enough avaliable space size on all GP hosts..."
            for((i=0; i<20; ++i))
            do
                retMsg=$(gpssh -f hosts_gp "df -h $ssdPath | tail -n 1 | awk '{print \$4}'" 2>&1)
                gpsshErrorMsg=$(echo "$retMsg" | grep -E "Traceback|could not synchronize with original prompt")
                if [ "$gpsshErrorMsg" ];then
                    echo "gpssh execute failed, begin to retry($((i+1)))..."
                    sleep 5
                    continue
                fi
                # 找ssd磁盘可用空间最小的一个
                minSize=$(echo "$retMsg" | cat -v | grep -oP '(?<=] ).*(?=\^M)' | sort -h | head -n 1)
                checkWith3T=$(echo -e "${minSize}\n3.0T" | sort -h | head -n 1)
                if [ "$checkWith3T" == "3.0T" ];then
                    ssdCondition="ok"
                    echo "$retMsg"
                    echo "Check OK"
                else
                    ssdCondition="fail"
                    echo "Some SSD avaliable size < 3T, see the following detail infomation:"
                    echo "$retMsg"
                fi
                break
            done
        fi
    else
        echo "File SSDVolumePath.cfg dose not exists, skip..."
    fi
} 

#to do 执行升级过程
function UpdateProcess()
{
    netmaxdbTablespace=$(psql -A -t -c "select spcname from pg_database a left join pg_tablespace b on a.dattablespace=b.oid where datname='netmaxdb'")
    echo "Current netmaxdb default tablespace is $netmaxdbTablespace"
    if [ "$netmaxdbTablespace" == "pg_default" ];then
        # 未执行过表分区操作
        ssdCondition="fail"
        CheckSSDCondition
        if [ "$ssdCondition" == "ok" ];then
            # 向各个GP节点拷贝SSDVolumePath.cfg文件
            cat /home/<USER>/paas_hosts_master.cc /home/<USER>/paas_hosts_seg.cc >hosts_gp 2>/dev/null
            echo "Begin to copy SSDVolumePath.cfg to all GP hosts..."
            for((i=1;i<=20;++i))
            do
              gpscp -f hosts_gp SSDVolumePath.cfg =:/home/<USER>/SSDVolumePath.cfg
              echo "Check result..."
              md5=$(md5sum SSDVolumePath.cfg | awk '{print $1}')
              gpscpResultCheck "hosts_gp" "/home/<USER>/SSDVolumePath.cfg" "$md5" "gpadmin"
              ret=$?
              if [ $ret -eq 0 ];then
                 echo "Result check success."
                 break;
              elif [ $i -lt 20 ];then
                 echo "Result check failed, begin retry ($i)..."
                 sleep 10
              else
                 echo "ERROR: Copy SSDVolumePath.cfg to all GP hosts failed, exiting..."
                 exit 1
              fi
            done
            
            ssdPath=$(head -n 1 SSDVolumePath.cfg)
            hotTableSpacePath=$ssdPath/hotdataspace
            warmTableSpacePath=$(cat /home/<USER>/greenplumpath.cc)/warmdataspace
            gpIPFile=hosts_gp
            CreateTableSpaceDirPath
            CreateTableSpace
            SetImportTableTableSpace $nrImportTableJarPath $nrXmlFileName
            SetImportTableTableSpace $lteImportTableJarPath $lteXmlFileName 
            SetDefaultTableSpace
            # 移动dm目录软链接
cat <<'EOS' >checkAndMoveDMDir.sh
#!/bin/bash

basePath="/home/<USER>/ngi-gpdb/ext"
if [ -h "${basePath}/dm/bin/NetMAXPLAT_DM.db" ];then
    echo "Found NetMAXPLAT_DM.db is an softlink, recover it to a normal file..."
    ls -l "${basePath}/dm/bin/NetMAXPLAT_DM.db"
    dmdbTruePath=$(readlink -f "${basePath}/dm/bin/NetMAXPLAT_DM.db")
    rm -vf "${basePath}/dm/bin/NetMAXPLAT_DM.db"
    mv -v "$dmdbTruePath" "${basePath}/dm/bin/NetMAXPLAT_DM.db"
    ls -l "${basePath}/dm/bin/NetMAXPLAT_DM.db"
fi

emsLinks=(
"${basePath}/dm/data/LTE/rundata/otherdata"
"${basePath}/dm/data/NR/rundata/otherdata"
"${basePath}/dm/emsdata"
"${basePath}/dm/EMSPreProcess/sourcedata"
"${basePath}/dm/NRUMEPreProcess/sourcedata"
)

for link in "${emsLinks[@]}"
do
    if [ -h "$link" ];then
        ls -l "$link"
        truePath=$(readlink -f "$link")
        linkDir=$(dirname "$link")
        rm -vf "$link"
        \cp -r -a "$truePath" "$linkDir" && rm -rf "$truePath"
        ls -l "$link"
    fi
done

if [ -h "${basePath}/dm" ];then
    ls -l "${basePath}/dm"
    truePath=$(readlink -f "${basePath}/dm")
    \cp -r -a "$truePath" SSD_PATH/ && ln -snf SSD_PATH/dm "${basePath}/dm"
elif [ -d "${basePath}/dm" ];then
    \cp -r -a "${basePath}/dm" SSD_PATH/ && rm -rf "${basePath}/dm" && ln -snf SSD_PATH/dm "${basePath}/dm"
    ls -l "${basePath}/dm"
fi

EOS
            sed -i "s|SSD_PATH|$ssdPath|g" checkAndMoveDMDir.sh
            for((i=1;i<=20;++i))
            do
              gpscp -f hosts_gp checkAndMoveDMDir.sh =:/home/<USER>/checkAndMoveDMDir.sh
              echo "Check result..."
              md5=$(md5sum checkAndMoveDMDir.sh | awk '{print $1}')
              gpscpResultCheck "hosts_gp" "/home/<USER>/checkAndMoveDMDir.sh" "$md5" "gpadmin"
              ret=$?
              if [ $ret -eq 0 ];then
                 echo "Result check success."
                 break;
              elif [ $i -lt 20 ];then
                 echo "Result check failed, begin retry ($i)..."
                 sleep 10
              else
                 echo "ERROR: Copy checkAndMoveDMDir.sh to all GP hosts failed, exiting..."
                 exit 1
              fi
            done
            cmd="cd /home/<USER>/checkAndMoveDMDir.sh"
            GPSSHResultCheck "f" hosts_gp "$cmd"
        fi
    elif [ "$netmaxdbTablespace" == "hottablespace" ];then
        # 已经执行过表分区操作
        SetImportTableTableSpace $nrImportTableJarPath $nrXmlFileName
        SetImportTableTableSpace $lteImportTableJarPath $lteXmlFileName 
    fi
}

#创建表空间的挂载路径
CreateTableSpaceDirPath(){
    echo "[$(date +'%F %T')] Start create hot and warm table spaces dir path."    
    #不存在路径则创建
    cmdTemp="if [ ! -d $hotTableSpacePath ]; then mkdir -p $hotTableSpacePath; fi && if [ ! -d $warmTableSpacePath ]; then mkdir -p $warmTableSpacePath; fi" 
    GPSSHResultCheck "f" "${gpIPFile}" "${cmdTemp}"
    echo "[$(date +'%F %T')] Finish create hot and warm table spaces dir path."       
}

#创建热表和温表空间
CreateTableSpace(){  
    echo "[$(date +'%F %T')] Start create hot and warm table spaces."    
    psql -c "Create TableSpace hottablespace LOCATION '$hotTableSpacePath';"
    psql -c "Create TableSpace warmtablespace LOCATION '$warmTableSpacePath';"  
    echo "[$(date +'%F %T')] Finish create hot and warm tablespaces."
}

#设置热表空间为默认表空间
SetDefaultTableSpace(){
    echo "[$(date +'%F %T')] Start set default table space."  
    #强制断开所有netmaxdb数据库连接
    psql -d postgres -c "SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE datname='netmaxdb' and pid<>pg_backend_pid();"
    #重新设置默认表空间
    psql -d postgres -c "alter database netmaxdb set tablespace hottablespace;"
    echo "[$(date +'%F %T')] Finish set default table space."
}

#修改导入表为温表空间
SetImportTableTableSpace(){  
   echo "[$(date +'%F %T')] Start set import table table space."  
   tablenamearray=() 
   #得到所有导入的表名     
   tablenamearray=$(unzip -p $1 $2 | grep -o -P  '(?<=tablename=")[^ ]*(?=")') 
   #表名去重
   tablenamearray=($(echo ${tablenamearray[*]} | sed 's/ /\n/g'|sort | uniq))
   if [ ${#tablenamearray[@]} != 0 ] 
   then     
       #修改所有表的默认表空间
       DoSetImportTableTableSpace "${tablenamearray[*]}"
   fi
   echo "[$(date +'%F %T')] Finish set import table table space."   
}  


#修改表空间为温表空间
DoSetImportTableTableSpace(){
    sql=""
    tablenamearrayTemp=$1  
    uuid=`uuidgen`
    sqlFileName=$homeDir"tmp"$uuid".sql"     
    for name in ${tablenamearrayTemp[*]}
    do     
       sql="alter table "$name" set tablespace warmtablespace;"
       #由于表名可能比较多，字符串拼接sql可能会内存溢出，采用写入sql文件的方式执行
       echo $sql >> $sqlFileName   
    done 
    psql -f $sqlFileName
    rm -f $sqlFileName  
}


#输入为1个参数，0表示安装，1表示升级
if [ $1 == 0 ]
then
   echo "[$(date +'%F %T')] Start install table space process."
   #执行安装过程
   InstallProcess
   echo "[$(date +'%F %T')] Finish install table space process."
elif [ $1 == 1 ]
then  
   echo "[$(date +'%F %T')] Start update table space process."
   #执行升级过程，当前路径为/home/<USER>/ngi-gpdb/ext/dm/bin/Update/Server/ZTE_UniPOS_NetMAX_Server
   echo "Current path: $PWD"
   UpdateProcess
   echo "[$(date +'%F %T')] Finish update table space process."
else
    echo "This install type is not supported."
fi
   
   
   
