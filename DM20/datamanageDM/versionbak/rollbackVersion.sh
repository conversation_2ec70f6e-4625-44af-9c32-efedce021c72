#!/bin/bash

# 执行版本回滚，当前用户为gpadmin

if [ "$USER" != gpadmin ];then
    echo "Must be gpadmin to run this script!"
    exit
fi

cat <<'EOS'
#############################################################
#                      !!! WARNING !!!                      #
#   You're performing a dangerous action that may cause     #
#           version features to be unavailable              #
#############################################################
EOS

declare -u answer
while [ "$answer" != "Y" -a "$answer" != "N" ]
do
    read -p "Do you really want to rollback to last version? [N/y] " answer
    answer=${answer:-N}
    answer=${answer:0:1}
    if [ "$answer" != "Y" -a "$answer" != "N" ];then
        echo "Invalid input!"
    fi
done
if [ "$answer" != "Y" ];then
    exit
fi

vCode=$(tr -cd "[:digit:]" </dev/urandom | head -c 6)
cat <<EOS
                +----------+
                |  $vCode  |
                +----------+
EOS
read -p "Please enter the verification code to continue: " code
if [ "$code" != "$vCode" ];then
    echo "Invalid verification code!"
    exit
fi

curDir=$(dirname $0)
cd "$curDir"

historyFiles=$(ls version_bak.zip.~[0-9]*~ 2>/dev/null | sort -t '~' -k 2,2 -n)

if [ -z "$historyFiles" ];then
    echo "No backup package of history version was found!"
    exit
fi

lastVersion=$(echo "$historyFiles" | tail -n 1)

# generate script to run rollback
do_rollbackScript=.$(basename $0)
sed -n '2,${\C^#!/bin/bashC,$p}' $0 > $do_rollbackScript
chmod +x $do_rollbackScript

logFile=rollback_$(date +'%F_%H_%M_%S').log
nohup ./$do_rollbackScript $lastVersion >../logs/$logFile 2>&1 &
nohup_pid=$!
echo "[$(date +'%F %T')] Begin to rollback version, log file: /home/<USER>/ngi-gpdb/ext/dm/logs/$logFile"
tail -f --pid $nohup_pid ../logs/$logFile

exit
############################################################################################################
#!/bin/bash

lastVersion=$1

curDir=$(dirname $(readlink -f $0))
cd "$curDir"

rm -vf ".version_bak.zip.~[0-9]*~.rollback"
mv -v "$lastVersion" ".${lastVersion}.rollback"

p1=$'\x70\x75\153\143\x61\x42'
p2=$'\123\117\x50\x69\156\125\x40' 
p3=$'\x45\124\132\x5f\x32\060\062\x32'
zipToken=$p1$p2$p3
tempDir=$(mktemp -d tmp_XXXXXX)
unzip -q -P $zipToken ".${lastVersion}.rollback" -d $tempDir

if [ ! -f $tempDir/datamanage-newcode.zip ];then
    echo "File $curDir/$tempDir/datamanage-newcode.zip does not exists, unzip .${lastVersion}.rollback failed!"
    ls -ltr $tempDir
    rm -rf $tempDir
    mv -v ".${lastVersion}.rollback" "$lastVersion"
    exit
fi

cd $tempDir
rollbackVer=$(unzip -p datamanage-newcode.zip version.cc)
echo -n "Rollback to version: $rollbackVer"
buildTime=$(unzip -l datamanage-newcode.zip version.cc | grep -P -o '[0-9]{2}-[0-9]{2}-20[0-9]{2}(?=.*version.cc$)')
echo "(build time: $buildTime)"

echo ">>>[$(date +'%F %T')] begin to do extract Database files..."
unzip -q datamanage-newcode.zip DB/NETMAXDB/Greenplum/Rollback/* DB/NETMAXDB/Greenplum/schema/*/sp/* DB/NETMAXDB/Greenplum/schema/*/fn/* DB/NETMAXDB/Greenplum/schema/*/vew/* DB/NETMAXDB/Greenplum/schema/*/vew_post/* DB/NETMAXDB/InitData/* -d DBSchema
chmod -R 755 DBSchema/DB/NETMAXDB
echo "---[$(date +'%F %T')] end to do extract Database files"

echo ">>>[$(date +'%F %T')] begin to do extract DM files..."
zip -q -d datamanage-newcode.zip ApPackage/* DB/*
mv -v datamanage-newcode.zip rollbackPackage.zip
echo "---[$(date +'%F %T')] end to do extract DM files"

(
    cd /home/<USER>/ngi-gpdb/ext/dm
    # stop DM and restart GP
    ./gpsshDatamanageStop.sh
    gpstop -a -M fast
    gpstart -a
)
ret=$?
if [ $ret -ne 0 ];then
    mv -v ".${lastVersion}.rollback" "$lastVersion"
    echo "[$(date +'%F %T')] Rollback to version $rollbackVer(build time: $buildTime) failed!"
    exit $ret
fi
export NETMAX='psql -f'
export NETMAX_SQL='psql -c'
(
    echo ">>>[$(date +'%F %T')] begin to do Custom table rollback..."
    cd DBSchema/DB/NETMAXDB/Greenplum/Rollback/
    # rollback some incompatible tables
    curVersion=$(cat /home/<USER>/ngi-gpdb/ext/dm/version.cc)
    ./rollback.sh "$rollbackVer" "$buildTime" "$curVersion"
    echo "---[$(date +'%F %T')] end to do Custom table rollback"
    # drop all sp/fn/view
    if [ -f "$curDir/delete_all_fn_view.sql" ];then
        echo "Use $curDir/delete_all_fn_view.sql instead"
        cat "$curDir/delete_all_fn_view.sql" >delete_all_fn_view.sql
    fi
    psql -f delete_all_fn_view.sql
)
(
    echo ">>>[$(date +'%F %T')] begin to do InitData rollback..."
    cd DBSchema/DB/NETMAXDB/InitData
    # rollback InitData
    ./upgradeInstall.sh
    echo "---[$(date +'%F %T')] end to do InitData rollback"
)
(
    echo ">>>[$(date +'%F %T')] begin to do fn/sp/view rollback..."
    cd DBSchema/DB/NETMAXDB/Greenplum/schema
    # Refresh fn/sp/view  
    subDirs=(fn sp vew vew_post)
    for netDir in `ls`
    do
        [ ! -d "$netDir" -o "$netDir" == "5G" ] && continue
        for subDir in ${subDirs[@]}
        do
            [ ! -f "$netDir/$subDir/install_$subDir.sh" ] && continue
            echo "$netDir/$subDir/install_$subDir.sh"
            ( cd "$netDir/$subDir"; ./install_$subDir.sh )
        done
    done
    # Place 5G at last for the reason of a dependency of 4G's view
    for subDir in ${subDirs[@]}
    do
        [ ! -f "5G/$subDir/install_$subDir.sh" ] && continue
        echo "5G/$subDir/install_$subDir.sh"
        ( cd "5G/$subDir"; ./install_$subDir.sh )
    done
    echo "---[$(date +'%F %T')] end to do fn/sp/view rollback"
)
(
    echo ">>>[$(date +'%F %T')] begin to do DM package rollback..."
    unzip -q -o rollbackPackage.zip -d /home/<USER>/ngi-gpdb/ext/dm
    pushd /home/<USER>/ngi-gpdb/ext/dm
    find . -name "*.sh" -type f -exec chmod +x {} +
    \cp -av data/load_shell/*.sh /home/<USER>/.netmax_shell/
    popd
    
    # 定义校验gpscp执行结果的函数
    # Param1: hostsfile or hostslist(start with "," eg. ",************,************,************")
    # Param2: filepath on remote
    # Param3: file md5
    # Param4: user
    # Call example: gpscpResultCheck /home/<USER>/dm_hosts /home/<USER>/gp_OSCheck.sh b28651c797d044f2523343235481efd5 gpadmin
    function gpscpResultCheck()
    {
        if [ $# -lt 4 ];then
            return 1
        fi
        hostParam=$1
        remoteFilePath=$2
        md5Check=$3
        user=$4

        declare -i hostNum=9999
        if [ "${hostParam:0:1}" == "," ];then
            hostNum=$(awk 'BEGIN{FS=","}{print NF-1}' <<<"$hostParam")
            hostsOption=${hostParam//,/ -h }
            cmd="gpssh $hostsOption \"md5sum '$remoteFilePath'\""
        else
            hostNum=$(awk 'BEGIN{hostsLine=""}{hostsLine=hostsLine" "$1}END{print hostsLine}' "$hostParam" | awk '{print NF}')
            cmd="gpssh -f '$hostParam' \"md5sum '$remoteFilePath'\""
        fi
        echo "Check command:  $cmd"
        echo "Expected MD5 is $md5Check"

        declare -i targetMd5Count=0
        if [ "$user" == "gpadmin" ];then
            targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
        else
            targetMd5Count=$(bash -c "$cmd | grep -c '$md5Check'")
        fi
        echo "Check result: $targetMd5Count/$hostNum"

        if [ $targetMd5Count -ge $hostNum ];then
            return 0
        else
            return 1
        fi
    }
    # rollback slave
    if [ -f /home/<USER>/paas_hosts_seg.cc ];then
        for((i=1;i<=10;++i))
        do
            gpscp -f /home/<USER>/paas_hosts_seg.cc rollbackPackage.zip =:/home/<USER>/ngi-gpdb/ext/dm
            echo "Check result..."
            gpscpResultCheck /home/<USER>/paas_hosts_seg.cc /home/<USER>/ngi-gpdb/ext/dm/rollbackPackage.zip $(md5sum rollbackPackage.zip | awk '{print $1}') gpadmin
            ret=$?
            if [ $ret -eq 0 ];then
               echo "Result check success."
               break;
            elif [ $i -lt 10 ];then
               echo "Result check failed, begin retry ($i)..."
               sleep 10
            else
               echo "ERROR: Copy rollbackPackage.zip to other hosts failed, exiting..."
               exit 1
            fi
        done
        gpssh -f /home/<USER>/paas_hosts_seg.cc "date +'%F %T'"
        cmd='
            cd /home/<USER>/ngi-gpdb/ext/dm
            unzip -q -o rollbackPackage.zip
            find . -name "*.sh" -type f -exec chmod +x {} +
            \cp -av data/load_shell/*.sh /home/<USER>/.netmax_shell/
        '
        gpssh -f /home/<USER>/paas_hosts_seg.cc -e "$cmd"
    fi
    echo "---[$(date +'%F %T')] end to do DM package rollback"
)
ret=$?
if [ $ret -ne 0 ];then
    mv -v ".${lastVersion}.rollback" "$lastVersion"
    echo "[$(date +'%F %T')] Rollback to version $rollbackVer(build time: $buildTime) failed!"
    exit $ret
fi
# remove temp directory and file
cd "$curDir"
rm -rf $tempDir
rm -vf $(basename $0)

# remove datamanage-newcode.zip
rm -vf /home/<USER>/ngi-gpdb/ext/dm/bin/Update/Server/datamanage-newcode.zip
rm -vf /home/<USER>/ngi-gpdb/ext/dm/datamanage-newcode.zip

# update current version backup file
\cp -av ".${lastVersion}.rollback" version_bak.zip

# start DM service
(
    cd /home/<USER>/ngi-gpdb/ext/dm
    # start DM
    ./gpsshDatamanageStart.sh
)

echo "[$(date +'%F %T')] Rollback to version $rollbackVer(build time: $buildTime) completed!"
