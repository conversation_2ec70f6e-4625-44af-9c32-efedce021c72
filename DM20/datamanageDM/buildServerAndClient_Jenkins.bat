set SourcePath=%cd%
echo %SourcePath%

REM 7-Zip·��
set SZip="D:\7-Zip\7z.exe"

REM ǰ�˴���·��(���ش���·����Ҫע���޸�)
set NovPath=D:\Code\DM\datamanage\DM20\DM-NOVisualization
echo %NovPath%

REM ����ǰ��
cd /d "%NovPath%"
echo "compile Client package..."
REM call npm install
call npm run build

echo "delete and mk %SourcePath%..."
rd /s /q %SourcePath%\src\main\resources
md %SourcePath%\src\main\resources\DM-NOVisualization

echo "copy NOV..."
xcopy "%NovPath%\dist\*" "%SourcePath%\src\main\resources\DM-NOVisualization" /e /y /q

REM ���º�˴���
cd /d "%SourcePath%"

REM ������
echo "compile Server package..."
call mvn clean package --settings D:\Code\DM\datamanage\DM20\datamanageDM\settings.xml
echo "compile Server package end"

REM ����SFTP
echo "compile SFTP package..."
cd /d ""%SourcePath%"\..\SFTPServer"
call mvn clean package --settings D:\Code\DM\datamanage\DM20\datamanageDM\settings.xml
echo "compile SFTP package end"

REM ����NGIDBMonitor
echo "compile NGIDBMonitor package..."
cd /d ""%SourcePath%"\..\NGIDBMonitor"
call mvn clean package
echo "compile NGIDBMonitor package end"

REM ����DataDownloadMonitor
echo "compile DataDownloadMonitor package..."
cd /d ""%SourcePath%"\..\DataDownloadMonitor"
call mvn clean package
echo "compile DataDownloadMonitor package end"

REM ����AlertManager
echo "compile alertManager package..."
cd /d ""%SourcePath%"\..\alertManager"
call mvn clean package
echo "compile alertManager package end"


echo "copy jar..."
cd /d "%SourcePath%"
md %SourcePath%\datamanagetest
copy "%SourcePath%\target\datamanage-newcode-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest" /y

echo "copy files..."
REM copy "%SourcePath%\shfile\InitNGI.sh" "%SourcePath%\ngi-common-service" /y
copy "%SourcePath%\StartDatamanage.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\StartDatamanageNew.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\StopDatamanageNew.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\datamanage.keystore" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\UnistallDatamanage.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\UnistallDatamanagefirst.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\gpsshDatamanageStart.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\gpsshDatamanageStop.sh" "%SourcePath%\datamanagetest" /y
REM copy "%SourcePath%\shfile\UnistallNGI.sh" "%SourcePath%\ngi-common-service" /y
REM md "%SourcePath%\ngi-common-service\tmpfile"
REM xcopy "%SourcePath%\tmpfile\*.*" "%SourcePath%\ngi-common-service\tmpfile" /e /y /s /q
md "%SourcePath%\datamanagetest\sql_script"
xcopy "%SourcePath%\sql_script\*.*" "%SourcePath%\datamanagetest\sql_script" /e /y /s /q
REM md "%SourcePath%\datamanagetest\lib"
REM xcopy "%SourcePath%\lib\*.*" "%SourcePath%\datamanagetest\lib" /e /y /s /q
md "%SourcePath%\datamanagetest\data"
xcopy "%SourcePath%\data\*.*" "%SourcePath%\datamanagetest\data" /e /y /s /q
REM md "%SourcePath%\ngi-common-service\file"
REM xcopy "%SourcePath%\file\*.*" "%SourcePath%\ngi-common-service\file" /e /y /s /q
md "%SourcePath%\datamanagetest\conf"
xcopy "%SourcePath%\conf\*.*" "%SourcePath%\datamanagetest\conf" /e /y /s /q
md "%SourcePath%\datamanagetest\bin"
xcopy "%SourcePath%\bin\*.*" "%SourcePath%\datamanagetest\bin" /e /y /s /q
md "%SourcePath%\datamanagetest\DB"
xcopy "%SourcePath%\..\DB\*.*" "%SourcePath%\datamanagetest\DB" /e /y /s /q
md "%SourcePath%\datamanagetest\EMSPreProcess"
xcopy "%SourcePath%\EMSPreProcess\*.*" "%SourcePath%\datamanagetest\EMSPreProcess" /e /y /s /q
md "%SourcePath%\datamanagetest\SFTPServer"
xcopy "%SourcePath%\SFTPServer\*.*" "%SourcePath%\datamanagetest\SFTPServer" /e /y /s /q
xcopy "%SourcePath%\..\SFTPServer\target\lib\*.*" "%SourcePath%\datamanagetest\SFTPServer\lib" /e /y /s /q
copy "%SourcePath%\..\SFTPServer\target\SFTPServer-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\SFTPServer\lib"

md "%SourcePath%\datamanagetest\NGIDBMonitor"
xcopy "%SourcePath%\NGIDBMonitor\*" "%SourcePath%\datamanagetest\NGIDBMonitor" /e /y /s /q
xcopy "%SourcePath%\..\NGIDBMonitor\target\lib\*" "%SourcePath%\datamanagetest\NGIDBMonitor\lib" /e /y /s /q
copy "%SourcePath%\..\NGIDBMonitor\target\NGIDBMonitor-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\NGIDBMonitor\bin"

md "%SourcePath%\datamanagetest\alertManager"
xcopy "%SourcePath%\alertManager\*" "%SourcePath%\datamanagetest\alertManager" /e /y /s /q
xcopy "%SourcePath%\..\alertManager\target\lib\*" "%SourcePath%\datamanagetest\alertManager\lib" /e /y /s /q
copy "%SourcePath%\..\alertManager\target\alertManager-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\alertManager\bin"

REM DataDownloadMonitor
md "%SourcePath%\datamanagetest\DataDownloadMonitor"
xcopy "%SourcePath%\DataDownloadMonitor\*" "%SourcePath%\datamanagetest\DataDownloadMonitor" /e /y /s /q
xcopy "%SourcePath%\..\DataDownloadMonitor\target\lib\*" "%SourcePath%\datamanagetest\DataDownloadMonitor\lib" /e /y /s /q
copy "%SourcePath%\..\DataDownloadMonitor\target\DataDownloadMonitor-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\DataDownloadMonitor\bin"




echo "zip files..."

del datamanage-newcode.zip
del "D:\Code\DM\packages\datamanage-service.zip"

call %SZip% a "datamanage-newcode.zip" "%SourcePath%\datamanagetest\*"
call %SZip% a "D:\Code\DM\packages\datamanage-service.zip" "%SourcePath%\datamanage-newcode.zip" "%SourcePath%\InstallDatamanage.sh"

echo "clean..."
del datamanage-newcode.zip
rd /s /q "%SourcePath%\datamanagetest"

REM rename ngi-service.zip datamanage-newcode.zip

echo "===== build ServerAndCilent End====="