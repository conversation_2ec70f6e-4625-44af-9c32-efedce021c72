#!/bin/bash

# DDD目录结构创建脚本
# 用于创建数据管理系统的领域驱动设计目录结构

BASE_PATH="src/main/java/com/zte/datamanage"

echo "开始创建DDD目录结构..."

# 创建领域层目录
echo "创建领域层目录..."
mkdir -p "${BASE_PATH}/domain/model/task"
mkdir -p "${BASE_PATH}/domain/model/execution"
mkdir -p "${BASE_PATH}/domain/model/dataimport"
mkdir -p "${BASE_PATH}/domain/model/schedule"
mkdir -p "${BASE_PATH}/domain/service"
mkdir -p "${BASE_PATH}/domain/event"

# 创建应用层目录
echo "创建应用层目录..."
mkdir -p "${BASE_PATH}/application/service"
mkdir -p "${BASE_PATH}/application/command"
mkdir -p "${BASE_PATH}/application/query"
mkdir -p "${BASE_PATH}/application/dto"

# 创建基础设施层目录
echo "创建基础设施层目录..."
mkdir -p "${BASE_PATH}/infrastructure/repository"
mkdir -p "${BASE_PATH}/infrastructure/persistence"
mkdir -p "${BASE_PATH}/infrastructure/adapter"
mkdir -p "${BASE_PATH}/infrastructure/config"
mkdir -p "${BASE_PATH}/infrastructure/external/nds"
mkdir -p "${BASE_PATH}/infrastructure/external/ems"
mkdir -p "${BASE_PATH}/infrastructure/external/vmax"

# 创建接口层目录
echo "创建接口层目录..."
mkdir -p "${BASE_PATH}/interfaces/rest"
mkdir -p "${BASE_PATH}/interfaces/facade"
mkdir -p "${BASE_PATH}/interfaces/assembler"

# 创建遗留代码目录
echo "创建遗留代码目录..."
mkdir -p "${BASE_PATH}/legacy/taskmanage"
mkdir -p "${BASE_PATH}/legacy/dataimport"
mkdir -p "${BASE_PATH}/legacy/sqlitedatamanage"

# 创建.gitkeep文件和说明
echo "创建.gitkeep文件和说明..."

# 领域层
echo "# 任务聚合目录" > "${BASE_PATH}/domain/model/task/.gitkeep"
echo "# 执行配置聚合目录" > "${BASE_PATH}/domain/model/execution/.gitkeep"
echo "# 数据导入聚合目录" > "${BASE_PATH}/domain/model/dataimport/.gitkeep"
echo "# 调度聚合目录" > "${BASE_PATH}/domain/model/schedule/.gitkeep"
echo "# 领域服务目录" > "${BASE_PATH}/domain/service/.gitkeep"
echo "# 领域事件目录" > "${BASE_PATH}/domain/event/.gitkeep"

# 应用层
echo "# 应用服务目录" > "${BASE_PATH}/application/service/.gitkeep"
echo "# 命令对象目录" > "${BASE_PATH}/application/command/.gitkeep"
echo "# 查询对象目录" > "${BASE_PATH}/application/query/.gitkeep"
echo "# 数据传输对象目录" > "${BASE_PATH}/application/dto/.gitkeep"

# 基础设施层
echo "# 仓储实现目录" > "${BASE_PATH}/infrastructure/repository/.gitkeep"
echo "# 持久化对象目录" > "${BASE_PATH}/infrastructure/persistence/.gitkeep"
echo "# 适配器目录" > "${BASE_PATH}/infrastructure/adapter/.gitkeep"
echo "# 配置目录" > "${BASE_PATH}/infrastructure/config/.gitkeep"
echo "# NDS系统集成目录" > "${BASE_PATH}/infrastructure/external/nds/.gitkeep"
echo "# EMS系统集成目录" > "${BASE_PATH}/infrastructure/external/ems/.gitkeep"
echo "# VMAX系统集成目录" > "${BASE_PATH}/infrastructure/external/vmax/.gitkeep"

# 接口层
echo "# REST接口目录" > "${BASE_PATH}/interfaces/rest/.gitkeep"
echo "# 门面服务目录" > "${BASE_PATH}/interfaces/facade/.gitkeep"
echo "# 组装器目录" > "${BASE_PATH}/interfaces/assembler/.gitkeep"

# 遗留代码
echo "# 遗留任务管理代码目录" > "${BASE_PATH}/legacy/taskmanage/.gitkeep"
echo "# 遗留数据导入代码目录" > "${BASE_PATH}/legacy/dataimport/.gitkeep"
echo "# 遗留SQLite管理代码目录" > "${BASE_PATH}/legacy/sqlitedatamanage/.gitkeep"

echo "✅ DDD目录结构创建完成！"
echo ""
echo "📁 创建的目录结构："
echo "src/main/java/com/zte/datamanage/"
echo "├── domain/                     # 领域层"
echo "│   ├── model/                 # 领域模型"
echo "│   │   ├── task/             # 任务聚合"
echo "│   │   ├── execution/        # 执行配置聚合"
echo "│   │   ├── dataimport/       # 数据导入聚合"
echo "│   │   └── schedule/         # 调度聚合"
echo "│   ├── service/              # 领域服务"
echo "│   └── event/                # 领域事件"
echo "├── application/               # 应用层"
echo "│   ├── service/              # 应用服务"
echo "│   ├── command/              # 命令对象"
echo "│   ├── query/                # 查询对象"
echo "│   └── dto/                  # 数据传输对象"
echo "├── infrastructure/           # 基础设施层"
echo "│   ├── repository/           # 仓储实现"
echo "│   ├── persistence/          # 持久化对象"
echo "│   ├── adapter/              # 适配器"
echo "│   ├── config/               # 配置"
echo "│   └── external/             # 外部系统集成"
echo "│       ├── nds/              # NDS系统集成"
echo "│       ├── ems/              # EMS系统集成"
echo "│       └── vmax/             # VMAX系统集成"
echo "├── interfaces/               # 接口层"
echo "│   ├── rest/                 # REST接口"
echo "│   ├── facade/               # 门面服务"
echo "│   └── assembler/            # 组装器"
echo "└── legacy/                   # 遗留代码"
echo "    ├── taskmanage/           # 遗留任务管理"
echo "    ├── dataimport/           # 遗留数据导入"
echo "    └── sqlitedatamanage/     # 遗留SQLite管理"
echo ""
echo "🎯 使用方法："
echo "chmod +x create_ddd_structure.sh"
echo "./create_ddd_structure.sh"