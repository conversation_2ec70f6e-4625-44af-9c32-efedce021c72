#!/bin/bash
source /home/<USER>/.bash_profile
#restart GreenPlum database
RestartGP()
{
  if [  "$USER" == "gpadmin" ]
  then
    #echo "1111111111"
    #重启gp数据库
	gpstop -a -M immediate &> /dev/null
	echo -e Y | gpstart &> /dev/null
	#echo "Restart GP Complete"
  else
  	#重启gp数据库
	sudo su - gpadmin -c "gpstop -a -M immediate" &> /dev/null
	sudo su - gpadmin -c "echo -e Y | gpstart" &> /dev/null
	#echo "Restart GP Complete" 
	#echo "22222"
  fi
}



#----------------------------------------------------
# Function    : show_msg 
# Description : print formatted message
# Parameter   : $1 - message
#----------------------------------------------------
show_msg()
{
    echo "***********************************************************************"
    echo "                    $1"
    echo "***********************************************************************"
}



restart(){
  curPath=$(pwd)
  cd /home/<USER>/ngi-gpdb/ext/dm
./gpsshDatamanageStop.sh -987789   &> /dev/null
  RestartGP
  cd /home/<USER>/ngi-gpdb/ext/dm
./gpsshDatamanageStart.sh -987789  &> /dev/null
  cd $curPath
}


curPath=$(pwd)
#目录固定
if [  "$USER" == "gpadmin" ]
then
  #echo "1111111111"
  psql -t -A -c "select Sp_check_db_segment_helth(); " &> /dev/null
 if [ $? -ne 0 ] ; then
   restart
  fi
  gpstate  &> /dev/null
  if [ $? -ne 0 ] ; then
    restart
  fi
else
  su - gpadmin -c "psql -t -A -c \"select Sp_check_db_segment_helth();\"" &> /dev/null
  if [ $? -ne 0 ] ; then
    restart
  fi
  su - gpadmin -c "gpstate" &> /dev/null
  if [ $? -ne 0 ] ; then
    restart
  fi
  #echo "22222"
fi
cd $curPath
