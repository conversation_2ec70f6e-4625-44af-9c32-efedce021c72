#!/bin/bash
source /home/<USER>/.bash_profile
if [  "$USER" == "gpadmin" ]
then
  #echo "1111111111"
  psql -t -A -c "select Sp_check_db_segment_helth(); " &> /dev/null
  if [ $? -ne 0 ] ; then
    exit 1
  fi
  gpstate &> /dev/null
  if [ $? -ne 0 ] ; then
    exit 1
  fi
else
  su - gpadmin -c "psql -t -A -c \"select Sp_check_db_segment_helth();\"" &> /dev/null
  if [ $? -ne 0 ] ; then
    exit 1
  fi
  su - gpadmin -c "gpstate" &> /dev/null
  if [ $? -ne 0 ] ; then
    exit 1
  fi
  #echo "22222"
fi
#echo "USER: $USER"
exit 0











