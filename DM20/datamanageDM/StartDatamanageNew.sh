#!/bin/bash
#############通过操作系统的判断决定jdk位置############
#!/bin/bash


#启动SFTPServer,root用户需要切换到gpadmin
startup_sftpServer()
{
	if [ "$user" == "gpadmin" ]
	then
	    #gpdbInstallFlag=`psql -l 2>/dev/null | grep netmaxdb | grep -v grep | wc -l`
		#if [[ $gpdbInstallFlag -eq 1 ]];  then
		MASTER_IP=`cat /home/<USER>/paas_hosts_master.cc`
		MASTER_HOSTNAME=`cat /etc/hosts|grep -w ${MASTER_IP}|awk '{print $2}'`
		CUR_HOST=`hostname`
		echo "MASTER_HOSTNAME: ${MASTER_HOSTNAME}"
		echo "CUR_HOST: ${CUR_HOST}"
		echo "USER: $USER"
		if [ "${MASTER_HOSTNAME}" == "${CUR_HOST}" ]
		then
			
			local RESULT=""
			if [ -f "$SFTPSERVER_HOME/startsftp.sh" ]
			then 
				echo "Starting sftpServer process....."
				#RESULT=`chmod a+x -R jdk;cd $SFTPSERVER_HOME; ./startsftp.sh -p 15434 -o sftphome=/ -o Ciphers=aes256-ctr,aes192-ctr,aes128-ctr -o MACs=hmac-sha2-256,hmac-sha2-512,hmac-sha1,<EMAIL> -o KeyExchanges=diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521`
				#sleep  5
				#echo $RESULT
				#process_is_running "$SFTPSERVER_HOME"
				#CheckCode=$?
				for((i=1;i<=20;++i))
				do
					RESULT=`chmod a+x -R jdk;cd $SFTPSERVER_HOME; ./startsftp.sh -p 15434 -o sftphome=/ -o Ciphers=aes256-ctr,aes192-ctr,aes128-ctr -o MACs=hmac-sha2-256,hmac-sha2-512,hmac-sha1,<EMAIL> -o KeyExchanges=diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521`
					sleep  5
					echo $RESULT
					process_is_running "$SFTPSERVER_HOME"
					CheckCode=$?
					if [ $CheckCode -eq 0 ]
					then
						echo "Starting sftpServer succeeded"
						break;
					else
						echo "Starting sftpServer failed"
					fi
				done
				fail_to_exit ${CheckCode} 0 "Failed to start sftpServer process."
			fi
		fi 
    else 
		gpdbInstallFlag=`su - gpadmin -c "psql -l" 2>/dev/null | grep netmaxdb | grep -v grep | wc -l`
		if [[ $gpdbInstallFlag -eq 1 ]];  then
			local RESULT=""
			if [ -f "$SFTPSERVER_HOME/startsftp.sh" ]
			then 
				echo "Starting sftpServer process....."
				#RESULT=`su - gpadmin -c "cd ${current_path};chmod a+x -R jdk;cd $SFTPSERVER_HOME; ./startsftp.sh -p 15434 -o sftphome=/ -o Ciphers=aes256-ctr,aes192-ctr,aes128-ctr -o MACs=hmac-sha2-256,hmac-sha2-512,hmac-sha1,<EMAIL> -o KeyExchanges=diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521"`
				#sleep  5
				#echo $RESULT
				#process_is_running "$SFTPSERVER_HOME"
				for((i=1;i<=20;++i))
				do
					RESULT=`su - gpadmin -c "cd ${current_path};chmod a+x -R jdk;cd $SFTPSERVER_HOME; ./startsftp.sh -p 15434 -o sftphome=/ -o Ciphers=aes256-ctr,aes192-ctr,aes128-ctr -o MACs=hmac-sha2-256,hmac-sha2-512,hmac-sha1,<EMAIL> -o KeyExchanges=diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521"`
					sleep  5
					echo $RESULT
					process_is_running "$SFTPSERVER_HOME"
					CheckCode=$?
					if [ $CheckCode -eq 0 ]
					then
						echo "Starting sftpServer succeeded"
						break;
					else
						echo "Starting sftpServer failed"
					fi
				done
			fi
			fail_to_exit $CheckCode 0 "Failed to start sftpServer process."
		fi
	fi
}

process_is_running()
{
   local query_pattern=$1
   local process=`ps -aef|grep $query_pattern | grep -v grep`
   if [ -n "$process" ]
   then
       return 0
   else
       return 1
   fi
}

fail_to_exit()
{
    if [ "$1" != "$2" ]
    then
        if [ -n "$3" ]
        then 
            echo "$3"
        fi
        exit $1
    fi
}



dba_user=gpadmin;
current_path=`pwd`;
user=`whoami`;

SFTPSERVER_HOME="${current_path}/SFTPServer"

#echo ${user};
#echo ${current_path}
if [ "$user" == "gpadmin" ]
then
  chmod +x  BackupDatamanage.sh 
  nohup ./BackupDatamanage.sh >/dev/null  2>&1  & 
else
  su - ${dba_user} -c "cd ${current_path}; chmod +x  BackupDatamanage.sh; nohup ./BackupDatamanage.sh >/dev/null  2>&1  &"
fi 
if [ "$user" == "gpadmin" ]
then
  chmod +x  StartDatamanage.sh
  ./StartDatamanage.sh $1;   
else
  su - ${dba_user} -c "cd ${current_path}; chmod +x  StartDatamanage.sh; ./StartDatamanage.sh $1;"
fi 




#启动SFTPServer
startup_sftpServer

echo "StartDataManageNew complete...";
