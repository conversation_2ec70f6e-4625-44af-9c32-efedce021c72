---
alwaysApply: true
---
# ================================
# 🎯 RIPER-5 模式 + 项目特定规则
# ================================

## 📋 总体要求
- 每次都必须使用中文回答
- 遵循RIPER-5模式的严格协议
- 针对基于FastAPI的领域驱动设计(DDD)架构优化
- 专为Python异步编程和图计算工作流设计
- 严格遵循TDD(Test-Driven Development)开发规范

## 🔄 RIPER-5 模式协议 (严格遵循)

### 模式声明要求
**必须在每个回答开始时声明当前模式：[模式: 模式名称]**

### 模式1: 研究模式 [模式: 研究]
- **目的**: 仅收集信息和理解代码结构
- **允许**: 读取文件、问澄清问题、理解现有代码
- **禁止**: 提供建议、实现、规划或任何暗示行动
- **要求**: 只能寻求理解现有内容，不能提及改进
- **持续**: 直到显式指令切换模式

### 模式2: 创新模式 [模式: 创新]
- **目的**: 头脑风暴潜在解决方案
- **允许**: 讨论想法、分析优缺点、征求反馈
- **禁止**: 具体规划、实现细节、编写代码
- **要求**: 所有想法都作为可能性呈现，不是决定
- **持续**: 直到显式指令切换模式

### 模式3: 规划模式 [模式: 规划]
- **目的**: 创建详尽的技术规范
- **允许**: 详细计划包含确切文件路径、函数名和更改
- **禁止**: 任何实现或代码编写，即使是"示例代码"
- **要求**: 计划必须足够详细，在实现时不需要创造性决策
- **强制最后步骤**: 将整个计划转换为编号的顺序清单
- **持续**: 直到计划被明确批准并指令切换模式

### 模式4: 执行模式 [模式: 执行]
- **目的**: 完全按照模式3的计划实施
- **允许**: 仅实现计划中明确详述的内容
- **禁止**: 任何偏离、改进或未在计划中的创造性添加
- **进入要求**: 仅在明确的"进入执行模式"命令后进入
- **偏离处理**: 如发现任何需要偏离的问题，立即返回规划模式

### 模式5: 审查模式 [模式: 审查]
- **目的**: 严格验证实现与计划的一致性
- **允许**: 逐行比较计划和实现
- **要求**: 明确标记任何偏离，无论多么轻微
- **偏离格式**: "⚠️ 检测到偏离: [偏离的确切描述]"
- **结论格式**: "✅ 实现与计划完全匹配" 或 "❌ 实现偏离计划"

## 📚 知识库集成规范

### 知识库目录结构
```
知识库/
├── 提示信息文档/
│   ├── 需求文档.md     # 需求规格说明，包含验收准则
│   └── 开发方案.md     # TDD开发方案模版
└── [其他需求文档...]
```

### 规划模式特殊要求
在**[模式: 规划]**时，必须执行以下步骤：

1. **读取知识库**: 首先读取@/知识库目录下的相关需求文档
2. **分析验收准则**: 从需求文档中提取验收准则和关键要求
3. **创建开发方案**: 基于@/知识库/提示信息文档/开发方案.md模版创建具体的开发方案
4. **TDD设计**: 严格按照TDD规范设计测试场景和测试用例
5. **验收覆盖**: 确保TDD TODOLIST清单完全涵盖需求方案的验收准则

### 开发方案文档要求
在规划模式时，必须生成完整的开发方案文档，包含：

#### 1. 用户故事描述
- 简短文字描述功能
- 明确-
交付边界

#### 2. TDD部分
- **测试场景**: 使用测试用例设计走查单
- **测试用例**: 详细的测试用例表格，包含：
  - 测试场景
  - 用例编号
  - 用例名称
  - 预置条件数据
  - 参数数据
  - then数据

#### 3. 详设部分
- **新增接口定义**: API接口规范
- **新增外部依赖**: 外部服务依赖分析
- **流程图**: 代码流程设计
- **类图**: 对象设计和关系

#### 4. 评审改进项
- 问题列表和改进建议

### 执行模式特殊要求
在**[模式: 执行]**时，必须确保：

1. **TODOLIST完成**: 所有TDD TODOLIST清单项目都必须通过
2. **UT覆盖率**: 单元测试覆盖率必须达到90%以上
3. **测试优先**: 先编写测试，再编写实现代码
4. **验收验证**: 生成的代码必须满足原始需求的验收准则

## 🧪 TDD开发流程

### TDD三步法则
1. **RED**: 编写一个失败的测试
2. **GREEN**: 编写最少的代码使测试通过
3. **REFACTOR**: 重构代码但保持测试通过

### 测试场景设计要求
基于知识库中的测试用例设计走查单，必须考虑：

- **文件访问**: 文件解压缩、清理机制、访问冲突
- **数据库访问**: SQL访问错误、事务处理
- **交互设计**: 交互设计易用性、错误处理
- **边界条件**: 异常情况处理和恢复机制

### 测试用例验证
每个测试用例必须包含：
- **Given**: 预置条件数据
- **When**: 参数数据和操作
- **Then**: 预期结果数据

## 🐍 Python/FastAPI 项目特定规则


### 代码架构规范
- **DDD架构**: 严格遵循domain(领域层)、infrastructure(基础设施层)、interfaces(接口层)分层
- **异步优先**: 所有IO操作使用async/await，遵循异步编程最佳实践
- **类型提示**: 所有函数和方法必须包含完整的类型注解
- **错误处理**: 使用自定义异常类，继承自项目的基础异常类

### 数据库操作
- **SQLModel优先**: 使用SQLModel而非纯SQLAlchemy
- **异步会话**: 使用AsyncSession进行所有数据库操作
- **迁移管理**: 使用Alembic进行数据库迁移，遵循现有版本控制模式
- **事务管理**: 确保数据库操作的原子性和一致性

### API设计
- **路由结构**: 遵循现有的路由组织模式，使用APIRouter
- **依赖注入**: 使用FastAPI的依赖注入系统
- **中间件**: 遵循现有的AuthMiddleware模式
- **响应模型**: 使用Pydantic模型定义响应结构

### 测试规范
- **测试框架**: 使用pytest和pytest-asyncio
- **TDD优先**: 必须先编写测试，再编写实现代码
- **Mock策略**: 广泛使用Mock和patch进行单元测试
- **异步测试**: 使用@pytest.mark.asyncio装饰器
- **测试数据**: 使用fixture管理测试数据，遵循现有的conftest.py模式
- **覆盖率要求**: 单元测试覆盖率必须达到90%以上

### 日志与监控
- **日志系统**: 使用loguru进行日志记录
- **告警集成**: 遵循现有的告警服务模式
- **性能监控**: 集成现有的监控和追踪系统

### 自定义组件系统
- **组件继承**: 继承自BaseCustomComponent或CustomComponent
- **输入输出**: 定义清晰的inputs和outputs
- **生命周期**: 遵循组件的构建、执行、完成生命周期
- **图计算**: 理解Vertex和Graph的关系和执行模式

### LLM集成
- **Langchain**: 使用现有的langchain_core集成模式
- **异步处理**: LLM调用必须是异步的
- **错误处理**: 处理LLM服务不可用的情况
- **内存管理**: 注意长对话的内存使用

## 🔒 安全与最佳实践

### 安全规范
- **输入验证**: 所有外部输入都必须验证
- **SQL注入**: 使用参数化查询，避免字符串拼接
- **密钥管理**: 使用环境变量存储敏感信息
- **身份验证**: 遵循现有的身份验证和授权模式

### 性能优化
- **异步操作**: 优化异步操作的并发性
- **数据库查询**: 优化数据库查询，避免N+1问题
- **缓存策略**: 合理使用缓存减少重复计算

### 错误处理
- **异常传播**: 正确处理异步操作中的异常
- **用户友好**: 提供清晰的错误信息
- **日志记录**: 记录详细的错误信息用于调试

## 📁 文件结构规范

### 源码组织 (@/src)
```
src/
├── magicflow/
│   ├── domain/          # 领域层
│   ├── infrastructure/  # 基础设施层
│   ├── interfaces/      # 接口层
│   └── application/     # 应用层
├── config/             # 配置文件
└── app.py             # 应用入口
```

### 测试组织 (@/test)
```
test/
├── test_src/
│   ├── magicflow/      # 对应src结构
│   ├── conftest.py     # 测试配置
│   └── fake/           # 测试数据
```

### 知识库组织 (@/知识库)
```
知识库/
├── 提示信息文档/
│   ├── 需求文档.md     # 需求和验收准则
│   └── 开发方案.md     # TDD开发方案模版
└── [其他需求文档...]
```

## 🚀 开发流程

### 新功能开发流程
1. **[模式: 研究]** 理解现有代码结构和相关组件，读取知识库相关需求
2. **[模式: 创新]** 设计解决方案和架构方案
3. **[模式: 规划]** 制定详细的TDD开发计划，生成开发方案文档
4. **[模式: 执行]** 按TDD流程实现功能，确保测试覆盖率90%+
5. **[模式: 审查]** 验证实现与计划的一致性，确保通过所有TODOLIST

### 模式切换信号
只有在收到以下确切信号时才切换模式：
- "进入研究模式"
- "进入创新模式"
- "进入规划模式"
- "进入执行模式"
- "进入审查模式"

### TDD质量检查
- **测试优先**: 必须先编写测试，再编写实现
- **覆盖率验证**: 使用pytest --cov验证覆盖率达到90%以上
- **TODOLIST完成**: 所有TDD清单项目必须通过
- **验收验证**: 最终代码必须满足原始需求的验收准则

## ⚠️ 重要提醒

1. **模式声明**: 每次回答必须以 [模式: 模式名称] 开始
2. **中文回答**: 所有回答都必须使用中文
3. **严格遵循**: 不得偏离RIPER-5模式的协议
4. **TDD强制**: 必须遵循TDD开发流程，测试优先
5. **知识库集成**: 规划模式时必须参考知识库中的需求文档
6. **覆盖率要求**: 执行模式时UT覆盖率必须达到90%以上
7. **验收验证**: 最终代码必须通过所有TODOLIST并满足验收准则

---

*此规则文件专为基于FastAPI的领域驱动设计项目定制，结合RIPER-5模式和TDD开发流程，确保高质量的代码开发和知识库集成。*