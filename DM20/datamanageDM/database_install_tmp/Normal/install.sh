#!/bin/bash
INSTALL_FLAG='replace'

if [ $# != 1 ] ; then 
    echo "USAGE: $0 gpdb_package" 
    echo " e.g.: $0 ../gpdb-6.0.tar" 
    echo "[ERROR]Please input gpdb package path"
    exit
fi 

gpdb_package=$1
tar xvf $gpdb_package

current_path=`pwd`;
host_file='host.cfg'
dos2unix "$host_file"

arr_IP=($(awk '{print $2}' ${host_file}))
arr_Host=($(awk '{print $3}' ${host_file}))
arr_Dirs=($(awk '{print $4}' ${host_file}))
master_dir=$(echo "${arr_Dirs[0]}" |awk -F"," '{print $1}')

size_host=${#arr_IP[*]}

dba_user=gpadmin;
dba_group=gpadmin;
gpadmin_home=/home/<USER>

umask 022

maxuid=550
ifMaxUidExist=`grep -i $maxuid /etc/passwd | wc -l`
while [ $ifMaxUidExist -ne 0 ]
do
    let maxuid++
    ifMaxUidExist=`grep -i $maxuid /etc/passwd | wc -l`
done

CheckDiskFree(){
    echo "Begin Check Disk Avalible Size"
    
    vSize=`df -m / |grep /$|awk '{for (i = 1; i<NF; i++) { if( match($i,/%$/) )  print $(i-1)}}'`
    
    echo "Disk Avalible Size: ${vSize} MB "
    if [ $vSize -lt 2000 ]; then 
        echo "Disk space (/) must be greater than 2000MB"
        exit
    fi
    
    echo "End Check Disk Avalible Size"
} 
#gpadmin_default=""
CheckDBAUser(){
    echo "Begin Init User Info: "${dba_user}

    if [[ $(id -nu $dba_user) = $dba_user ]]; then
        echo "user '$dba_user' already exists,need recreate $dba_user"
    read -t 100 -p "Do you want to auto remove the $dba_user account data and continue install greenplum.[N/Y]:" CONTINUE_INSTALL
        if [ "$CONTINUE_INSTALL" == "Y" ] || [ "$CONTINUE_INSTALL" == "y" ] 
        then
            su - $dba_user -c "gpstop -a -M fast"
            pkill -9 -u $dba_user  
            rm -rf /tmp/.s.PGSQL.15432*
            rm -rf /tmp/.s.PGSQL.4*
            userdel  -r  -f  $dba_user  
        else
            echo "user '$dba_user' already exists,need recreate $dba_user, first backup /home/<USER>"
            echo "userdel  -r  -f  $dba_user"        
            echo "Terminal INSTALL"   
            exit 1
        fi
    fi
        echo "to add user '$dba_user'"
    if [[ $(cat /etc/group|grep -c ${dba_group}) -lt 1 ]]; then
        groupadd $dba_group
    fi
        useradd -u $maxuid -g $dba_group -G $dba_group -m -r -d /home/<USER>
    echo "umask 022" >> /home/<USER>/.bashrc
    chmod +w /etc/shadow
    cp -p /etc/shadow /etc/shadow.bak
    #save ciphertext
    str='$6$9gOVRUHXem/QnF$8V9FTjC7Q5Z5XO.oMSKgOsx4OXUHqLtY8oUgd5fnNWZ3d91GlEzUf4Vj0iimwwkw9myKMC07AhTVky3.RVz8r1:17638:0:99999:7:::'
    #split string and save suffix
    OLD_IFS="$IFS"
    IFS=":"
    arr=(`cat /etc/shadow|grep gpadmin`)
    IFS="$OLD_IFS"
    #replace existing password
    sed -e "s#$dba_user:.*:#$dba_user:$str#g" /etc/shadow.bak > /etc/shadow
    rm -f /etc/shadow.bak

    echo "Sunccess Init User Info: "${dba_user}
} 

if [[ "$INSTALL_FLAG" == "replace" ]]; then
    CheckDiskFree
    CheckDBAUser
fi

SetNTP(){
    echo "Begin SetNTP"
    REDHAT_VERSION=$(rpm -q redhat-release-server)
    source /home/<USER>/gpdb/greenplum_path.sh
    
    MasterTime=`date '+%Y-%m-%d %H:%M:%S'` 
    
    echo ${MasterTime}
    
    gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e date -s \'${MasterTime}\'
    rm -rf /etc/ntp.conf
    \cp ntp_master.cfg /etc/ntp.conf
    if [[ "$REDHAT_VERSION" =~ "el7" ]]; then
        systemctl stop ntpd
        kill -9 $(pgrep ntpd) >/dev/null 2>&1
        systemctl start ntpd
        systemctl enable ntpd
    else
        service ntpd stop
        kill -9 $(pgrep ntpd) >/dev/null 2>&1
        service ntpd start
        chkconfig --level 35 ntpd on
    fi

    if [ -f "${gpadmin_home}/tmp_hosts_seg.cc" ]; then
        \cp ntp_slave.cfg ${gpadmin_home}/ntp.cc
        echo "restrict default kod nomodify notrap nopeer noquery" >> ${gpadmin_home}/ntp.cc
        echo "restrict -6 default kod nomodify notrap nopeer noquery" >> ${gpadmin_home}/ntp.cc
        echo "server "${arr_IP[0]} >> ${gpadmin_home}/ntp.cc
        gpscp -f ${gpadmin_home}/tmp_hosts_seg.cc ${gpadmin_home}/ntp.cc =:/etc/ntp.conf

        cmdStr="if [[ \"\$(rpm -q redhat-release-server)\" =~ \"el7\" ]];then { systemctl stop ntpd; kill -9 \$(pgrep ntpd) >/dev/null 2>&1; ntpdate -u ${arr_IP[0]}; systemctl start ntpd; systemctl enable ntpd; } else { service ntpd stop; kill -9 \$(pgrep ntpd) >/dev/null 2>&1; ntpdate -u ${arr_IP[0]}; service ntpd start; chkconfig --level 35 ntpd on; } fi"
        gpssh -f ${gpadmin_home}/tmp_hosts_seg.cc -v -e $cmdStr
    fi

    echo "End SetNTP"
}

SegmentInstall(){
    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'mkdir /home/<USER>'
    gpscp -f /home/<USER>/tmp_hosts_seg.cc ./OSCheck.sh =:/home/<USER>/OSCheck.sh
    gpscp -f /home/<USER>/tmp_hosts_seg.cc ./RemoteInitDBUser.sh =:/home/<USER>/RemoteInitDBUser.sh

    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'bash /home/<USER>/OSCheck.sh'
    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'bash /home/<USER>/RemoteInitDBUser.sh gpadmin gpadmin'


    tar -zcvf gpdb.tar.gz /home/<USER>/gpdb
    gpscp -f /home/<USER>/tmp_hosts_seg.cc ./gpdb.tar.gz =:/home/<USER>/gpdb.tar.gz
    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'cd /; tar -zxvf /home/<USER>/gpdb.tar.gz > /dev/null; rm -rf /home/<USER>/'

    mkdir /home/<USER>/.ssh
    cp /root/.ssh/known_hosts /home/<USER>/.ssh/
    chown -R gpadmin:gpadmin /home/<USER>/.ssh/

    su - gpadmin -c "ssh-keygen -f /home/<USER>/.ssh/id_rsa -t rsa -N ''"
  
    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'mkdir /home/<USER>/.ssh'
    gpscp -f /home/<USER>/tmp_hosts_seg.cc /home/<USER>/.ssh/id_rsa.pub =:/home/<USER>/.ssh/authorized_keys
    gpssh -f /home/<USER>/tmp_hosts_seg.cc -v -e 'chown -R gpadmin:gpadmin /home/<USER>/.ssh/'

    su - gpadmin -c "gpssh-exkeys -f /home/<USER>/tmp_hosts_file.cc"
}

InstallPackage(){
    echo "Begin Build SSH"
    rm -rf ${gpadmin_home}/*.cc

    
    rm -rf ./*.rc
    mv /etc/hosts /etc/hosts.bak
 
    echo "127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4" >> /etc/hosts 
    echo "::1         localhost localhost.localdomain localhost6 localhost6.localdomain6" >> /etc/hosts 

    echo ${arr_IP[0]} ${arr_Host[0]} >> /etc/hosts 
    echo ${arr_Host[0]} | tr A-Z a-z >> ${gpadmin_home}/tmp_hosts_file.cc

    for ((i=1; i<$size_host; i++));  
    do  
        if [ ${arr_Host[$i]} = ${arr_Host[0]} ]; then
            continue
        fi
        if [ ${arr_Host[$i]} = ${arr_Host[$((i-1))]} ]; then
            continue
        fi
        
        echo ${arr_IP[$i]} ${arr_Host[$i]} >> /etc/hosts 
        echo ${arr_Host[$i]} | tr A-Z a-z >> ${gpadmin_home}/tmp_hosts_file.cc 
        echo ${arr_Host[$i]} | tr A-Z a-z >> ${gpadmin_home}/tmp_hosts_seg.cc 
    done 
    
    chmod 644 /etc/hosts

    if [[ "$INSTALL_FLAG" == "replace" ]]; then
        cp -r gpdb /home/<USER>/
    fi
    
    
    
    #数据管理安装和分发 
    [ ! -d "dm" ] && mkdir "dm"
    if [ -f datamanage-service.zip ]
    then
    mv  datamanage-service.zip  dm
    mv  JDK.zip   dm
    fi
    cp -r ./dm  ${gpadmin_home}/gpdb/ext/
    
    #修改DM的配置文件，记录master ip和所有 segment ip，通过下一步广播出去
    rm -rf /home/<USER>/gpdb/lib/libcrypto.*
    rm -rf /home/<USER>/gpdb/lib/libssl.so.*
    
    libcryptopath=`ldd /usr/bin/ssh | grep libcrypto | grep  -e '=>' | awk '{ print ($3)}'  | grep "so"`

    /bin/cp $libcryptopath /home/<USER>/gpdb/lib/
    /bin/cp $libcryptopath /home/<USER>/gpdb/lib/libcrypto.so.1.0.0
    /bin/cp $libcryptopath /home/<USER>/gpdb/lib/libcrypto.so.10
    /bin/cp $libcryptopath /home/<USER>/gpdb/lib/libcrypto.so    
    
    libsslpath=`ldd /usr/bin/openssl | grep libssl | grep  -e '=>' | awk '{ print ($3)}'  | grep "so"`

    /bin/cp $libsslpath /home/<USER>/gpdb/lib/libssl.so.1.0.0
    /bin/cp $libsslpath /home/<USER>/gpdb/lib/libssl.so.10
    /bin/cp $libsslpath /home/<USER>/gpdb/lib/libssl.so
    
    libcurlpath=`ldd /usr/bin/curl | grep libcurl | grep  -e '=>' | awk '{ print ($3)}'  | grep "so"`
    
    /bin/cp $libcurlpath /home/<USER>/gpdb/lib/
    
    chown gpadmin:gpadmin /home/<USER>/gpdb -R
    sed  -i  "s#^GPHOME.*#GPHOME=/home/<USER>/gpdb#"  /home/<USER>/gpdb/greenplum_path.sh
    
    dba_bash_profile=/home/<USER>/.bash_profile
    sed -i "/greenplum_path.sh/d"  $dba_bash_profile
    sed -i "/MASTER_DATA_DIRECTORY/d"  $dba_bash_profile
    sed -i "/PGPORT/d"  $dba_bash_profile
    sed -i "/PGUSER/d"  $dba_bash_profile
    sed -i "/PGDATABASE/d" $dba_bash_profile
    echo "source /home/<USER>/gpdb/greenplum_path.sh" >> $dba_bash_profile
    echo "export MASTER_DATA_DIRECTORY=$master_dir/NetMax_seg-1" >> $dba_bash_profile      
    echo "export PGPORT=15432" >>  $dba_bash_profile
    echo "export PGUSER=gpadmin" >>  $dba_bash_profile
    echo "export PGDATABASE=netmaxdb" >>  $dba_bash_profile
    source /home/<USER>/gpdb/greenplum_path.sh

    if [ -f "${gpadmin_home}/tmp_hosts_seg.cc" ]; then
        if [ ! -f  /root/.ssh/id_rsa ]; then
            ssh-keygen -f /root/.ssh/id_rsa -t rsa -N ''
        fi

        for asegment in `cat ${gpadmin_home}/tmp_hosts_seg.cc`
        do
            ssh-copy-id -i /root/.ssh/id_rsa $asegment
        done
    fi

    gpssh-exkeys -f ${gpadmin_home}/tmp_hosts_file.cc;
    gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e 'echo "umask 022" >> /root/.bashrc'
    SetNTP
    gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e 'echo "umask 022" >> /home/<USER>/.bashrc'
    if [ -f "${gpadmin_home}/tmp_hosts_seg.cc" ]; then
        gpscp -f ${gpadmin_home}/tmp_hosts_seg.cc ./GPCheck.sh =:/home/<USER>
        gpssh -f ${gpadmin_home}/tmp_hosts_seg.cc -e 'bash /home/<USER>/home/<USER>'
    
        gpscp -f ${gpadmin_home}/tmp_hosts_seg.cc /etc/hosts =:/etc/hosts 
        SegmentInstall
        SegmentInstall_Flag="1"
    else
        lowerName=$(echo ${arr_Host[1]})
        su - $dba_user -c "gpssh-exkeys -h $lowerName"
      gpscp -f ${gpadmin_home}/tmp_hosts_file.cc ./OSCheck.sh =:/home/<USER>/OSCheck.sh
      gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e 'bash /home/<USER>/OSCheck.sh'
    fi 
    if [ -f "${gpadmin_home}/tmp_hosts_seg.cc" ]; then
        gpscp -f ${gpadmin_home}/tmp_hosts_seg.cc $dba_bash_profile =:$dba_bash_profile
    fi
    
    gpscp -f ${gpadmin_home}/tmp_hosts_file.cc ./"${host_file}" =:/home/<USER>/host.cfg
    echo "End Build SSH"
}
SegmentInstall_Flag="0"

InstallPackage

Makedir(){
    echo "Begin Makedir"
    source /home/<USER>/gpdb/greenplum_path.sh

    lasthost=${arr_Host[0]}
    dir=""

    for ((i=0; i<${size_host}; i++));
    do
        newhost=${arr_Host[i]}
        #newdir=${arr_Dirs[i]}
        #for GP6. only use master hot/warm/cold config!
        newdir=${arr_Dirs[0]}
        if [ "$lasthost" = "$newhost" ]; then
            dir="$newdir"
        else
            gpssh -h $lasthost -e -v 'rm -rf {'$dir',}; mkdir -pv {'$dir',}; chown '${dba_user}':'${dba_user}' {'$dir',};'
            lasthost=$newhost
            dir="$newdir"
        fi
    done
    gpssh -h $lasthost -e -v 'rm -rf {'$dir',}; mkdir -pv {'$dir',}; chown '${dba_user}':'${dba_user}' {'$dir',};'

    echo "End Makedir"
}

Makedir

if [ "$SegmentInstall_Flag" == "3" ]; then
    exit
fi

InstallServer(){
    echo "Begin Install GPDB"
    OLD_IFS="$IFS" 
    IFS=","
    source /home/<USER>/gpdb/greenplum_path.sh

    cp ./gpdb/docs/cli_help/gpconfigs/gpinitsystem_config ${gpadmin_home}/gpinitsystem_config.cc
     
    sed -i "/SEG_PREFIX/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "SEG_PREFIX=NetMax_seg" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/MASTER_PORT/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "MASTER_PORT=15432" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/CHECK_POINT_SEGMENTS/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "CHECK_POINT_SEGMENTS=64" >>${gpadmin_home}/gpinitsystem_config.cc
    echo "MASTER_MAX_CONNECT=150" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/DATABASE_NAME/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "DATABASE_NAME=netmaxdb" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/MASTER_HOSTNAME/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "MASTER_HOSTNAME=${arr_Host[0]}" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/MASTER_DIRECTORY/d"  ${gpadmin_home}/gpinitsystem_config.cc
    echo "MASTER_DIRECTORY=$master_dir" >>${gpadmin_home}/gpinitsystem_config.cc
    sed -i "/DATA_DIRECTORY/d"  ${gpadmin_home}/gpinitsystem_config.cc
    
    #arr=(${arr_Dirs[1]})
    #for GP6. only use master hot/warm/cold config!
    arr=(${arr_Dirs[0]})
    seg_dir=${arr[0]}
    echo "$seg_dir"
    
    echo  "declare -a DATA_DIRECTORY=("$seg_dir")" >>${gpadmin_home}/gpinitsystem_config.cc    
    
    echo ${arr_Host[1]} >> ${gpadmin_home}/tmp_host_install.cc
    
    chown gpadmin:gpadmin ${gpadmin_home}/tmp_host_install.cc
    chown gpadmin:gpadmin ${gpadmin_home}/gpinitsystem_config.cc
    su - $dba_user -c "gpinitsystem -a -c gpinitsystem_config.cc -h tmp_host_install.cc"
    cp $master_dir/NetMax_seg-1/pg_hba.conf $master_dir/NetMax_seg-1/pg_hba.conf.md5
    
    echo  'host       netmaxdb       netmax   0.0.0.0/0    md5'   >>$master_dir/NetMax_seg-1/pg_hba.conf.md5
    echo  'host       all       gpadmin  0.0.0.0/0    md5'   >>$master_dir/NetMax_seg-1/pg_hba.conf.md5
    echo  'host       netmaxdb       netmax   ::/0    md5'   >>$master_dir/NetMax_seg-1/pg_hba.conf.md5
    echo  'host       all       gpadmin  ::/0    md5'   >>$master_dir/NetMax_seg-1/pg_hba.conf.md5
    
    echo  'host       netmaxdb       netmax   0.0.0.0/0    trust'   >>$master_dir/NetMax_seg-1/pg_hba.conf
    echo  'host       all       gpadmin  0.0.0.0/0    trust'   >>$master_dir/NetMax_seg-1/pg_hba.conf
    echo  'host       netmaxdb       netmax   ::/0    trust'   >>$master_dir/NetMax_seg-1/pg_hba.conf
    echo  'host       all       gpadmin  ::/0    trust'   >>$master_dir/NetMax_seg-1/pg_hba.conf

    su - $dba_user -c "gpstop -u"
    IFS="$OLD_IFS"
    echo "End Install GPDB"
}
InstallServer

ExpendServer(){
    echo "Begin Expend GPDB"
    if [ $size_host -eq 2 ]; then
        gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e "sed -i '/umask/d' /root/.bashrc"
        rm -rf ${gpadmin_home}/*.cc
        echo "End Expend GPDB"
        return
    fi
    
    source /home/<USER>/gpdb/greenplum_path.sh
    
    OLD_IFS="$IFS"
    IFS=","
    base_id=1
    base_port=40001

    lasthost=${arr_Host[1]}
    
    for ((i=2; i<$size_host; i++));  
    do  
        newhost=${arr_Host[$i]}
        
        if [ "$newhost" != "$lasthost" ]; then
            lasthost=$newhost
            base_port=40001
        fi
        #dirs=(${arr_Dirs[i]})
        #for GP6. only use master hot/warm/cold config!
        dirs=(${arr_Dirs[0]})
        dir=${dirs[0]}
        
        echo "${newhost}|${newhost}|$[$base_port]|$dir/NetMax_seg${base_id}|$[$base_id + 2]|${base_id}|p" >> ${gpadmin_home}/tmp_expend.cc
        base_id=$[$base_id + 1]
        base_port=$[$base_port + 1]
        
    done
    
    chown gpadmin:gpadmin ${gpadmin_home}/tmp_expend.cc
    
    su - $dba_user -c "gpexpand -i ${gpadmin_home}/tmp_expend.cc";
    su - $dba_user -c "gpexpand -d 10:00:00  -n 16";
    su - $dba_user -c "echo y >> gpexpand -c";
    
    gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e "sed -i '/umask/d' /root/.bashrc"
    #rm -rf ${gpadmin_home}/*.cc
    
    IFS="$OLD_IFS"
    
    echo "End Expend GPDB"
}
ExpendServer

ChangeLogin2Md5(){
    mv $master_dir/NetMax_seg-1/pg_hba.conf $master_dir/NetMax_seg-1/pg_hba.conf.trust 
    mv $master_dir/NetMax_seg-1/pg_hba.conf.md5 $master_dir/NetMax_seg-1/pg_hba.conf
    
    su - $dba_user -c "gpstop -u"
    su - $dba_user -c "psql -c \"ALTER ROLE gpadmin with DDDDDD '$gpadmin_default'\""
    
    rm -rf $master_dir/NetMax_seg-1/pg_hba.conf.trust
    
    echo 'DB Configure Over'
}
ChangeLogin2Md5

InitInfo(){
    OLD_IFS="$IFS"
    IFS=","
    
    CurrenTime=`date '+%Y-%m-%d %H:%M:%S'`

    su - $dba_user -c "psql -c \"CREATE TABLE GPDBINFO(ROLE TEXT, IP TEXT, HOSTNAME TEXT, PORT INT, TIME TIMESTAMP, DIR TEXT)\" >/dev/null 2>&1"
    
    su - $dba_user -c "psql -c \"INSERT INTO GPDBINFO VALUES('MASTER', '${arr_IP[0]}', '${arr_Host[0]}', 15432, '${CurrenTime}', '$master_dir/NetMax_seg-1')\" 2>&1 >/dev/null"
    
    base_id=0
    lasthost=""

    for ((i=1; i<$size_host; i++));  
    do  
        newhost=${arr_Host[$i]}
        
        if [ "$newhost" != "$lasthost" ]; then
            lasthost=$newhost
            base_port=40000
        fi
        #dirs=(${arr_Dirs[i]})
        #for GP6. only use master hot/warm/cold config!
        dirs=(${arr_Dirs[0]})
        dir=${dirs[0]}
        
        su - $dba_user -c "psql -c \"INSERT INTO GPDBINFO VALUES('SLAVE', '${arr_IP[$i]}', '${arr_Host[$i]}', $base_port, '${CurrenTime}', '$dir/NetMax_seg${base_id}')\" 2>&1 >/dev/null"

        base_id=$[$base_id + 1]
        base_port=$[$base_port + 1]
        
    done
    
    IFS="$OLD_IFS"
}
InitInfo


g_dir=''
GetDataDir(){
    OLD_IFS="$IFS"
    IFS=","
    #dirs=(${arr_Dirs[$1]})
    #for GP6. only use master hot/warm/cold config!
    dirs=(${arr_Dirs[0]})
    size_dirs=${#dirs[*]}
    size_dirs=$[$size_dirs - 1]
    if [ $2 -gt $size_dirs ]; then
       echo "$host_file is wrong!"
       exit -2
    fi
    g_dir=${dirs[$2]}
    IFS="$OLD_IFS"
}
CreateTablespaces(){
    echo "Begin create tablespaces."
    echo "only use master hot/warm/cold config!"
    source /home/<USER>/gpdb/greenplum_path.sh
    hostname=(${arr_Host[0]})

    GetDataDir 0 1 #hotdata
    hotdatadir=$g_dir
    echo "hottablespace location :   $hotdatadir "

    GetDataDir 0 2 #warmdata
    warmdatadir=$g_dir
    echo "warmtablespace location :   $warmdatadir "


    GetDataDir 0 3 #colddata
    colddata=$g_dir
    echo "coldtablespace location :   $colddata "

    su - $dba_user -c "psql -c \"Create TableSpace hottablespace   LOCATION '$hotdatadir'; \""
    su - $dba_user -c "psql -c \"Create TableSpace warmtablespace   LOCATION '$warmdatadir'; \""
    su - $dba_user -c "psql -c \"Create TableSpace coldtablespace   LOCATION '$colddata'; \""

    echo 'End create tablespaces.'
}
CreateTablespaces


SetDefaultTablespace(){
    echo 'Begin set default tablespace.'
    
    source /home/<USER>/gpdb/greenplum_path.sh
    
    su - $dba_user -c "gpconfig -c default_tablespace -v warmtablespace"
    su - $dba_user -c "gpstop -u"
    echo 'End set default tablespace.'
}
SetDefaultTablespace

InstallMADlib(){
    echo 'Begin install MADlib.'

    source /home/<USER>/gpdb/greenplum_path.sh
    cd ${gpadmin_home}/gpdb/madlib/bin/
    ./madpack install -s madlib -p greenplum -c gpadmin@127.0.0.1:15432/netmaxdb

    echo 'End install MADlib.'

}
#InstallMADlib 

runuser -l gpadmin -c "psql -c 'CREATE LANGUAGE plpythonu'"

cp addfun_string_agg.sql $gpadmin_home
chown gpadmin:gpadmin $gpadmin_home/addfun_string_agg.sql
runuser -l gpadmin -c "psql < $gpadmin_home/addfun_string_agg.sql"

gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e 'chown gpadmin.gpadmin -R /home/<USER>'

#chown gpadmin.gpadmin -R $gpadmin_home
echo 'Install Over'

echo `pwd`


#install datamanage 
install_datamanage(){
cmd="cd ${gpadmin_home}/gpdb/ext/dm;
     unzip  datamanage-service.zip;
     unzip JDK.zip
     cp -r ./jdk/linux/*  jdk
     rm -rf ./jdk/linux
     rm -rf ./jdk/aix
     rm -rf ./jdk/Clean.bat
     chmod a+x ./InstallDatamanage.sh;  
    ./InstallDatamanage.sh   ${gpadmin_home}/gpdb/ext/dm   ${gpadmin_home};
     rm -rf  InstallDatamanage.sh;
     rm -rf  JDK.zip;
     rm -rf  datamanage-service.zip;"
    sudo su - ${dba_user} -c "gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -f ${gpadmin_home}/tmp_hosts_file.cc -v -e '${cmd}'"
    fi
}
install_datamanage


#install gpinstance
install_gp_instance(){
sudo su - ${dba_user} -c "cd ${gpadmin_home}/gpdb/ext/dm/DB;
                          chmod a+x ./InstallInstance_gp.sh;
                          ./InstallInstance_gp.sh;"
}
install_gp_instance
#启动数据管理服务
start_datamanage(){
su - ${dba_user} -c "cd ${gpadmin_home}/gpdb/ext/dm;
chmod +x ./gpsshDatamanageStart.sh
./gpsshDatamanageStart.sh"
}
start_datamanage
rm -rf gpdb
rm -f gpdb.tar.gz 
rm -rf ./dm/JDK.zip
rm -rf ./dm/datamanage-service.zip
