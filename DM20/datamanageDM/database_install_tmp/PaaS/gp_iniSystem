#!/bin/bash



#解析集群IP
IFS="," 
arr_IP=($nodeIPList)
arr_Size=${#arr_IP[*]}

log=/home/<USER>/install.log

SetProfile(){
    echo "`date`: begin SetProfile" >>$log
    dba_bash_profile=/home/<USER>/.bash_profile
    sed -i "/greenplum_path.sh/d"  $dba_bash_profile
    sed -i "/MASTER_DATA_DIRECTORY/d"  $dba_bash_profile
    sed -i "/PGPORT/d"  $dba_bash_profile
    sed -i "/PGUSER/d"  $dba_bash_profile
    sed -i "/PGDATABASE/d" $dba_bash_profile
    echo "source ${gpdb_path}/greenplum_path.sh" >> $dba_bash_profile
    echo "export MASTER_DATA_DIRECTORY=$installpath/ngi_seg-1" >> $dba_bash_profile      
    echo "export PGPORT=$master_port" >>  $dba_bash_profile
    echo "export PGUSER=$dba_user" >>  $dba_bash_profile
    echo "export PGDATABASE=$ngi_db" >>  $dba_bash_profile
    echo "export LOG_DIR=/home/<USER>/gpAdminLogs" >>  $dba_bash_profile
    echo "`date`: end SetProfile" >>$log
}
SetProfile

CreateConf(){
    echo "`date`: begin CreateConf" >>$log
    source /home/<USER>/.bash_profile
    
    cp ${gpdb_path}/docs/cli_help/gpconfigs/gpinitsystem_config /home/<USER>/gpinitsystem_config
     
    sed -i "/SEG_PREFIX/d"                    /home/<USER>/gpinitsystem_config
    echo "SEG_PREFIX=ngi_seg"               >>/home/<USER>/gpinitsystem_config
    sed -i "/MASTER_PORT/d"                   /home/<USER>/gpinitsystem_config
    echo "MASTER_PORT=${master_port}"       >>/home/<USER>/gpinitsystem_config
    sed -i "/CHECK_POINT_SEGMENTS/d"          /home/<USER>/gpinitsystem_config
    echo "CHECK_POINT_SEGMENTS=64"          >>/home/<USER>/gpinitsystem_config
    echo "MASTER_MAX_CONNECT=150"           >>/home/<USER>/gpinitsystem_config
    sed -i "/DATABASE_NAME/d"                 /home/<USER>/gpinitsystem_config
    echo "DATABASE_NAME=$ngi_db"            >>/home/<USER>/gpinitsystem_config
    sed -i "/PORT_BASE/d"                     /home/<USER>/gpinitsystem_config
    echo "PORT_BASE=$seg_port"              >>/home/<USER>/gpinitsystem_config
    sed -i "/MASTER_HOSTNAME/d"               /home/<USER>/gpinitsystem_config
    echo "MASTER_HOSTNAME=`hostname`"       >>/home/<USER>/gpinitsystem_config
    sed -i "/MASTER_DIRECTORY/d"              /home/<USER>/gpinitsystem_config
    echo "MASTER_DIRECTORY=$installpath"    >>/home/<USER>/gpinitsystem_config
    sed -i "/DATA_DIRECTORY/d"                /home/<USER>/gpinitsystem_config
    
    seg_dir=${installpath}
    for ((i=1; i<${seg_count}; i++));  
    do  
        seg_dir="${seg_dir},${installpath}"
    done 
    echo  "declare -a DATA_DIRECTORY=("${seg_dir}")" >>/home/<USER>/gpinitsystem_config 
    
    echo "`date`: end CreateConf" >>$log
}
CreateConf

InstallGPInstance(){
    echo "`date`: begin InstallGPInstance" >>$log
    source /home/<USER>/.bash_profile
    
    echo ${arr_IP[0]} >>/home/<USER>/master_host
    
    for ((i=0; i<${arr_Size}; i++));  
    do  
        echo ${arr_IP[$i]}>>/home/<USER>/host_seg
    done 
    
    gpinitsystem -a -c /home/<USER>/gpinitsystem_config -h /home/<USER>/host_seg >>$log
    if [ `echo $?` -ne 0 ];then
        gpinitsystem -a -c /home/<USER>/gpinitsystem_config -h /home/<USER>/host_seg >>$log
    fi 
 
    rm -rf /home/<USER>/master_host
    rm -rf /home/<USER>/host_seg
    rm -rf /home/<USER>/gpinitsystem_config
    
    cp $installpath/ngi_seg-1/pg_hba.conf $installpath/ngi_seg-1/pg_hba.conf.md5
    
    echo  'host       all       ngi      0.0.0.0/0    md5'      >>$installpath/ngi_seg-1/pg_hba.conf.md5
    echo  'host       all       gpadmin  0.0.0.0/0    md5'      >>$installpath/ngi_seg-1/pg_hba.conf.md5
    echo  'host       all       ngi      ::/0         md5'      >>$installpath/ngi_seg-1/pg_hba.conf.md5
    echo  'host       all       gpadmin  ::/0         md5'      >>$installpath/ngi_seg-1/pg_hba.conf.md5
    echo  'local      all       gpadmin  ident'                 >>$installpath/ngi_seg-1/pg_hba.conf.md5
    echo  'local      all       ngi      ident'                 >>$installpath/ngi_seg-1/pg_hba.conf.md5
	echo  'host       all       netmax      0.0.0.0/0    md5'      >>$installpath/ngi_seg-1/pg_hba.conf.md5
    
    echo  'host       all       ngi      0.0.0.0/0    trust'    >>$installpath/ngi_seg-1/pg_hba.conf
    echo  'host       all       gpadmin  0.0.0.0/0    trust'    >>$installpath/ngi_seg-1/pg_hba.conf
    echo  'host       all       ngi      ::/0         trust'    >>$installpath/ngi_seg-1/pg_hba.conf
    echo  'host       all       gpadmin  ::/0         trust'    >>$installpath/ngi_seg-1/pg_hba.conf
    echo  'local      all       gpadmin  ident'                 >>$installpath/ngi_seg-1/pg_hba.conf
    echo  'local      all       ngi      ident'                 >>$installpath/ngi_seg-1/pg_hba.conf
	echo  'host       all       netmax      0.0.0.0/0    trust'    >>$installpath/ngi_seg-1/pg_hba.conf

    gpstop -u
    
    echo "`date`: end InstallGPInstance" >>$log
}
InstallGPInstance

#PaaS不进行扩展安装，一次安装即可
ExpendServer(){
    echo "`date`: begin ExpendServer" >>$log
    if [ $arr_Size -eq 1 ]; then
        echo "End Expend GPDB"
        return
    fi
    
    source ${gpdb_path}/greenplum_path.sh
    
    base_id=${seg_count}

    for ((i=1; i<$arr_Size; i++));  
    do  
        base_port=0
        
        for ((j=0; j<$seg_count; j++));  
        do  
            echo "${arr_IP[$i]}:${arr_IP[$i]}:$((base_port+seg_port)):${installpath}/ngi_seg${base_id}:$((base_id+2))):${base_id}:p" >> /home/<USER>/gpexpend_config
            base_id=$((base_id+1))
            base_port=$((base_port+1))
        done 
    done
    
    chown $dba_user:$dba_group /home/<USER>/gpexpend_config
    
    gpexpand -i  /home/<USER>/gpexpend_config -D ngi
    gpexpand -d 10:00:00 -D ngi -n 16
    echo y | gpexpand -c -D ngi
    
    rm -rf /home/<USER>/gpexpend_config
    
    echo "`date`: end ExpendServer" >>$log
}
#ExpendServer

ChangeLogin2Md5(){
    echo "`date`: begin ChangeLogin2Md5" >>$log
    source ${gpdb_path}/greenplum_path.sh

    mv $installpath/ngi_seg-1/pg_hba.conf $installpath/ngi_seg-1/pg_hba.conf.trust 
    mv $installpath/ngi_seg-1/pg_hba.conf.md5 $installpath/ngi_seg-1/pg_hba.conf
    
    gpstop -u
    
    psql -c "ALTER ROLE gpadmin with password '$gpadmin_init'"
    
    
    rm -rf $installpath/ngi_seg-1/pg_hba.conf.trust
    
    echo "`date`: end ChangeLogin2Md5" >>$log
}
ChangeLogin2Md5

