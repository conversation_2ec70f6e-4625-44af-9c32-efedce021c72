#!/bin/bash

if [ $# != 3 ] ; then 
    echo "USAGE: $0 install.conf MSB-IP gpdb_package" 
    echo "[ERROR]Please input right parameter"
    exit 1; 
fi 

#install.conf
source $1
#MSB IP
localIp=$2
#GPDB安装包
gpdb_package=$3

#解析集群IP
OLD_IFS=$IFS
IFS="," 
arr_IP=($nodeIPList)
IFS=$OLD_IFS
#数组大小
arr_Size=${#arr_IP[*]}

#数据库安装路径
greenplumpath=($installpath)

#数据库管理员用户
dba_user=gpadmin
dba_group=gpadmin

#数据管理安装包
datamanage_package=datamanage-service.zip
jdk_package=JDK.zip
unzippackage=unzip-6.0-19.el7.x86_64.rpm
#对配置文件中的root用户、ubuntu用户、gpadmin用户的密码进行解密
#解密报错会给默认的密码
ERROR_INFO="error:"
dba_default=`python -c "import ex; print ex.aes256_decrypt('$sys_gpadminPwd')"`
if [[ $dba_default = *$ERROR_INFO* ]] 
then  
  echo "dba_default pwd is error"
  exit 1
fi
sys_sshuserPwd=`python -c "import ex; print ex.aes256_decrypt('$sys_sshuserPwd')"`
if [[ $sys_sshuserPwd = *$ERROR_INFO* ]] 
then  
  echo "sys_sshuserPwd pwd is error"
  exit 1
fi 
sys_rootPwd=`python -c "import ex; print ex.aes256_decrypt('$sys_rootPwd')"`
if [[ $sys_rootPwd = *$ERROR_INFO* ]] 
then  
  echo "sys_rootPwd pwd is error"
  exit 1
fi

#安装工具存放目录
ngi_tools=/usr/local/ngi_tools

#ngi数据库实例名（默认为ngi其实可以从install.conf中读取，目前暂时不管）
ngi_db=netmaxdb

#CPU虚核个数
cpu_count=`cat /proc/cpuinfo| grep "processor"| wc -l`
#数据库集群单台服务器上segment个数（每个segment分配4个核，预留8个给其PaaS等使用）
seg_count=$((cpu_count/4-2))
#判断是否和控制节点安装在一起，如果在一起，则只安装2个segment
if [ "$scene" -eq 2 ]
then
    seg_count=2
fi

#GP数据库master的端口
master_port=15432
#GP数据库segment起始端口
seg_port=40000

#数据库集群master服务器上segment个数（master节点segment个数比slave节点的个数少一个）
mseg_count=$((seg_count-1))

echo "**** NGI GPDB INSTALL INFO"
echo "**** CMD        : $0 $1 $2 $3"
echo "**** TYPE       : $install_type"
echo "**** SCENE      : $scene"
echo "**** DATA PATH  : $installpath"
echo "**** MSB IP     : $localIp"
echo "**** MASTER IP  : ${arr_IP[0]}"
echo "**** IP ARRAY   : $nodeIPList"
echo "**** SEG NUM    : $seg_count"
echo "**** MASTER PORT: $master_port"
echo "**** SEG PORT   : $seg_port"
echo "**** IP ARRAY   : $nodeIPList"
echo "**** DB NAME    : $ngi_db"

#用户输入数据库gpadmin的密码
gpadmin_init=""
while [ -z "$gpadmin_init" ]
do
    read -s -p "Please set database password for user gpadmin: " gpadmin_init
    if [ -z "$gpadmin_init" ];then
        continue
    fi
    check1=$(echo "$gpadmin_init" | tr -d [a-zA-Z_0-9])
    check2=$(echo "$gpadmin_init" | tr -d [_])
    if [ "$check1" ];then
        echo "Error: Passwords can only contain letters, numbers, and underscores."
        gpadmin_init=""
    elif [ -z "$check2" ];then
        echo "Error: Password can not only be underscores."
        gpadmin_init=""
    elif [ ${#gpadmin_init} -lt 8 ];then
        echo "Error: Password length must equals or longer than 8"
        gpadmin_init=""
    elif [ ${#gpadmin_init} -ge 127 ];then
        echo "Error: Password length must less than 127"
        gpadmin_init=""
    fi
done

#安装依赖的RPM包  
#主要是安装expect，自动填写密码
InstallRPM(){
    echo '**** Install RPM'
    sudo su root -c 'cd ./gp_dependence_rpm && 
                     chmod +x *.sh && 
                     ./install_all_rpm.sh'
    echo '**** Install RPM OK'
}
InstallRPM

#安装gpssh工具到${ngi_tools}目录下
InstallTools(){
    echo '**** Install Tools'
    
    cmd="rm -rf ${ngi_tools};  
         cp -r  ngi_tools /usr/local/;  
         chmod 755 ${ngi_tools} -R && 
         echo 'export PATH=$PATH:${ngi_tools}' >>/etc/profile && 
         echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:${ngi_tools}' >>/etc/profile;
         cp gp_dependence_rpm.tar ${ngi_tools}"
         
    echo $cmd     

    sudo su root -c "$cmd"
    echo '**** Install Tools OK'
}
InstallTools

export PATH=$PATH:${ngi_tools}
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:${ngi_tools}

#创建SSH通道需要的配置文件
#host_all GP集群所有节点IP+当前运行环境的hostname
#host_gp GP集群所有节点IP
#gp_adduser.sh 配置用户的ssh通道
#gp_runOSConf.sh 在每个节点上运行操作系统配置参数
CreateConf(){ 
    echo '**** Create Configuration File'
    for ((i=0; i<${arr_Size}; i++));  
    do  
        echo ${arr_IP[$i]}>>./host_all
        echo ${arr_IP[$i]}>>./host_gp
    done 
    echo `hostname` >>./host_all
    
    echo ${arr_IP[0]}>>./paas_hosts_master.cc
    
    for ((i=1; i<${arr_Size}; i++));  
    do  
        echo ${arr_IP[$i]}>>./paas_hosts_seg.cc
    done 
    
    echo $greenplumpath>>./greenplumpath.cc
    
    cmd="mkdir ${ngi_tools}/conf &&
         mv ./host_all ${ngi_tools}/conf &&
         mv ./host_gp  ${ngi_tools}/conf && 
         cp gp_adduser.sh   ${ngi_tools}/conf &&
         cp gp_runOSConf.sh ${ngi_tools}/conf && 
         chmod 755 ${ngi_tools} -R"
    
    sudo su root -c "$cmd"
    echo '**** Create Configuration File OK'
}
CreateConf

#创建用户及用户的SSH通道
#PaaS中root用户无法直接登陆，也无法建立SSH通道
#Paas中的ubuntu用户作为超级用户，具有sudo权限
BuildSSH(){
    echo '**** Build SSH'
  
    echo "****** Build SSH: $sys_sshuser"
    #sys_sshuser创建SSH通道
    #如果创建失败重试一次
    ./gp_sshuser.sh $sys_sshuser $sys_sshuserPwd ${ngi_tools}/conf/host_all
    if [ `echo $?` -ne 0 ];then
        ./gp_sshuser.sh $sys_sshuser $sys_sshuserPwd ${ngi_tools}/conf/host_all
    fi
    
    echo "****** Build SSH: inetrules"
    #设置PaaS防火墙
    #规则为【IP=本机IP前两位+.0.0/16】
    #先删除防火墙设置：inetrules del-rule --srccidr IP --portrange 25,587,1025:65535
    #再增加防火墙设置：inetrules add-rule --srccidr IP --portrange 25,587,1025:65535
    IFS="." 
    ip_parts=(${arr_IP[0]})
    IFS=$OLD_IFS
    new_ip=${ip_parts[0]}'.'${ip_parts[1]}".0.0/16"
    line_info=" --srccidr "$new_ip" --portrange 25,587,1025:65535"

    cmd="sudo inetrules del-rule"$line_info"; ""sudo inetrules add-rule"$line_info
    
    sudo su - ${sys_sshuser} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${sys_sshuser} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi
    
    echo "****** Build SSH: clean old data"
    cmd="sudo pkill -9 -u ${dba_user};sudo find /tmp/ -type f -user ${dba_user} | sudo xargs rm -rf;"
    sudo su - ${sys_sshuser} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${sys_sshuser} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi


    echo "****** Build SSH: Create User:${dba_user}"
    #在所有GP集群服务器上创建gpadmin用户(所用的集群节点包括控制节点)
    sudo su - $sys_sshuser -c "bash ${ngi_tools}/conf/gp_adduser.sh ${ngi_tools}/conf/host_all ${dba_user} $dba_group $dba_default $installpath"
    if [ `echo $?` -ne 0 ];then
        sudo su - $sys_sshuser -c "bash ${ngi_tools}/conf/gp_adduser.sh ${ngi_tools}/conf/host_all ${dba_user} $dba_group $dba_default $installpath"
    fi

    echo "****** Build SSH: ${dba_user}"
    #为gpadmin创建SSH通道
    ./gp_sshuser.sh ${dba_user} $dba_default ${ngi_tools}/conf/host_all 
    if [ `echo $?` -ne 0 ];then
        ./gp_sshuser.sh ${dba_user} $dba_default ${ngi_tools}/conf/host_all 
    fi
    
    #如果GP数据库和控制节点安装在一起，修改操作系统参数较少，否则修改系统参数
    if [ "$scene" -eq 2 ]
    then
        cp gp_OSCheck_s gp_OSCheck.sh
    else
        cp gp_OSCheck_m gp_OSCheck.sh
    fi
    
    #为集群中的每台服务器的/etc/hosts增加hostname信息
    echo "" >>gp_OSCheck.sh
    for ((i=0; i<${arr_Size}; i++));  
    do  
        echo "echo ${arr_IP[$i]} `ssh $sys_sshuser@${arr_IP[$i]} 'hostname'`>>/etc/hosts">>gp_OSCheck.sh
    done 
    echo "rm -rf \$0">>gp_OSCheck.sh
    echo "" >>gp_OSCheck.sh
    
    #复制gp_OSCheck.sh到${ngi_tools}/conf
    cmd="cp gp_OSCheck.sh ${ngi_tools}/conf && 
         chmod 755 ${ngi_tools} -R"
    sudo su root -c "$cmd"
    
    #复制gp_OSCheck.sh到集群每个节点上
    echo "****** Build SSH: scp gp_OSCheck"
    sudo su - $sys_sshuser -c "gpscp -f ${ngi_tools}/conf/host_gp ${ngi_tools}/conf/gp_OSCheck.sh =:/home/<USER>/gp_OSCheck.sh"
    if [ `echo $?` -ne 0 ];then
        sudo su - $sys_sshuser -c "gpscp -f ${ngi_tools}/conf/host_gp ${ngi_tools}/conf/gp_OSCheck.sh =:/home/<USER>/gp_OSCheck.sh"
    fi

    #在集群上每个节点上运行gp_OSCheck.sh
    echo "****** Build SSH: run gp_OSCheck"
    sudo su - $sys_sshuser -c "bash ${ngi_tools}/conf/gp_runOSConf.sh ${ngi_tools}/conf/host_gp /home/<USER>/gp_OSCheck.sh"
    if [ `echo $?` -ne 0 ];then
        sudo su - $sys_sshuser -c "bash ${ngi_tools}/conf/gp_runOSConf.sh ${ngi_tools}/conf/host_gp /home/<USER>/gp_OSCheck.sh"
    fi
    
    rm gp_OSCheck.sh
    
    
    
    #分发GPDB安装包
    echo "****** Build SSH: scp ${gpdb_package}"
    cmd="cp ${gpdb_package} /home/<USER>/ && 
         chown ${dba_user}:$dba_group /home/<USER>/ -R"     
    sudo su root -c "$cmd"
    
    sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${gpdb_package} =:/home/<USER>/${gpdb_package}"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${gpdb_package} =:/home/<USER>/${gpdb_package}"
    fi
    
    #解压GPDB安装包
    echo "****** Build SSH: tar zxf ${gpdb_package}"
    cmd="tar zxf ${gpdb_package} && 
         sed -i \"s#^GPHOME.*#GPHOME=/home/<USER>/ngi-gpdb#\" /home/<USER>/ngi-gpdb/greenplum_path.sh && 
         chmod 755 /home/<USER>/ngi-gpdb -R &&
         rm -rf ${gpdb_package}"

    sudo su - ${dba_user} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi
}
BuildSSH

#进行安装GP数据库
CreateGPConf(){
    echo "**** CreateGPConf"
    cp gp_iniSystem gp_iniSystem.sh

    sed -i "3i\dba_user=${dba_user}"                    gp_iniSystem.sh
    sed -i "3i\gpdb_path=/home/<USER>/ngi-gpdb"    gp_iniSystem.sh
    sed -i "3i\installpath=${installpath}"              gp_iniSystem.sh
    sed -i "3i\master_port=${master_port}"              gp_iniSystem.sh
    sed -i "3i\ngi_db=${ngi_db}"                        gp_iniSystem.sh
    sed -i "3i\nodeIPList=${nodeIPList}"                gp_iniSystem.sh
    sed -i "3i\mseg_count=${mseg_count}"                gp_iniSystem.sh
    sed -i "3i\seg_count=${seg_count}"                  gp_iniSystem.sh
    sed -i "3i\dba_group=${dba_group}"                  gp_iniSystem.sh
    sed -i "3i\seg_port=${seg_port}"                    gp_iniSystem.sh
    sed -i "3i\gpadmin_init=${gpadmin_init}"              gp_iniSystem.sh
    cmd="cp gp_iniSystem.sh /home/<USER>/ && 
         chmod +x /home/<USER>/gp_iniSystem.sh && 
         chown ${dba_user}:$dba_group /home/<USER>/gp_iniSystem.sh"
         
    sudo su root -c "$cmd"
    
    #将gp_iniSystem.sh文件分发到GP的主节点上，这个文件进行安装GP数据库实例
    echo "**** gpscp -h ${arr_IP[0]} /home/<USER>/gp_iniSystem.sh =:/home/<USER>/gp_iniSystem.sh" 
    sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/gp_iniSystem.sh =:/home/<USER>/gp_iniSystem.sh"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/gp_iniSystem.sh =:/home/<USER>/gp_iniSystem.sh"
    fi
    
    echo "**** gpscp -h ${cmd}"
    cmd="bash /home/<USER>/gp_iniSystem.sh"
    sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    fi
    
    rm -rf gp_iniSystem.sh
    
    echo "**** CreateGPConf OK"
}
CreateGPConf

#进行MSB注册
RegMSB(){
    echo "**** RegMSB"

    curl -i -X POST \
     --url http://${localIp}:10081/api/microservices/v1/services?createOrUpdate=false \
     --header "Content-Type: application/json" \
     --data '{"serviceName":"greenplum-master-srv-ngi", "version":"v1", "url":"/", "protocol":"TCP", "visualRange":"0|1", "namespace":"ranoss", "nodes":[{"ip":"'${arr_IP[0]}'", "port":"'${master_port}'"}]}'
    
    echo 'Install Over'
}
RegMSB



#注册数据库卸载信息
RegDel(){
    sudo su - $sys_sshuser -c "gpscp -f ${ngi_tools}/conf/host_gp ${ngi_tools}/gp_dependence_rpm.tar =:/home/<USER>/gp_dependence_rpm.tar"
	#sudo su - $sys_sshuser -c "gpscp -h ${arr_IP[0]} ${ngi_tools}/gp_dependence_rpm.tar =:/home/<USER>/gp_dependence_rpm.tar"
    if [ `echo $?` -ne 0 ];then
        sudo su - $sys_sshuser -c "gpscp -f ${ngi_tools}/conf/host_gp ${ngi_tools}/gp_dependence_rpm.tar =:/home/<USER>/gp_dependence_rpm.tar"
    fi
    
    cmd="tar xf /home/<USER>/gp_dependence_rpm.tar &&
         cd /home/<USER>/gp_dependence_rpm && 
         chmod +x *.sh && 
         sudo su root -c \"bash /home/<USER>/gp_dependence_rpm/install_all_rpm.sh>/dev/null 2>&1\" && 
         rm -rf /home/<USER>/gp_dependence_rpm.tar && 
         rm -rf /home/<USER>/gp_dependence_rpm && 
         rm -rf /home/<USER>/gp_OSCheck.sh "

    sudo su - $sys_sshuser -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - $sys_sshuser -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi
    
    cp  gp_delete  gp_delete.sh
    chmod +x gp_delete.sh
    
    sed -i "2i\set master_path $installpath/ngi_seg-1" gp_delete.sh
    
    cmd="cp gp_delete.sh /home/<USER>/ &&
         chown ${dba_user} /home/<USER>/gp_delete.sh"
    sudo su root -c "$cmd"
    
    rm -rf gp_delete.sh
    
    sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/gp_delete.sh =:/home/<USER>/gp_delete.sh"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/gp_delete.sh =:/home/<USER>/gp_delete.sh"
    fi
    
    cmd="mv ./paas_hosts_master.cc /home/<USER>/ && 
         mv ./paas_hosts_seg.cc /home/<USER>/ && 
         mv ./greenplumpath.cc /home/<USER>/ && 
         chown ${dba_user} /home/<USER>/*.cc"
    sudo su root -c "$cmd"
    
    sudo su - ${dba_user} -c "gpscp -f  ${ngi_tools}/conf/host_gp /home/<USER>/paas_hosts_master.cc =:/home/<USER>/paas_hosts_master.cc"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -f  ${ngi_tools}/conf/host_gp /home/<USER>/paas_hosts_master.cc =:/home/<USER>/paas_hosts_master.cc"
    fi
    sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/paas_hosts_seg.cc =:/home/<USER>/paas_hosts_seg.cc"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -h ${arr_IP[0]} /home/<USER>/paas_hosts_seg.cc =:/home/<USER>/paas_hosts_seg.cc"
    fi
    sudo su - ${dba_user} -c "gpscp -f  ${ngi_tools}/conf/host_gp /home/<USER>/greenplumpath.cc =:/home/<USER>/greenplumpath.cc"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -f  ${ngi_tools}/conf/host_gp /home/<USER>/greenplumpath.cc =:/home/<USER>/greenplumpath.cc"
    fi
}
RegDel


datamanageInstall()
{
  #分发数据管理安装包  
    echo "****** Build SSH: scp ${datamanage_package}"
    cmd="cp ${datamanage_package} /home/<USER>/ "     
    sudo su root -c "$cmd"
    cmd="cp ${jdk_package} /home/<USER>/ "     
    sudo su root -c "$cmd"
    cmd="cp ${unzippackage} /home/<USER>/;
         cp ${datamanage_package} /home/<USER>/"     
    sudo su root -c "$cmd"
    
    sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${datamanage_package} =:/home/<USER>/ngi-gpdb/ext/${datamanage_package}"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${datamanage_package} =:/home/<USER>/ngi-gpdb/ext/${datamanage_package}"
    fi
    
    sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${jdk_package} =:/home/<USER>/ngi-gpdb/ext/${jdk_package}"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${jdk_package} =:/home/<USER>/ngi-gpdb/ext/${jdk_package}"
    fi
    
	 sudo su - ${sys_sshuser} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${unzippackage} =:/home/<USER>/${unzippackage}"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${sys_sshuser} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${unzippackage} =:/home/<USER>/${unzippackage}"
    fi
	
    sudo su - ${sys_sshuser} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${unzippackage} =:/home/<USER>/${unzippackage}"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${sys_sshuser} -c "gpscp -f ${ngi_tools}/conf/host_gp /home/<USER>/${unzippackage} =:/home/<USER>/${unzippackage}"
    fi
     #unzip检测
    cmd="unzippath=\`rpm -qa|grep java\`;
         if [[ ${unzippath} =~ "unzip" ]]
         then
                 rm -rf  /home/<USER>/unzippackage;
         else
                 sudo su - root -c \"rpm -ivh  /home/<USER>/${unzippackage}\";
                 rm -rf  /home/<USER>/unzippackage;
         fi"
    sudo su - $dba_user -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - $dba_user -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi

    #解压数据管理安装包
    echo "****** Build SSH: unzip ${datamanage_package}"
    dmdir="/home/<USER>/ngi-gpdb/ext/dm";
    jdkdir="/home/<USER>/ngi-gpdb/ext/jdk";
    jdktmpdir="/home/<USER>/ngi-gpdb/ext/dm/jdk";
    cmd="[ ! -d "$dmdir" ] && mkdir "$dmdir";
         chmod 755 /home/<USER>/ngi-gpdb/ext/dm -R;
         unzip /home/<USER>/ngi-gpdb/ext/${datamanage_package} -d /home/<USER>/ngi-gpdb/ext/dm;
         chmod +x /home/<USER>/ngi-gpdb/ext/dm/InstallDatamanage.sh;
          [ ! -d "$jdkdir" ] && mkdir "$jdkdir";
          [ ! -d "$jdktmpdir" ] && mkdir "$jdktmpdir";
         chmod 755 /home/<USER>/ngi-gpdb/ext/jdk -R;
         chmod 755 /home/<USER>/ngi-gpdb/ext/dm/jdk -R;
         unzip /home/<USER>/ngi-gpdb/ext/${jdk_package} -d /home/<USER>/ngi-gpdb/ext/jdk;
         cp -r /home/<USER>/ngi-gpdb/ext/jdk/jdk/linux/*  /home/<USER>/ngi-gpdb/ext/dm/jdk;
         cd  /home/<USER>/ngi-gpdb/ext/dm;
         ./InstallDatamanage.sh   /home/<USER>/ngi-gpdb/ext/dm  /home/<USER>
         chmod +x /home/<USER>/ngi-gpdb/ext/dm/DB/*.sh;
         chmod +x /home/<USER>/ngi-gpdb/ext/dm/*.sh;
		 rm -rf /home/<USER>/ngi-gpdb/ext/${datamanage_package};
		 rm -rf /home/<USER>/ngi-gpdb/ext/${jdk_package};"

    sudo su - ${dba_user} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -f ${ngi_tools}/conf/host_gp -v -e '${cmd}'"
    fi

    echo '****datamanageInstall OK'
}
datamanageInstall

creategpInstance(){
 #数据库实例安装
    echo "****** InstanceInstall ******"
    sudo su - ${sys_sshuser} -c "ssh ${arr_IP[0]} sudo su - ${dba_user} -c "/home/<USER>/ngi-gpdb/ext/dm/DB/InstallInstance_gp.sh""
    echo "****** InstanceInstall success******"
}
creategpInstance


datamanageStart(){ 
    echo '**** datamanageInstance  start***'
    cmd="cd  /home/<USER>/ngi-gpdb/ext/dm/;
         chmod +x  ./gpsshDatamanageStart.sh;
         ./gpsshDatamanageStart.sh"

    sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    fi
    echo '**** datamanageInstance  start OK'
}
datamanageStart