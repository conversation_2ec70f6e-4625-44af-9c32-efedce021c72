#!/bin/bash

if [ $# != 1 ] ; then 
    echo "USAGE: $0 install.conf" 
    echo "[ERROR]Please input right parameter"
    exit 1; 
fi 

source $1

#解析集群IP
IFS="," 
arr_IP=($nodeIPList)
arr_Size=${#arr_IP[*]}

dba_user=gpadmin
datamanageStop(){ 
    echo '**** datamanageInstance  stop***'
    cmd="cd  /home/<USER>/ngi-gpdb/ext/dm/;
         chmod +x  ./gpsshDatamanageStop.sh;
         ./gpsshDatamanageStop.sh"

    sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    if [ `echo $?` -ne 0 ];then
        sudo su - ${dba_user} -c "gpssh -h ${arr_IP[0]} -v -e '${cmd}'"
    fi
    echo '**** datamanageInstance  stop OK'
}
datamanageStop

sudo su - gpadmin -c "gpssh -h ${arr_IP[0]} -e -v \"./gp_delete.sh\""