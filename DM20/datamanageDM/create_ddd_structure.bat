@echo off
echo Creating DDD directory structure...

set BASE_PATH=src\main\java\com\zte\datamanage

echo Creating domain layer directories...
mkdir "%BASE_PATH%\domain\model\task" 2>nul
mkdir "%BASE_PATH%\domain\model\execution" 2>nul
mkdir "%BASE_PATH%\domain\model\dataimport" 2>nul
mkdir "%BASE_PATH%\domain\model\schedule" 2>nul
mkdir "%BASE_PATH%\domain\service" 2>nul
mkdir "%BASE_PATH%\domain\event" 2>nul

echo Creating application layer directories...
mkdir "%BASE_PATH%\application\service" 2>nul
mkdir "%BASE_PATH%\application\command" 2>nul
mkdir "%BASE_PATH%\application\query" 2>nul
mkdir "%BASE_PATH%\application\dto" 2>nul

echo Creating infrastructure layer directories...
mkdir "%BASE_PATH%\infrastructure\repository" 2>nul
mkdir "%BASE_PATH%\infrastructure\persistence" 2>nul
mkdir "%BASE_PATH%\infrastructure\adapter" 2>nul
mkdir "%BASE_PATH%\infrastructure\config" 2>nul
mkdir "%BASE_PATH%\infrastructure\external\nds" 2>nul
mkdir "%BASE_PATH%\infrastructure\external\ems" 2>nul
mkdir "%BASE_PATH%\infrastructure\external\vmax" 2>nul

echo Creating interface layer directories...
mkdir "%BASE_PATH%\interfaces\rest" 2>nul
mkdir "%BASE_PATH%\interfaces\facade" 2>nul
mkdir "%BASE_PATH%\interfaces\assembler" 2>nul

echo Creating legacy code directories...
mkdir "%BASE_PATH%\legacy\taskmanage" 2>nul
mkdir "%BASE_PATH%\legacy\dataimport" 2>nul
mkdir "%BASE_PATH%\legacy\sqlitedatamanage" 2>nul

echo Creating .gitkeep files...

echo # Task aggregate directory > "%BASE_PATH%\domain\model\task\.gitkeep"
echo # Execution configuration aggregate directory > "%BASE_PATH%\domain\model\execution\.gitkeep"
echo # Data import aggregate directory > "%BASE_PATH%\domain\model\dataimport\.gitkeep"
echo # Schedule aggregate directory > "%BASE_PATH%\domain\model\schedule\.gitkeep"
echo # Domain service directory > "%BASE_PATH%\domain\service\.gitkeep"
echo # Domain event directory > "%BASE_PATH%\domain\event\.gitkeep"

echo # Application service directory > "%BASE_PATH%\application\service\.gitkeep"
echo # Command object directory > "%BASE_PATH%\application\command\.gitkeep"
echo # Query object directory > "%BASE_PATH%\application\query\.gitkeep"
echo # Data transfer object directory > "%BASE_PATH%\application\dto\.gitkeep"

echo # Repository implementation directory > "%BASE_PATH%\infrastructure\repository\.gitkeep"
echo # Persistence object directory > "%BASE_PATH%\infrastructure\persistence\.gitkeep"
echo # Adapter directory > "%BASE_PATH%\infrastructure\adapter\.gitkeep"
echo # Configuration directory > "%BASE_PATH%\infrastructure\config\.gitkeep"
echo # NDS system integration directory > "%BASE_PATH%\infrastructure\external\nds\.gitkeep"
echo # EMS system integration directory > "%BASE_PATH%\infrastructure\external\ems\.gitkeep"
echo # VMAX system integration directory > "%BASE_PATH%\infrastructure\external\vmax\.gitkeep"

echo # REST interface directory > "%BASE_PATH%\interfaces\rest\.gitkeep"
echo # Facade service directory > "%BASE_PATH%\interfaces\facade\.gitkeep"
echo # Assembler directory > "%BASE_PATH%\interfaces\assembler\.gitkeep"

echo # Legacy task management code directory > "%BASE_PATH%\legacy\taskmanage\.gitkeep"
echo # Legacy data import code directory > "%BASE_PATH%\legacy\dataimport\.gitkeep"
echo # Legacy SQLite management code directory > "%BASE_PATH%\legacy\sqlitedatamanage\.gitkeep"

echo.
echo DDD directory structure created successfully!
echo.
echo Directory structure:
echo src\main\java\com\zte\datamanage\
echo   domain\
echo     model\
echo       task\
echo       execution\
echo       dataimport\
echo       schedule\
echo     service\
echo     event\
echo   application\
echo     service\
echo     command\
echo     query\
echo     dto\
echo   infrastructure\
echo     repository\
echo     persistence\
echo     adapter\
echo     config\
echo     external\
echo       nds\
echo       ems\
echo       vmax\
echo   interfaces\
echo     rest\
echo     facade\
echo     assembler\
echo   legacy\
echo     taskmanage\
echo     dataimport\
echo     sqlitedatamanage\
echo.
pause