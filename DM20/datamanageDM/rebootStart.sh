#!/bin/bash

curDir=$(dirname $0)
cd $curDir

dateStr=$(date +"%F")

{
    if [ "$USER" != "gpadmin" ];then
        echo "[$(date +'%F %T')] ERROR Must gpadmin to run this script!"
        exit 1
    fi
    masterIP=$(cat /home/<USER>/paas_hosts_master.cc)
    masterHostName=$(grep -w "$masterIP" /etc/hosts | awk '{print $2}')
    currentHostName=$(hostname)
    if [ "$masterHostName" == "$currentHostName" ];then
        while ! netstat -ltnp 2>/dev/null | grep -q ":15432"
        do
            echo "[$(date +'%F %T')] Database is not running, rechek after 30s..."
            sleep 30
        done
        echo "[$(date +'%F %T')] Database is running now, restart datamanage..."
        ./gpsshDatamanageStop.sh
        ./gpsshDatamanageStart.sh
    else
        echo "[$(date +'%F %T')] Start standalone..."
        cd bin/DataManage
        ./startstandalone.sh
    fi
    echo "[$(date +'%F %T')] Reboot start completed!"
} >>"logs/rebootStart.$dateStr.log" 2>&1
