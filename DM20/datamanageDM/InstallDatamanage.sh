#!/bin/bash

# 判断传入的参数的个数是不是一个

if [ ! $# -eq 2  ];then
  echo param error!
  exit 1
fi

# 判断目录是不是已经存在，如果不存在则创建，存在则输出“dir exist” 
dirname=$1
echo "the dir name is $dirname"
if [ ! -d $dirname  ];then
  mkdir -p $dirname
else
  echo dir exist
fi

gp_home=$2

#if [ "$1" = "" ]
#then
#    DMDataHome='/home/<USER>/ngi-test2'
#else
#    DMDataHome=$1
#fi
#################################################
#if [ -d $DMDataHome ]
#then
#    rm -rf $DMDataHome/*
#fi

unzip -o datamanage-newcode.zip -d $dirname

if [ ! -d $gp_home/.netmax_shell  ];then
  mkdir  $gp_home/.netmax_shell
fi
chmod 755  $gp_home/.netmax_shell -R
chmod 755  -R $dirname/SFTPServer
cd $dirname/data/load_shell
cp -r  *  $gp_home/.netmax_shell
chmod +x  $gp_home/.netmax_shell/*.sh


