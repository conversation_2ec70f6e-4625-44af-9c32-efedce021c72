#!/bin/bash
#############通过操作系统的判断决定jdk位置############
DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
if [ "$MODE" != "run" ]&&[ "$MODE" != "debug" ]; then
    MODE="run"
    echo "start as 'run' mode."
fi

#echo Mode=$MODE

OSNAME=`uname`
#echo OS=$OSNAME
#echo $HOME
JAVA_HOME=""
JRE_HOME=""
if [ -d "$HOME/jdk/" ] ; then
			JAVA_HOME=`cd $HOME/jdk/; pwd`
fi

#如果没有找到JAVA_HOME,直接退出
if [ -z $JAVA_HOME ] ; then
    echo "can not find JAVA_HOME"
	exit 1
fi

#设置JRE_HOME
JRE_HOME=$JAVA_HOME/jre

#echo JAVA_HOME=$JAVA_HOME
#echo JRE_HOME=$JRE_HOME

#对solaris的64位系统进行特殊处理
if [ $OSNAME != "SunOS" ]; then
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/java"

    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/java"
else
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/sparcv9/java"

    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/sparcv9/java"
fi

#设置JVM和JVM参数
JAVA_OPTS="$JAVA_OPTS -Xms2048M -Xmx20480M -XX:-UseGCOverheadLimit -XX:-ReduceInitialCardMarks"
if [ "$MODE" = "run" ] ; then
   JAVA=${_RUNJAVA}
else
   JAVA=${_RUNJDB}
   #JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,address=10001,server=y,suspend=n"
fi

psql -d netmaxdb -c 'select * from ext_modifyConfigPassword' >/dev/null 2>&1

#赋予java执行权限
chmod a+x $JAVA
nohup $JAVA -jar ./datamanage-newcode-1.0-SNAPSHOT.jar server ./conf/serverconfiguration.yml >/dev/null 2>&1 &
echo "Start datamanage-newcode process ..."
sleep 3
flag=0
until [ ! $flag -lt 20 ]
do
    dm=$(ps -ef|grep "datamanage-newcode-1.0-SNAPSHOT.jar"|grep -v grep)
    if [ -z "$dm" ]
    then
        echo "Datamange starting failed...flag $flag"
        flag=$(($flag+1))
        sleep 30
        nohup $JAVA -jar ./datamanage-newcode-1.0-SNAPSHOT.jar server ./conf/serverconfiguration.yml >/dev/null 2>&1 &
        sleep 3
    else
        echo 'Datamanage has been started...exit checking loop'
        break;
    fi
done
echo "Start datamanage-newcode process end"
#cd  $HOME/bin/DataManage/
chmod +x  $HOME/bin/DataManage/startstandalone.sh
$HOME/bin/DataManage/startstandalone.sh $1

#启动EMSpropress
#删除中间文件以防历史异常文件影响
if [[ -d "/home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/EMS" ]]; then
    # 删除目录下的所有文件
    rm -rf "/home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/EMS"/*
fi
if [[ -d "/home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/UME" ]]; then
    # 删除目录下的所有文件
    rm -rf "/home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/UME"/*
fi
#杀掉EMSPreProcess相关进程以防历史遗留进程影响
# 定义要查找的jar包名称
#jar_name="EMSPreProcess.jar"
#pid_list=$(pgrep -f "$jar_name")
#if [[ -n "$pid_list" ]]; then
#    for pid in $pid_list; do
#        kill "$pid"
#        echo "已杀死进程 $pid"
#    done
#else
#    echo "未找到进程 $jar_name"
#fi
chmod +x  $HOME/EMSPreProcess/bin/start_EMSPreProcess.sh
$HOME/EMSPreProcess/bin/start_EMSPreProcess.sh

#启动NREMSpropress
#删除中间文件以防历史异常文件影响
if [[ -d "/home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/sourcedata/NR/UME" ]]; then
    # 删除目录下的所有文件
    rm -rf "/home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/sourcedata/NR/UME/"/*
fi
#杀掉NRUMEPreProcess相关进程以防历史遗留进程影响
#jar_name="NRUMEPreProcess.jar"
#pid_list=$(pgrep -f "$jar_name")
#if [[ -n "$pid_list" ]]; then
#    for pid in $pid_list; do
#        kill "$pid"
#        echo "已杀死进程 $pid"
#    done
#else
#    echo "未找到进程 $jar_name"
#fi
chmod +x  $HOME/NRUMEPreProcess/bin/start_NRUMEPreProcess.sh
$HOME/NRUMEPreProcess/bin/start_NRUMEPreProcess.sh

chmod +x /home/<USER>/ngi-gpdb/ext/dm/CMJobQueue/queue.sh
chmod +x /home/<USER>/ngi-gpdb/ext/dm/EmsDispatcher.sh
chmod +x /home/<USER>/ngi-gpdb/ext/dm/clearCMrecord.sh
chmod +x /home/<USER>/ngi-gpdb/ext/dm/dataReuse.sh

#启动NGI使用频次数据导出脚本
chmod +x $HOME/featureusagestat.sh
nohup $HOME/featureusagestat.sh >/dev/null 2>&1 &

#启动数据库监控
chmod +x  $HOME/NGIDBMonitor/bin/gpmonitor.sh
chmod +x  $HOME/NGIDBMonitor/bin/monitorrestartdmgp.sh
chmod +x  $HOME/NGIDBMonitor/bin/start_NGIDBMonitor.sh
chmod +x  $HOME/NGIDBMonitor/bin/shutdown_NGIDBMonitor.sh
if [ "$1"x != "-987789"x ]
then 
  $HOME/NGIDBMonitor/bin/start_NGIDBMonitor.sh
fi

mkdir -p /home/<USER>/ngi-gpdb/ext/dm/emsdata/cson/pm
mkdir -p /home/<USER>/ngi-gpdb/ext/dm/emsdata/NR/UME/cson/pm
mkdir -p /home/<USER>/ngi-gpdb/ext/dm/emsdata/MultiMode/VMAX/aggr

#启动监控
alertflag=0
#先停止监控接收再重启
nohup kill -9 $(ps -ef|grep "alertOperator-1.0-SNAPSHOT.jar"|grep -v grep|awk '{print $2}')  >/dev/null 2>&1 &
sleep 10;

until [ ! $alertflag -lt 20 ]
do
    alert=$(ps -ef|grep "alertOperator-1.0-SNAPSHOT.jar"|grep -v grep)
    if [ -z "$alert" ]
    then
        echo "alertOperator starting failed...alertflag $alertflag"
        alertflag=$(($alertflag+1))
        sleep 30
        nohup $JAVA -jar ./alertOperator/bin/alertOperator-1.0-SNAPSHOT.jar server ./conf/alertconfig.yml >/dev/null 2>&1 &
        sleep 3
    else
        echo 'alertOperator has been started...exit checking loop'
        break;
    fi
done
echo "Start alertOperator process end"


echo "All import process started"