REM ע�⣺1�����ǰ��pull����֤���������µĲ���û�г�ͻ 2������ǰ��ֹ�����ͻ����git stash��pull����Ҫע�Ᵽ�汾��δ�ύ�Ĵ��롣3��NovPathΪ����ǰ�˴���·��

@echo off

set SourcePath=%cd%
echo %SourcePath%

REM 7-Zip·��
set SZip="C:\Program Files\7-Zip\7z.exe"

REM ǰ�˴���·��(���ش���·����Ҫע���޸�)
set NovPath=..\DM-NOVisualization
echo %NovPath%

REM ���´���
echo "pull code..."
REM ����ǰ��ֹ�����ͻ����git stash��pull����Ҫע�Ᵽ�汾��δ�ύ�Ĵ��롣
REM git stash
git fetch
git pull
if %errorlevel% neq 0 echo "pull Client code ERROR"

REM ����ǰ�˴���
cd /d "%NovPath%"
REM echo "pull Client code..."
REM ����ǰ��ֹ�����ͻ����git stash��pull����Ҫע�Ᵽ�汾��δ�ύ�Ĵ��롣
REM git stash
REM git fetch
REM git pull
REM if %errorlevel% neq 0 echo "pull Client code ERROR"

REM ����ǰ��
echo "compile Client package..."
REM call npm install
call npm run build

echo "delete and mk %SourcePath%..."
rd /s /q %SourcePath%\src\main\resources
md %SourcePath%\src\main\resources\DM-NOVisualization

echo "copy NOV..."
xcopy "%NovPath%\dist\*" "%SourcePath%\src\main\resources\DM-NOVisualization" /e /y /q

REM ���º�˴���
cd /d "%SourcePath%"
REM echo "pull Server code..."
REM git fetch
REM git pull
REM if %errorlevel% neq 0 echo "pull Server code ERROR"

echo "compile Server package..."
call mvn clean package -Dmaven.test.skip=true
echo "compile Server package end"

REM �Ƴ�Swagger����
call %SZip% d "%SourcePath%\target\datamanage-newcode-1.0-SNAPSHOT.jar" com/zte/ums/zenap/swagger/SwaggerManager.class

REM ����SFTP
echo "compile SFTP package..."
cd /d ""%SourcePath%"\..\SFTPServer"
call mvn clean package -Dmaven.test.skip=true
echo "compile SFTP package end"

REM ����NGIDBMonitor
echo "compile NGIDBMonitor package..."
cd /d ""%SourcePath%"\..\NGIDBMonitor"
call mvn clean package -Dmaven.test.skip=true
echo "compile NGIDBMonitor package end"


REM ����DataDownloadMonitor
echo "compile DataDownloadMonitor package..."
cd /d ""%SourcePath%"\..\DataDownloadMonitor"
call mvn clean package -Dmaven.test.skip=true
echo "compile DataDownloadMonitor package end"

REM ����DataDownloadMonitor
echo "compile alertManager package..."
cd /d ""%SourcePath%"\..\alertManager"
call mvn clean package -Dmaven.test.skip=true
echo "compile alertManager package end"

echo "copy jar..."
cd /d "%SourcePath%"
md %SourcePath%\datamanagetest
copy "%SourcePath%\target\datamanage-newcode-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest" /y

echo "copy files..."
REM copy "%SourcePath%\shfile\InitNGI.sh" "%SourcePath%\ngi-common-service" /y
copy "%SourcePath%\StartDatamanage.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\StartDatamanageNew.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\StopDatamanageNew.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\featureusagestat.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\datamanage.keystore" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\UnistallDatamanage.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\UnistallDatamanagefirst.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\gpsshDatamanageStart.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\gpsshDatamanageStop.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\BackupDatamanage.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\OperateTableSpace.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\SSDVolumePath.cfg" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\EmsDispatcher.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\clearCMrecord.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\dataReuse.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\collectLog.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\rebootStart.sh" "%SourcePath%\datamanagetest" /y
copy "%SourcePath%\rebootStart.cron" "%SourcePath%\datamanagetest" /y
REM copy "%SourcePath%\shfile\UnistallNGI.sh" "%SourcePath%\ngi-common-service" /y
REM md "%SourcePath%\ngi-common-service\tmpfile"
REM xcopy "%SourcePath%\tmpfile\*.*" "%SourcePath%\ngi-common-service\tmpfile" /e /y /s /q
md "%SourcePath%\datamanagetest\sql_script"
xcopy "%SourcePath%\sql_script\*.*" "%SourcePath%\datamanagetest\sql_script" /e /y /s /q
REM md "%SourcePath%\datamanagetest\lib"
REM xcopy "%SourcePath%\lib\*.*" "%SourcePath%\datamanagetest\lib" /e /y /s /q
md "%SourcePath%\datamanagetest\data"
xcopy "%SourcePath%\data\*.*" "%SourcePath%\datamanagetest\data" /e /y /s /q
REM md "%SourcePath%\ngi-common-service\file"
REM xcopy "%SourcePath%\file\*.*" "%SourcePath%\ngi-common-service\file" /e /y /s /q
md "%SourcePath%\datamanagetest\conf"
xcopy "%SourcePath%\conf\*.*" "%SourcePath%\datamanagetest\conf" /e /y /s /q
md "%SourcePath%\datamanagetest\bin"
xcopy "%SourcePath%\bin\*.*" "%SourcePath%\datamanagetest\bin" /e /y /s /q
md "%SourcePath%\datamanagetest\versionbak"
xcopy "%SourcePath%\versionbak\*.*" "%SourcePath%\datamanagetest\versionbak" /e /y /s /q
md "%SourcePath%\datamanagetest\DB"
xcopy "%SourcePath%\..\DB\*.*" "%SourcePath%\datamanagetest\DB" /e /y /s /q
md "%SourcePath%\datamanagetest\EMSPreProcess"
xcopy "%SourcePath%\EMSPreProcess\*.*" "%SourcePath%\datamanagetest\EMSPreProcess" /e /y /s /q
md "%SourcePath%\datamanagetest\NRUMEPreProcess"
xcopy "%SourcePath%\NRUMEPreProcess\*.*" "%SourcePath%\datamanagetest\NRUMEPreProcess" /e /y /s /q
md "%SourcePath%\datamanagetest\SFTPServer"
xcopy "%SourcePath%\SFTPServer\*" "%SourcePath%\datamanagetest\SFTPServer" /e /y /s /q
xcopy "%SourcePath%\..\SFTPServer\target\lib\*" "%SourcePath%\datamanagetest\SFTPServer\lib" /e /y /s /q
copy "%SourcePath%\..\SFTPServer\target\SFTPServer-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\SFTPServer\lib"

REM NGIDBMonitor
md "%SourcePath%\datamanagetest\NGIDBMonitor"
xcopy "%SourcePath%\NGIDBMonitor\*" "%SourcePath%\datamanagetest\NGIDBMonitor" /e /y /s /q
xcopy "%SourcePath%\..\NGIDBMonitor\target\lib\*" "%SourcePath%\datamanagetest\NGIDBMonitor\lib\" /e /y /s /q
copy "%SourcePath%\..\NGIDBMonitor\target\NGIDBMonitor-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\NGIDBMonitor\bin"

REM alertManager
md "%SourcePath%\datamanagetest\alertManager"
xcopy "%SourcePath%\alertManager\*" "%SourcePath%\datamanagetest\alertManager" /e /y /s /q
xcopy "%SourcePath%\..\alertManager\target\lib\*" "%SourcePath%\datamanagetest\alertManager\lib\" /e /y /s /q
copy "%SourcePath%\..\alertManager\target\alertManager-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\alertManager\bin"

REM DataDownloadMonitor
md "%SourcePath%\datamanagetest\DataDownloadMonitor"
xcopy "%SourcePath%\DataDownloadMonitor\*" "%SourcePath%\datamanagetest\DataDownloadMonitor" /e /y /s /q
xcopy "%SourcePath%\..\DataDownloadMonitor\target\lib\*" "%SourcePath%\datamanagetest\DataDownloadMonitor\lib\" /e /y /s /q
copy "%SourcePath%\..\DataDownloadMonitor\target\DataDownloadMonitor-1.0-SNAPSHOT.jar" "%SourcePath%\datamanagetest\DataDownloadMonitor\bin"

REM ApPackage���
md "%SourcePath%\datamanagetest\ApPackage"
xcopy "%SourcePath%\ApPackage\*" "%SourcePath%\datamanagetest\ApPackage\" /e /y /s /q

REM CMJobQueue
md "%SourcePath%\datamanagetest\CMJobQueue"
xcopy "%SourcePath%\CMJobQueue\*" "%SourcePath%\datamanagetest\CMJobQueue\" /e /y /s /q

echo "zip files..."
del datamanage-newcode.zip
del datamanage-service.zip
call %SZip% a "datamanage-newcode.zip" "%SourcePath%\datamanagetest\*"
call %SZip% a "datamanage-service.zip" "%SourcePath%\datamanage-newcode.zip" "%SourcePath%\InstallDatamanage.sh" "%SourcePath%\SSDVolumePath.cfg"

echo "clean..."
del datamanage-newcode.zip
rd /s /q "%SourcePath%\datamanagetest"

REM rename ngi-service.zip datamanage-newcode.zip

echo "===== build ServerAndCilent End====="
pause