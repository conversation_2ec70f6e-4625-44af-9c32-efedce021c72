# DataManage项目组件设计说明文档

## 📋 项目概述

### 项目基本信息
- **项目名称**: DataManage
- **项目类型**: DropWizard Web应用
- **技术栈**: Java 8 + DropWizard + Maven + PostgreSQL/SQLite
- **主要功能**: 数据管理、任务调度、数据导入导出、告警处理

### 架构特点
- 基于DropWizard框架的RESTful Web服务
- 多数据源支持（PostgreSQL、SQLite、SybaseIQ）
- 任务调度和批处理系统
- 数据导入导出管道
- 告警数据处理服务

## 🏗️ 组件架构设计

### 1. 应用层 (Application Layer)

#### 1.1 主应用入口
- **CommonDatamanageApplication**: 主应用类，负责应用启动和配置
  - 继承自IOCApplication，支持依赖注入
  - 初始化数据库连接和表结构
  - 配置静态资源服务和过滤器
  - 启动定时任务（数据清理、告警检查）

#### 1.2 REST服务层
- **OperateService**: 主要的REST API服务，提供数据管理操作接口
  - 路径: `/NetMAX/rest/datamanage`
  - 支持操作: 查询、执行、插入、删除、初始化等
  - 参数清理和安全验证
  - 操作类型枚举处理

### 2. 业务服务层 (Service Layer)

#### 2.1 核心业务服务
- **ImportService**: 数据导入服务
  - 文件包处理
  - CSV数据读取
  - 实时数据导入
  
- **DownloadService**: 数据下载服务
  - 文件下载管理
  - 下载状态跟踪
  
- **TaskService**: 任务管理服务
  - 任务调度
  - 任务状态管理
  - 任务执行监控

#### 2.2 专业服务
- **AlarmDataImportService**: 告警数据导入服务
  - 多数据源告警获取
  - 数据过滤和预处理
  - 告警数据入库
  
- **TaskScheduleService**: 任务调度服务
  - 实时任务执行
  - 定时任务管理
  - 任务参数预处理
  
- **TaskLogService**: 任务日志服务
  - 任务执行记录
  - 日志查询和管理

#### 2.3 数据源服务
- **DataSourceService**: 数据源管理服务
- **NdsInfoService**: NDS信息服务
- **UmeHttpsService**: UME HTTPS数据源服务

### 3. 数据访问层 (Data Access Layer)

#### 3.1 数据库接口层
- **DatabaseHelper**: 数据库操作助手类
  - 通用数据库操作封装
  - 连接管理
  - 事务处理

#### 3.2 数据库实现层
- **PostgresDBI**: PostgreSQL数据库接口实现
  - 连接池管理
  - SQL执行和脚本运行
  - 数据库初始化
  
- **SQLiteDBI**: SQLite数据库接口实现
  - 轻量级数据库操作
  - 本地数据存储
  
- **GreenPlumDBI**: GreenPlum数据库接口实现
  - 大数据处理
  - 分布式查询
  
- **MemoryDBI**: 内存数据库接口实现
  - 高速缓存操作
  - 临时数据处理

#### 3.3 数据访问对象 (DAO)
- **TaskDAO**: 任务数据访问对象
- **TaskLogDAO**: 任务日志数据访问对象
- **TaskScheduleDAO**: 任务调度数据访问对象
- **UmeHttpsAccountDao**: UME HTTPS账户数据访问

### 4. 工具类层 (Utility Layer)

#### 4.1 通用工具
- **CleanPathUtil**: 路径清理工具
- **PathUtil**: 路径处理工具
- **FileUtil**: 文件操作工具
- **CfgUtil**: 配置文件工具

#### 4.2 专业工具
- **FileTools**: 文件处理工具集
- **CryptoUtil**: 加密解密工具
- **DateUtil**: 日期时间工具

### 5. 数据模型层 (Data Model Layer)

#### 5.1 实体类
- **Bean包**: 数据传输对象
  - 菜单相关Bean
  - 认证相关Bean
  - 数据源相关Bean

#### 5.2 任务相关实体
- **Task**: 任务实体
- **TaskLog**: 任务日志实体
- **TaskParam**: 任务参数实体
- **TaskExecParams**: 任务执行参数实体
- **TaskSchedule**: 任务调度实体

#### 5.3 数据源相关实体
- **DataSourceDO**: 数据源数据对象
- **UmeHttpsAccount**: UME HTTPS账户实体

### 6. 配置管理层 (Configuration Layer)

#### 6.1 应用配置
- **NgiCommonServiceConfiguration**: 主配置类
  - 多数据源配置
  - MSB客户端配置
  - Swagger配置
  - 辅助任务配置

#### 6.2 配置文件
- **common_dataimport.conf**: 数据导入通用配置
- **serverconfiguration.yml**: 服务器配置
- **各种XML配置文件**: 数据源、过滤器等配置

### 7. 安全和过滤层 (Security Layer)

#### 7.1 过滤器
- **AuthFilter**: 认证过滤器
  - Cookie验证
  - 访问令牌检查
  - 权限控制
  
- **HSTSFilter**: HSTS安全过滤器
  - HTTP安全传输
  - 安全头设置

#### 7.2 安全工具
- **SMAgentManager**: 安全管理代理
- **AccessTokenInfo**: 访问令牌信息

### 8. 数据处理层 (Data Processing Layer)

#### 8.1 数据预处理
- **DataFromNDSProcess**: NDS数据处理
- **G5_DataFromNDSProcess**: 5G数据处理
- **DataProcessCommon**: 通用数据处理

#### 8.2 告警数据处理
- **AlarmDataFetcherThread**: 告警数据获取线程
- **AlarmSourceProxy**: 告警源代理
- **DataFilter**: 数据过滤器

## 🔄 组件间关系和交互

### 业务流程
1. **用户界面输入**: 用户通过Web界面输入参数（文件路径、SFTP信息等）
2. **参数封装**: 系统将参数封装成任务对象
3. **任务调度**: TaskScheduleService启动定时任务
4. **Scala服务调用**: 调用Scala入库服务处理数据
5. **SFTP文件获取**: Scala服务通过SFTP获取文件
6. **数据入库**: 处理后的数据存储到数据库

### 依赖关系
- REST层依赖Service层
- Service层依赖DAO层和工具类
- DAO层依赖数据库接口
- 所有层都可能依赖工具类和配置

### 数据流向
```
Web界面 → OperateService → TaskScheduleService → Scala服务 → SFTP → 数据库
```

## 📊 技术特点

### 多数据源支持
- PostgreSQL: 主要业务数据
- SQLite: 本地配置和缓存
- GreenPlum: 大数据分析
- 内存数据库: 临时数据处理

### 任务调度机制
- Quartz调度框架
- 实时任务和定时任务
- 任务状态跟踪和日志记录

### 安全机制
- Cookie认证
- 访问令牌管理
- 参数清理和验证
- HTTPS支持

这个架构设计体现了分层架构的优势，各组件职责清晰，便于维护和扩展。
