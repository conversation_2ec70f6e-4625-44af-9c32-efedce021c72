#!/bin/bash
# Program : start EMSPreProcess module

#only root can run this script
#if [ "$USER" != "root" ]; then
   #echo "Must be root to run the script!"
   #exit 1 
#fi


DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
MODE=${1:-"run"}

#如果输入的运行模式既不是run,也不是debug,退出脚本
if [ "$MODE" != "run" ]&&[ "$MODE" != "debug" ]; then
   echo "$MODE is not valid,please input \"run\" or \"debug\""
   exit 1
fi

echo Mode=$MODE

OSNAME=`uname`
echo OS=$OSNAME

echo HOME=$HOME

#通过操作系统的判断决定jdk位置
JAVA_HOME=""
JRE_HOME=""

# if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../CDT/jdk/linux/" ] ; then
   # JAVA_HOME=`cd $HOME/../../CDT/jdk/linux/; pwd`
# fi
if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../jdk/" ] ; then
   JAVA_HOME=`cd $HOME/../../jdk/; pwd`
fi

if [ $OSNAME = "SunOS" ]&&[ -d "$HOME/../../CDT/jdk/solaris/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/solaris/; pwd`
fi

if [ $OSNAME = "AIX" ]&&[ -d "$HOME/../../CDT/jdk/aix/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/aix/; pwd`
fi

#如果没有找到JAVA_HOME,直接退出
if [ -z $JAVA_HOME ] ; then
   echo "can not find JAVA_HOME"
   exit 1
fi

#设置JRE_HOME
JRE_HOME=$JAVA_HOME/jre

echo JAVA_HOME=$JAVA_HOME
echo JRE_HOME=$JRE_HOME

#对solaris的64位系统进行特殊处理
if [ $OSNAME != "SunOS" ]; then
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/java"

    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/java"
else
    #设置Run模式下的JVM
    _RUNJAVA="$JRE_HOME/bin/sparcv9/java"

    #设置Debug模式下的JVM
    _RUNJDB="$JAVA_HOME/bin/sparcv9/java"
fi



#设置JVM和JVM参数
JAVA_OPTS="$JAVA_OPTS -Xms2048M -Xmx20480M"
if [ "$MODE" = "run" ] ; then
   JAVA=${_RUNJAVA}
else
   JAVA=${_RUNJDB}
   #JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,address=10001,server=y,suspend=n"
fi

#赋予java执行权限
chmod a+x $JAVA

#获取安装路径
gp_path=/home/<USER>
gpConfigFile=${gp_path}/host.cfg
gpPaaSpathfile=${gp_path}/greenplumpath.cc

#创建pmcm推送目录
emsDir="${HOME}/../../emsdata"
[ ! -d "$emsDir" ] && mkdir "$emsDir"
[ ! -d "$emsDir/cm" ] && mkdir "$emsDir/cm"
[ ! -d "$emsDir/UME/cm" ] && mkdir -p "$emsDir/UME/cm"
[ ! -d "$emsDir/UME/pm" ] && mkdir -p "$emsDir/UME/pm"
otherdatapath="$HOME/../../data/LTE/rundata/otherdata"
[ ! -d "$otherdatapath" ]  && mkdir -p "$otherdatapath"
sourcedatapath="${HOME}/../sourcedata"
[ ! -d "$sourcedatapath" ] && mkdir "$sourcedatapath"

#设置emsdata的下载路径
cmpmdatatmp=${HOME%/*}
cmpmdataPath=${cmpmdatatmp%/*}/emsdata
echo ${cmpmdataPath}
confignowpath=$(grep  "^ExtTablePath" ${HOME}/../conf/DataPreProcessConfig.ini | tail -n 1 | awk -F'=' '{ print $2 }')
if [ "${cmpmdataPath}" != "${confignowpath}" ]
then
sed -i "s|ExtTablePath=.*|ExtTablePath=${cmpmdataPath}|"     ${HOME}/../conf/DataPreProcessConfig.ini
fi

if [ -f "${gpPaaSpathfile}" ]
then
sed -i "s|isPaas=.*|isPaas=true|"     ${HOME}/../conf/DataPreProcessConfig.ini
fi

#设置PID文件
EMSPREPROCESS_PID="$HOME/EMSPreProcess.pid"
echo EMSPREPROCESS_PID=$EMSPREPROCESS_PID
#设置进程输出文件
EMSPREPROCESS_OUT="$HOME/EMSPreProcess.out"

#检测程序是否已经运行
if [ ! -z "$EMSPREPROCESS_PID" ]; then
    if [ -f "$EMSPREPROCESS_PID" ]; then
      if [ -s "$EMSPREPROCESS_PID" ]; then
        echo "Existing PID file found during start."
        if [ -r "$EMSPREPROCESS_PID" ]; then
          PID=`cat "$EMSPREPROCESS_PID"`
          ps -p $PID >/dev/null 2>&1
          if [ $? -eq 0 ] ; then
            echo "EMSPreProcess appears to still be running with PID $PID. Start aborted."
            exit 1
          else
            echo "Removing/clearing stale PID file."
            rm -f "$EMSPREPROCESS_PID" >/dev/null 2>&1
            if [ $? != 0 ]; then
              if [ -w "$EMSPREPROCESS_PID" ]; then
                cat /dev/null > "$EMSPREPROCESS_PID"
              else
                echo "Unable to remove or clear stale PID file. Start aborted."
                exit 1
              fi
            fi
          fi
        else
          echo "Unable to read PID file. Start aborted."
          exit 1
        fi
      else
        rm -f "$EMSPREPROCESS_PID" >/dev/null 2>&1
        if [ $? != 0 ]; then
          if [ ! -w "$EMSPREPROCESS_PID" ]; then
            echo "Unable to remove or write to empty PID file. Start aborted."
            exit 1
          fi
        fi
      fi
    fi
  fi
  
#创建输出日志
touch "$EMSPREPROCESS_OUT"
	
#启动程序
cd "$HOME"
chmod a+x ./EMSPreProcess.jar
eval LC_ALL=zh_CN \"$JAVA\" ${CLASS_PATH:-' '} ${JAVA_OPTS:-' '} -jar EMSPreProcess.jar >> "$EMSPREPROCESS_OUT" 2>&1 "&"

#记录程序的PID号
if [ ! -z "$EMSPREPROCESS_PID" ]; then
   echo $! > "$EMSPREPROCESS_PID"
fi
