#!/bin/bash

curDir=$(dirname $0)
cd "$curDir"

#U31UME/U31/UME
clearType=$1
clearType=${clearType:-U31}

# 清理非最新一天的CM数据，防止导入多天的CM数据
eval $(grep '^ExtTablePath=' ../conf/DataPreProcessConfig.ini)
if [ -d "$ExtTablePath" ];then
(
    # 进入 emsdata 目录
    cd "$ExtTablePath"
    if [ "${clearType//UME/}" == "U31" ];then
        # U31
        dataDir="$PWD/cm"
        echo "Begin to clear $dataDir (U31)..."
        # 获取文件中最新一天的日期
        latestDay=$(find "$dataDir" -maxdepth 1 -name "*.zip" -type f -printf "%TY%Tm%Td\n" | sort | tail -n 1)
        ls -ltr "$dataDir"
        if [ "$latestDay" ];then
            echo "Latest day is <$latestDay>"
            # 删除非最新一天的数据文件
            find "$dataDir" -maxdepth 1 -name "*.zip" -type f -printf "%TY%Tm%Td:%p\n" | grep -v "^${latestDay}" | awk 'BEGIN{FS=":"}{print $2}' | xargs rm -vf
        else
            echo "No data find!"
        fi
        ls -ltr "$dataDir"
    fi
)
else
    echo "ERROR: $ExtTablePath does not exists!"
    exit 1
fi
