#!/bin/bash
# Program : shutdown EMSPreProcess module

#only root can run this script
#if [ "$USER" != "root" ]; then
    #echo "Must be root to run the script!"
    #exit 1 
#fi

DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`

#����PID�ļ�
EMSPREPROCESS_PID="$HOME/EMSPreProcess.pid"
echo EMSPREPROCESS_PID=$EMSPREPROCESS_PID

if [ -z "$EMSPREPROCESS_PID" ]; then
    echo "Shutdown failed: \$EMSPREPROCESS_PID not set"
else
    if [ -f "$EMSPREPROCESS_PID" ]; then
        PID=`cat "$EMSPREPROCESS_PID"`
        echo "Shutting down EMSPreProcess with the PID: $PID"
        kill -9 $PID
        rm -f "$EMSPREPROCESS_PID" >/dev/null 2>&1
        if [ $? != 0 ]; then
            echo "EMSPreProcess has been shut down but the PID file could not be removed."
        fi
		echo "EMSPreProcess has been shut down successfully."
    fi
fi
