[cellkpi]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,Subnetwork,Cell,eNodeB,RRCConnSuccs,RRCConnAttems,ERABEstSuccs,ERABEstAttems,HOOUTGOSuccs,HOOUTGOAttems,RRCRelAbnormals,RRCRelAll,ERABRelAbnormals,ERABRelAll,MaxRRCConUserNum,

[hotcell]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,SubNetWork,Cell,eNodeB,C373343804,C373343805,C373343807,C373343806,C373424600,C373424601,C373424602,C373424603,C373424636,C373424640,C373596957,C373596956,C373424610,C373424611,C373384348,C374107514,C374107515,C374107516,DlConPrbRatio,UlConPrbRatio,C373200030,C373424608,C373424609,<PERSON>373454802,<PERSON>373454803,<PERSON>373454800,C373454801,C373454822,ULMCS_ScheduleNum,C373454823,DLMCS_ScheduleNum,C373454807,C373454805,C373454814,C373454815,C373414597,VoLTE_Call_Setup_Rate_Numerator,VoLTE_Call_Setup_Rate_Denominator,VoLTE_Call_Drop_Rate_Numerator,VoLTE_Call_Drop_Rate_Denominator,CSFB_FallBack_Rate_Numerator,CSFB_FallBack_Rate_Denominator,

[neigbor]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,SubNetwork,CellID,eNodeBID,Relation,NMcc,NMnc,NeNodeBID,NCellID,Handovers,Handover_success_ratio,intra_hoout_try_times,intra_hoout_success_times,inter_hoout_try_times,inter_hoout_success_times,intra_ho_success_times,intra_ho_failure_times,

[supercell]
ColumnInfo=Index,StartTime,EndTime,,,CellID,eNodeB,,Mean_Number_of_RRC_Connection_User,Average_Cell_DL_PHY_Throughput_Kbps,PRB_Number_Available_on_PDSCH_Channel,Average_Cell_DL_PDCP_Throughput_Mbps,Average_Active_User_Number_on_User_Plane,PRB_Number_Used_on_PDSCH_Channel,Cell_DL_Aggregated_Volume_High_Mbit,Cell_DL_Aggregated_Volume_Low_Mbit

[counter]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,Subnetwork,eNodeB,PacketLoss,PacketOutOfOrder,PacketTotal,

[hotkpicell]
ColumnInfo=Index,StartTime,EndTime,Cell,eNodeB,DownlinkTraffic,ULlinkTraffic,CU,AU,InterRATHandover,Caps,ULPRBUsage,DLPRBUsage,

[hotkpienodeb]
ColumnInfo=Index,StartTime,EndTime,eNodeB,CPUPeakUtilization,CPUAverageUtilization,DurationRate,

[rssi15mincell]
ColumnInfo=StartTime,EndTime,Subnetwork,ManagedElement,Rack,Shelf,Slot,Channel,MaxRSSIRRUCarrier1,MinRSSIRRUCarrier1,AvgRSSIRRUCarrier1,ReplaceableUnit

[kpicell]
ColumnInfo=StartTime,EndTime,Cell,eNodeB,,ErabVolume,RRCAU,PUSCHUsage,PDSCHUsage,PDCCHUsage,ULVolume,DLVolume,CellULQCI1AvgActUENum,CellULQCI1MaxActUENum,CellDLQCI1AvgActUENum,CellDLQCI1MaxActUENum,MaxActUENum,RRCConMaxUENum,RRCConAvgUENum,C373230700,C373230701,C373230702,C373230703,C373230704,C373230705,C373230706

[intersysinterference]
ColumnInfo=StartTime,EndTime,CellID,eNodeBID,rb03AvgNI,rb34AvgNI,rb96AvgNI,auDuration,auCount,rbAvgNI,ulPrbUseRatio