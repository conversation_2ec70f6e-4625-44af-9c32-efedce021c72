[CMExportTemplateMergeMap]
#T204
EUtranCellNE=EUtranCellFDD,EUtranCellTDD
#T205
EUtranRelation=EUtranRelation,EUtranRelationTDD
#T206
ExternalEUtranCell=ExternalEUtranCellFDD,ExternalEUtranCellTDD
#T207
QoSPBRMapping=QoSPBRMapping,QoSPBRMappingTDD
#T211
EUtranCellMeasurement=EUtranCellMeasurement,EUtranCellMeasurementTDD
#T212
UeEUtranMeasurement=UeEUtranMeasurement,UeEUtranMeasurementTDD
#T214
PhyChannel=PhyChannel,PhyChannelTDD
#T215
IrAntGroup=IrAntGroup
#T217
ControlPlaneTimer=ControlPlaneTimer,ControlPlaneTimerTDD
#T218
LoadManagement=LoadManagement,LoadManagementTDD
#T232
PowerControlDL=PowerControlDL,PowerControlDLTDD
#T233
PowerControlUL=PowerControlUL,PowerControlULTDD
#T234
ECellEquipmentFunction=ECellEquipmentFunction,ECellEquipmentFunctionTDD
#T210
BpDevice=BpDevice
#T209
SdrDeviceGroup=SdrDeviceGroup
#T201
ENBFunction=ENBFunctionFDD,ENBFunctionTDD
#L_T240
UtranRelationUMTS=UtranRelation,UtranRelationTDD
#L_T241
ExternalUtranCellUMTS=ExternalUtranCellFDD,ExternalUtranCellTDD
#L_T242
DualConnectionBear=DualConnectionBear,DualConnectionBearTDD
#L_T243
ENDCX2Ap=ENDCX2Ap,ENDCX2ApTDD
#L_T244
ExternalNrCell=ExternalNrCell,ExternalNrCellTDD,ExternalNrCellFDD
#L_T245
GlobleSwitchInformation=GlobleSwitchInformation,GlobleSwitchInformationTDD
#L_T246
IpLayerConfig=IpLayerConfig
#L_T247
NrNiborRela=NrNiborRela,NrNiborRelaTDD
#L_T248
QoSServiceClass=QoSServiceClass,QoSServiceClassTDD
#L_T249
Sctp=Sctp

#�ļ���ȡ�ֶ�
#��ldn�в����ֶ�Ҳ���ã��Զ����
#ռλ�ֶ�ʹ��NULL
[CMExportTemplateConfig]
EUtranCellFDD=SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,cellLocalId,refPlmn,pci,tac,earfcnUl,earfcnDl,bandWidthUl,bandWidthDl,cellSize,cFI,cceAdaptMod,freqBandInd,refECellEquipmentFunction,userLabel,pb,flagSwiMode,cellRSPortNum,specialSfPatterns,atmosphericSwch,NULL,atmosphericSymBackSwch,maxUeRbNumDl,ldn,null,qamSwch,maxUeRbNumUl,sceneCfg
EUtranCellTDD=SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,cellLocalId,refPlmn,pci,tac,earfcn,earfcn,bandWidth,bandWidth,cellSize,cFI,cceAdaptMod,bandIndicator,refECellEquipmentFunctionTDD,userLabel,pb,flagSwiMode,cellRSPortNum,specialSfPatterns,atmosphericSwch,atmosDuctMethod,atmosphericSymBackSwch,maxUeRbNumDl,ldn,sfAssignment,qamSwch,maxUeRbNumUl,sceneCfg
EUtranRelation=SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,refEUtranCellFDD,refExternalEUtranCellFDD,isX2HOAllowed,isHOAllowed,refExternalEUtranCellTDD,coperType,SubNetwork,ManagedElement,shareCover
EUtranRelationTDD=SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,refEUtranCellTDD,refExternalEUtranTCellFDD,isX2HOAllowed,isHOAllowed,refExternalEUtranTCellTDD,coperType,SubNetwork,ManagedElement,shareCover
ExternalEUtranCellFDD=SubNetwork,ManagedElement,ENBFunctionFDD,ExternalEUtranCellFDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcnUl,earfcnDl,bandWidthDl,bandWidthUl,freqBandInd
ExternalEUtranCellTDD=SubNetwork,ManagedElement,ENBFunctionFDD,ExternalEUtranCellTDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcn,earfcn,bandWidth,bandWidth,freqBandInd
QoSPBRMapping=SubNetwork,ManagedElement,ENBFunctionFDD,qCI,lchDirection,pbrValue
QoSPBRMappingTDD=SubNetwork,ManagedElement,ENBFunctionTDD,qCI,lchDirection,pbrValue
EUtranCellMeasurement=SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,eutranMeasParas_offsetFreq,handover4AtmosductCell
EUtranCellMeasurementTDD=SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,eutranMeasParas_offsetFreq,handover4AtmosductCell
UeEUtranMeasurement=SubNetwork,ManagedElement,ENBFunctionFDD,hysteresis,a3Offset,timeToTrigger,eventId
UeEUtranMeasurementTDD=SubNetwork,ManagedElement,ENBFunctionTDD,hysteresis,a3Offset,timeToTrigger,eventId
PhyChannel=ldn,SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,freqSelectUl,pucchIrcSwch
PhyChannelTDD=ldn,SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,freqSelectUl,pucchIrcSwch
IrAntGroup=ldn,SubNetwork,ManagedElement,irAntGroupNo,refSdrDeviceGroup
ControlPlaneTimer=SubNetwork,ManagedElement,ENBFunctionFDD,storeUEcntxtTimer
ControlPlaneTimerTDD=SubNetwork,ManagedElement,ENBFunctionTDD,storeUEcntxtTimer
LoadManagement=SubNetwork,ManagedElement,ENBFunctionFDD,ulPRBLBExeThrd,dlPRBLBExeThrd
LoadManagementTDD=SubNetwork,ManagedElement,ENBFunctionTDD,ulPRBLBExeThrd,dlPRBLBExeThrd
PowerControlDL=SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,paForDTCH
PowerControlDLTDD=SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,paForDTCH
PowerControlUL=SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
PowerControlULTDD=SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
ECellEquipmentFunction=SubNetwork,ManagedElement,ENBFunctionFDD,ECellEquipmentFunction,cpSpeRefSigPwr,refBpDevice,cpId,ldn,ref1SdrDeviceGroup,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
ECellEquipmentFunctionTDD=SubNetwork,ManagedElement,ENBFunctionTDD,ECellEquipmentFunctionTDD,cpSpeRefSigPwr,refBpDevice,cpId,ldn,refRfDevice,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
BpDevice=ldn,SubNetwork,ManagedElement,Slot,description
SdrDeviceGroup=SubNetwork,ManagedElement,productData_productName,Rack,ldn,SubRack,Slot,NULL
ENBFunctionFDD=SubNetwork,ManagedElement,ENBFunctionFDD,eNBId,enbName
ENBFunctionTDD=SubNetwork,ManagedElement,ENBFunctionTDD,eNBId,enbName
UtranRelation=ldn,SubNetwork,ManagedElement,ENBFunctionFDD,EUtranCellFDD,UtranRelation,description,refExternalUtranCellTDD,refExternalUtranCellFDD
UtranRelationTDD=ldn,SubNetwork,ManagedElement,ENBFunctionTDD,EUtranCellTDD,UtranRelation,description,refExternalUtranTCellTDD,refExternalUtranTCellFDD
ExternalUtranCellFDD=ldn,SubNetwork,ManagedElement,ENBFunctionFDD,ExternalUtranCellFDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcnUl,uarfcnDl
ExternalUtranCellTDD=ldn,SubNetwork,ManagedElement,ENBFunctionFDD,ExternalUtranCellTDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcn,uarfcn
#DualConnectionBear
DualConnectionBear=ldn,SubNetwork,ManagedElement,qCI,dCBearerType
DualConnectionBearTDD=ldn,SubNetwork,ManagedElement,qCI,dCBearerType
#ENDCX2ApTDD
ENDCX2Ap=ldn,SubNetwork,ManagedElement,refSctp
ENDCX2ApTDD=ldn,SubNetwork,ManagedElement,refSctp
#ExternalNrCell
ExternalNrCell=ldn,SubNetwork,ManagedElement,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaT1_centerFreq,nRFreqCfgParaT1_pci,nRFreqCfgParaT1_sSBSCS,nrPointA,nrPointA,plmnIdList_mcc,plmnIdList_mnc,tac
ExternalNrCellTDD=ldn,SubNetwork,ManagedElement,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaT1_centerFreq,nRFreqCfgParaT1_pci,nRFreqCfgParaT1_sSBSCS,nrPointA,nrPointA,plmnIdList_mcc,plmnIdList_mnc,tac
ExternalNrCellFDD=ldn,SubNetwork,ManagedElement,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaF_centerFreqDL,nRFreqCfgParaF_pci,nRFreqCfgParaF_sSBSCS,nrPointAUL,nrPointADL,plmnIdList_mcc,plmnIdList_mnc,tac
#GlobleSwitchInformation
GlobleSwitchInformation=ldn,SubNetwork,ManagedElement,switchForUserInactivity
GlobleSwitchInformationTDD=ldn,SubNetwork,ManagedElement,switchForUserInactivity
#IpLayerConfig
IpLayerConfig=ldn,SubNetwork,ManagedElement,ipAddr
#NrNiborRela
NrNiborRela=ldn,SubNetwork,ManagedElement,swchNoENDC,refExternalNrCell,refExternalNrCellFDD
NrNiborRelaTDD=ldn,SubNetwork,ManagedElement,swchNoENDC,refExternalNrCellTDD,refExternalNrCellTFDD
#QoSServiceClass
QoSServiceClass=ldn,SubNetwork,ManagedElement,qCI,rlcMode,SequenNumLenth,ueRATType
QoSServiceClassTDD=ldn,SubNetwork,ManagedElement,qCI,rlcMode,sequenNumLenth,ueRATType
#Sctp
Sctp=ldn,SubNetwork,ManagedElement,localPort,refIpLayerConfig,remoteAddr,remotePort,sctpType
