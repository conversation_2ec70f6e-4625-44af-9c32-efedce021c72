[management]
#the max count of source csv files in every processing
FileCount=500
#the max size of one file in every processing, unit: MB
FileSize=200
#the source files path, Please do not modify
ExtTablePath=%File_Path%
#the result files path, to be loaded to database
ResultTablePath=../../Server/data/LTE/rundata/otherdata
#the cycle of scaning EMS source files, unit: min
ProcessPeriod=120	
#isPaas
isPaas=false
#sleep time, unit(s)
SleepTime=300


#tdd/fdd file merge map
#target file name=source file1,source file2
[CMExportTemplateMergeMap] 
#T204
EUtranCellNE=EUtranCellFDD,EUtranCellTDD,EUtranCellFDDLTE,EUtranCellTDDLTE
#T205
EUtranRelation=EUtranRelation,EUtranRelationTDD,EUtranRelationFDDLTE,EUtranRelationTDDLTE
#T206
ExternalEUtranCell=ExternalEUtranCellFDD,ExternalEUtranTCellTDD,ExternalEUtranTCellFDD,ExternalEUtranCellTDD,ExternalEUtranCellFDDLTE,ExternalEUtranCellTDDLTE
#T207
QoSPBRMapping=QoSPBRMapping,QoSPBRMappingTDD,QoSPBRMappingLTE
#T211
EUtranCellMeasurement=EUtranCellMeasurement,EUtranCellMeasurementTDD,EUtranCellMeasFDDLTE,EUtranCellMeasTDDLTE
#T212
UeEUtranMeasurement=UeEUtranMeasurement,UeEUtranMeasurementTDD,UeEUtranMeasurementLTE
#T214
PhyChannel=PhyChannel,PhyChannelTDD
#T215
IrAntGroup=IrAntGroup
#T217
ControlPlaneTimer=ControlPlaneTimer,ControlPlaneTimerTDD,ControlPlaneTimerLTE
#T218
LoadManagement=LoadManagement,LoadManagementTDD,LoadManagementLTE
#T232
PowerControlDL=PowerControlDL,PowerControlDLTDD,PowerControlDLFDDLTE,PowerControlDLTDDLTE
#T233
PowerControlUL=PowerControlUL,PowerControlULTDD,PowerControlULFDDLTE,PowerControlULTDDLTE
#T234
ECellEquipmentFunction=ECellEquipmentFunction,ECellEquipmentFunctionTDD,ECellEquipFuncFDDLTE,ECellEquipFuncTDDLTE
#T210
BpDevice=BpDevice
#T209
SdrDeviceGroup=SdrDeviceGroup
#T200
NEManagedElement=NEManagedElement
#T201
ENBFunction=ENBFunctionFDD,ENBFunctionTDD,ENBFunction
#T239
omminfo=omminfo
#L_T240
UtranRelationUMTS=UtranRelation,UtranRelationTDD
#L_T241
ExternalUtranCellUMTS=ExternalUtranCellTDD,ExternalUtranCellFDD,ExternalUtranTCellFDD,ExternalUtranTCellTDD
#L_T242
DualConnectionBear=DualConnectionBear,DualConnectionBearTDD
#L_T243
ENDCX2Ap=ENDCX2Ap,ENDCX2ApTDD
#L_T244
ExternalNrCell=ExternalNrCell,ExternalNrCellTDD,ExternalNrCellFDD,ExternalNrCellTFDD
#L_T245
GlobleSwitchInformation=GlobleSwitchInformation,GlobleSwitchInformationTDD
#L_T246
IpLayerConfig=IpLayerConfig
#L_T247
NrNiborRela=NrNiborRela,NrNiborRelaTDD
#L_T248
QoSServiceClass=QoSServiceClass,QoSServiceClassTDD
#L_T249
Sctp=Sctp


#config field need to get
#file name = field1,field2....
[CMExportTemplateConfig]
EUtranCellFDD=SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,cellLocalId,refPlmn,pci,tac,earfcnUl,earfcnDl,bandWidthUl,bandWidthDl,cellSize,CFI,cceAdaptMod,freqBandInd,refECellEquipmentFunction,userLabel,pb,flagSwiMode,cellRSPortNum,cellRSPortNum,atmosphericSwch,atmosphericSwch,atmosphericSwch,maxUeRbNumDl,MOI,null,qamSwch,maxUeRbNumUl,sceneCfg
EUtranCellTDD=SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,cellLocalId,refPlmn,pci,tac,earfcn,earfcn,bandWidth,bandWidth,cellSize,CFI,cceAdaptMod,bandIndicator,refECellEquipmentFunctionTDD,userLabel,pb,flagSwiMode,cellRSPortNum,specialSfPatterns,atmosphericSwch,atmosDuctMethod,atmosphericSymBackSwch,maxUeRbNumDl,MOI,sfAssignment,qamSwch,maxUeRbNumUl,sceneCfg
EUtranCellFDDLTE=MOI,SubNetwork,MEID,ENBFunction,EUtranCellFDDLTE,cellLocalId,refPlmn,pci,tac,earfcnUl,earfcnDl,bandWidthUl,bandWidthDl,cellSize,CFI,cceAdaptMod,freqBandInd,refECellEquipFuncFDDLTE,userLabel,pb,flagSwiMode,null,null,null
EUtranCellTDDLTE=MOI,SubNetwork,MEID,ENBFunction,EUtranCellTDDLTE,cellLocalId,refPlmn,pci,tac,earfcn,earfcn,bandWidth,bandWidth,cellSize,CFI,cceAdaptMod,bandIndicator,refECellEquipFuncTDDLTE,userLabel,pb,flagSwiMode,null,null,null
EUtranRelation=SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,refEUtranCellFDD,refExternalEUtranCellFDD,isX2HOAllowed,isHOAllowed,refExternalEUtranCellTDD,coperType,shareCover
EUtranRelationTDD=SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,refEUtranCellTDD,refExternalEUtranTCellTDD,isX2HOAllowed,isHOAllowed,refExternalEUtranTCellFDD,coperType,shareCover
EUtranRelationFDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellFDDLTE,refEUtranCellFDDLTE,refExternalEUtranCellFDDLTE,isX2HOAllowed,isHOAllowed,refExternalEUtranCellTDDLTE,coperType,shareCover
EUtranRelationTDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellTDDLTE,refEUtranCellTDDLTE,refExternalEUtranCellTDDLTE,isX2HOAllowed,isHOAllowed,refExternalEUtranCellFDDLTE,coperType,shareCover
ExternalEUtranCellFDD=SubNetwork,MEID,ENBFunctionFDD,ExternalEUtranCellFDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcnUl,earfcnDl,bandWidthDl,bandWidthUl,freqBandInd
ExternalEUtranTCellTDD=SubNetwork,MEID,ENBFunctionTDD,ExternalEUtranTCellTDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcn,earfcn,bandWidth,bandWidth,freqBandInd
ExternalEUtranTCellFDD=SubNetwork,MEID,ENBFunctionTDD,ExternalEUtranTCellFDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcnUl,earfcnDl,bandWidthDl,bandWidthUl,freqBandInd
ExternalEUtranCellTDD=SubNetwork,MEID,ENBFunctionFDD,ExternalEUtranCellTDD,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcn,earfcn,bandWidth,bandWidth,freqBandInd
ExternalEUtranCellTDDLTE=SubNetwork,MEID,ENBFunction,ExternalEUtranCellTDDLTE,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcn,earfcn,bandWidthDl,bandWidthUl,freqBandInd
ExternalEUtranCellFDDLTE=SubNetwork,MEID,ENBFunction,ExternalEUtranCellFDDLTE,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcnUl,earfcnDl,bandWidth,bandWidth,freqBandInd
QoSPBRMapping=SubNetwork,MEID,ENBFunctionFDD,qCI,lchDirection,pbrValue
QoSPBRMappingLTE=SubNetwork,MEID,ENBFunction,qCI,lchDirection,pbrValue
QoSPBRMappingTDD=SubNetwork,MEID,ENBFunctionTDD,qCI,lchDirection,pbrValue
EUtranCellMeasurement=SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,eutranMeasParas_offsetFreq
EUtranCellMeasFDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellFDDLTE,eutranMeasParas_offsetFreq
EUtranCellMeasTDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellTDDLTE,eutranMeasParas_offsetFreq
EUtranCellMeasurementTDD=SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,eutranMeasParas_offsetFreq,handover4AtmosductCell
UeEUtranMeasurement=SubNetwork,MEID,ENBFunctionFDD,hysteresis,a3Offset,timeToTrigger,eventId
UeEUtranMeasurementLTE=SubNetwork,MEID,ENBFunction,hysteresis,a3Offset,timeToTrigger,eventId
UeEUtranMeasurementTDD=SubNetwork,MEID,ENBFunctionTDD,hysteresis,a3Offset,timeToTrigger,eventId
ControlPlaneTimer=SubNetwork,MEID,ENBFunctionFDD,storeUEcntxtTimer
ControlPlaneTimerTDD=SubNetwork,MEID,ENBFunctionTDD,storeUEcntxtTimer
ControlPlaneTimerLTE=SubNetwork,MEID,ENBFunction,storeUEcntxtTimer
LoadManagement=SubNetwork,MEID,ENBFunctionFDD,ulPRBLBExeThrd,dlPRBLBExeThrd
LoadManagementTDD=SubNetwork,MEID,ENBFunctionTDD,ulPRBLBExeThrd,dlPRBLBExeThrd
LoadManagementLTE=SubNetwork,MEID,ENBFunction,ulPRBLBExeThrd,dlPRBLBExeThrd
PowerControlDL=SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,paForDTCH
PowerControlDLFDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellFDDLTE,paForDTCH
PowerControlDLTDD=SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,paForDTCH
PowerControlDLTDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellTDDLTE,paForDTCH
PowerControlUL=SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
PowerControlULFDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellFDDLTE,switchForCLPCofPUSCH
PowerControlULTDD=SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
PowerControlULTDDLTE=SubNetwork,MEID,ENBFunction,EUtranCellTDDLTE,switchForCLPCofPUSCH
ECellEquipmentFunction=SubNetwork,MEID,ENBFunctionFDD,ECellEquipmentFunction,cpSpeRefSigPwr,refBpDevice,cpId,MOI,ref1SdrDeviceGroup,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
ECellEquipFuncFDDLTE=SubNetwork,MEID,ENBFunction,ECellEquipFuncFDDLTE,cpSpeRefSigPwr,refBpDevice,cpId,MOI,ref1SdrDeviceGroup,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
ECellEquipmentFunctionTDD=SubNetwork,MEID,ENBFunctionTDD,ECellEquipmentFunctionTDD,cpSpeRefSigPwr,refBpDevice,cpId,MOI,refRfDevice,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
ECellEquipFuncTDDLTE=SubNetwork,MEID,ENBFunction,ECellEquipFuncTDDLTE,cpSpeRefSigPwr,refBpDevice,cpId,MOI,ref1SdrDeviceGroup,supercellFlagCopy,refRfDevice,refIrAntGroup,upActAntBitmapSeq,maxCPTransPwr
ENBFunctionFDD=SubNetwork,MEID,ENBFunctionFDD,eNBId,enbName
ENBFunction=SubNetwork,MEID,ENBFunction,eNBId,enbName
ENBFunctionTDD=SubNetwork,MEID,ENBFunctionTDD,eNBId,enbName
BpDevice=MOI,SubNetwork,MEID,Slot,description
SdrDeviceGroup=SubNetwork,MEID,productData_productName,Rack,MOI,SubRack,Slot,NULL
NEManagedElement=SubNetwork,MEID,SWVERSION
omminfo=SUBNETID,OMMNAME,MEID
PhyChannel=MOI,SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,freqSelectUl,pucchIrcSwch
PhyChannelTDD=MOI,SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,freqSelectUl,pucchIrcSwch
IrAntGroup=MOI,SubNetwork,MEID,IrAntGroup,refSdrDeviceGroup
UtranRelation=MOI,SubNetwork,MEID,ENBFunctionFDD,EUtranCellFDD,UtranRelation,description,refExternalUtranCellTDD,refExternalUtranCellFDD
UtranRelationTDD=MOI,SubNetwork,MEID,ENBFunctionTDD,EUtranCellTDD,UtranRelationTDD,description,refExternalUtranTCellTDD,refExternalUtranTCellFDD
ExternalUtranCellTDD=MOI,SubNetwork,MEID,ENBFunctionFDD,ExternalUtranCellTDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcn,uarfcn
ExternalUtranCellFDD=MOI,SubNetwork,MEID,ENBFunctionFDD,ExternalUtranCellFDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcnUl,uarfcnDl
ExternalUtranTCellFDD=MOI,SubNetwork,MEID,ENBFunctionTDD,ExternalUtranTCellFDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcnUl,uarfcnDl
ExternalUtranTCellTDD=MOI,SubNetwork,MEID,ENBFunctionTDD,ExternalUtranTCellTDD,description,mcc,mnc,rncId,cId,freqBandInd,uarfcn,uarfcn
#DualConnectionBear
DualConnectionBear=MOI,SubNetwork,MEID,qCI,dCBearerType
DualConnectionBearTDD=MOI,SubNetwork,MEID,qCI,dCBearerType
#ENDCX2Ap
ENDCX2Ap=MOI,SubNetwork,MEID,refSctp
ENDCX2ApTDD=MOI,SubNetwork,MEID,refSctp
#ExternalNrCell
ExternalNrCell=MOI,SubNetwork,MEID,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaT1_centerFreq,nRFreqCfgParaT1_pci,nRFreqCfgParaT1_sSBSCS,nrPointA,nrPointA,plmnIdList_mcc,plmnIdList_mnc,tac
ExternalNrCellTDD=supportEnDc,MOI,SubNetwork,MEID,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaT1_centerFreq,nRFreqCfgParaT1_pci,nRFreqCfgParaT1_sSBSCS,nrPointA,nrPointA,mcc,mnc,tac
ExternalNrCellFDD=MOI,SubNetwork,MEID,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaF_centerFreq,nRFreqCfgParaF_pci,nRFreqCfgParaF_sSBSCS,nrPointAUL,nrPointADL,plmnIdList_mcc,plmnIdList_mnc,tac
ExternalNrCellTFDD=supportEnDc,MOI,SubNetwork,MEID,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,nRFreqCfgParaF_centerFreq,nRFreqCfgParaF_pci,nRFreqCfgParaF_sSBSCS,nrPointAUL,nrPointADL,mcc,mnc,tac
#GlobleSwitchInformation
GlobleSwitchInformation=MOI,SubNetwork,MEID,switchForUserInactivity
GlobleSwitchInformationTDD=MOI,SubNetwork,MEID,switchForUserInactivity
#IpLayerConfig
IpLayerConfig=MOI,SubNetwork,MEID,ipAddr
#NrNiborRela
NrNiborRela=MOI,SubNetwork,MEID,swchNoENDC,refExternalNrCell,refExternalNrCellFDD
NrNiborRelaTDD=MOI,SubNetwork,MEID,NULL,refExternalNrCellTDD,refExternalNrCellTFDD
#QoSServiceClass
QoSServiceClass=MOI,SubNetwork,MEID,qCI,rlcMode,SequenNumLenth,ueRATType
QoSServiceClassTDD=MOI,SubNetwork,MEID,qCI,rlcMode,sequenNumLenth,ueRATType
#Sctp
Sctp=MOI,SubNetwork,MEID,localPort,refIpLayerConfig,remoteAddr,remotePort,sctpType