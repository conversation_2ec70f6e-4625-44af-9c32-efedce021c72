[CMExportTemplateMergeMap]
#T204
EUtranCellNE=CUEUtranCellFDDLTE,CUEUtranCellTDDLTE
#T205
EUtranRelation=EUtranRelationFDDLTE,EUtranRelationTDDLTE
#T206
ExternalEUtranCell=ExternalEUtranCellFDDLTE,ExternalEUtranCellTDDLTE
#T207
QoSPBRMapping=QoSPBRMappingLTE
#T211
EUtranCellMeasurement=EUtranMeasFDDLTE,EUtranMeasTDDLTE
#T212
UeEUtranMeasurement=UeEUtranMeasurementLTE
#T214
PhyChannel=PhyChannelFDDLTE,PhyChannelTDDLTE
#T215
IrAntGroup=IrRruTxRxGroup
#T217
ControlPlaneTimer=ControlPlaneTimerLTE
#T218
LoadManagement=LoadMNGCellFDDLTE,LoadMNGCellTDDLTE
#T232
PowerControlDL=PowerControlDLFDDLTE,PowerControlDLTDDLTE
#T233
PowerControlUL=PowerControlULFDDLTE,PowerControlULTDDLTE
#T234
ECellEquipmentFunction=ECellEquipFuncFDDLTE,ECellEquipFuncTDDLTE
#T236
SectorFunction=SectorFunction
#T237
RfLink=RfLink
#T238
AauTxRxGroup=AauTxRxGroup
#T209
SdrDeviceGroup=ReplaceableUnit
#T201
ENBFunction=ENBCUCPFunction
#L_T240
UtranRelationUMTS=UtranRelationFDDLTE,UtranRelationTDDLTE
#L_T241
ExternalUtranCellUMTS=ExternalUtranCellFDDLTE,ExternalUtranCellTDDLTE
#L_T242
DualConnectionBear=DualConnectionBearLTE
#L_T243
ENDCX2Ap=ENDCX2Ap
#L_T244
ExternalNrCell=ExternalNrCellLTE,ExternalNrCellFDDLTE
#L_T245
GlobleSwitchInformation=GlobalSwitch
#L_T246
IpLayerConfig=Ip
#L_T247
NrNiborRela=NrNiborRelaFDDLTE,NrNiborRelaTDDLTE
#L_T248
QoSServiceClass=QoSServiceClassLTE
#L_T249
Sctp=Sctp

[ITBBUPlatFileMergeConfig]
PlatMO=BpPoolFunction,SectorFunction,RfLink,ReplaceableUnit,IrRruTxRxGroup,AauTxRxGroup
#ECellEquipmentFunction: ECellEquipFuncFDDLTE,ECellEquipFuncTDDLTE,BpPoolFunction,SectorFunction,RfLink

#�ļ���ȡ�ֶ�
#��ldn�в����ֶ�Ҳ���ã��Զ����
[CMExportTemplateConfig]
CUEUtranCellFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,cellLocalId,cellLocalId,refPlmn,pci,tac,earfcnUl,earfcnDl,bandWidthUl,bandWidthDl,cellSize,cFI,cceAdaptMod,freqBandInd,userLabel,pb,flagSwiMode,cellRSPortNum,NULL,atmosphericSwch,NULL,atmosphericSymBackSwch,maxUeRbNumDl,ldn,null,qamSwch,maxUeRbNumUl,sceneCfg
CUEUtranCellTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,cellLocalId,cellLocalId,refPlmn,pci,tac,earfcn,earfcn,bandWidth,bandWidth,cellSize,cFI,cceAdaptMod,bandIndicator,userLabel,pb,flagSwiMode,cellRSPortNum,specialSfPatterns,atmosphericSwch,atmosDuctMethod,atmosphericSymBackSwch,maxUeRbNumDl,ldn,sfAssignment,qamSwch,maxUeRbNumUl,sceneCfg
DUEUtranCellFDDLTE=SubNetwork,ManagedElement,ldn,refECellEquipFuncFDDLTE
DUEUtranCellTDDLTE=SubNetwork,ManagedElement,ldn,refECellEquipFuncTDDLTE
EUtranRelationFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,refCUEUtranCellFDDLTE,refExternalEUtranCellFDDLTE,isX2HOAllowed,isHOAllowed,refExternalEUtranCellTDDLTE,coperType,SubNetwork,ManagedElement,shareCover
#refExternalEUtranCellFDDLTE
EUtranRelationTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,refCUEUtranCellTDDLTE,refExternalEUtranCellFDDLTE,isX2HOAllowed,isHOAllowed,refExternalEUtranCellTDDLTE,coperType,SubNetwork,ManagedElement,shareCover
ExternalEUtranCellFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,cellLocalId,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcnUl,earfcnDl,ldn,bandWidthDl,bandWidthUl,freqBandInd
ExternalEUtranCellTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,cellLocalId,pci,tac,mcc,mnc,eNBId,cellLocalId,earfcn,earfcn,ldn,bandWidth,bandWidth,freqBandInd
QoSPBRMappingLTE=SubNetwork,ManagedElement,ENBCUCPFunction,qCI,lchDirection,pbrValue
EUtranMeasFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,offsetFreq,ldn
EUtranMeasTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,offsetFreq,ldn
EUtranCellMeasFDDLTE=SubNetwork,ManagedElement,ldn,handover4AtmosductCell
EUtranCellMeasTDDLTE=SubNetwork,ManagedElement,ldn,handover4AtmosductCell
UeEUtranMeasurementLTE=SubNetwork,ManagedElement,ENBCUCPFunction,Hysteresis,a3Offset,timeToTrigger,eventId
PhyChannelFDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,freqSelectUl,pucchIrcSwch
#CUEUtranCellTDDLTE
PhyChannelTDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,freqSelectUl,pucchIrcSwch
IrRruTxRxGroup=ldn,SubNetwork,ManagedElement,antGroupNo,refReplaceableUnit
AauTxRxGroup=SubNetwork,ManagedElement,ldn,refReplaceableUnit
ControlPlaneTimerLTE=SubNetwork,ManagedElement,ENBCUCPFunction,storeUEcntxtTimer
LoadMNGCellFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,prbLBExeThrdZUl,prbLBExeThrdZDl
LoadMNGCellTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,prbLBExeThrdZUl,prbLBExeThrdZDl
PowerControlDLFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,paForDTCH
PowerControlDLTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,paForDTCH
PowerControlULFDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
#CUEUtranCellTDDLTE
PowerControlULTDDLTE=SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,switchForCLPCofPUSCH,targetIOT,poNominalPUSCH1,alpha,p0UePusch1Pub
ECellEquipFuncFDDLTE=SubNetwork,ManagedElement,ENBDUFunction,ECellEquipFuncFDDLTE,cpSpeRefSigPwr,refBpPoolFunction,cpId,ldn,refSectorFunction,refSectorFunction,refSectorFunction,supercellFlagCopy,upActAntBitmapSeq,maxCPTransPwr
ECellEquipFuncTDDLTE=SubNetwork,ManagedElement,ENBDUFunction,ECellEquipFuncTDDLTE,cpSpeRefSigPwr,refBpPoolFunction,cpId,ldn,refSectorFunction,refSectorFunction,refSectorFunction,supercellFlagCopy,upActAntBitmapSeq,maxCPTransPwr
ReplaceableUnit=SubNetwork,ManagedElement,name,NULL,ldn,refSubRack,slotNo,ReplaceableUnit
BpPoolFunction=SubNetwork,ManagedElement,ldn,refReplaceableUnit
SectorFunction=SubNetwork,ManagedElement,ldn,refRxRfLink,refIrRruTxRxGroup,refAauTxRxGroup
RfLink=SubNetwork,ManagedElement,ldn,refRfPort
#ENBCUCPFunction
ENBCUCPFunction=SubNetwork,ManagedElement,ENBCUCPFunction,eNBId,enbName
UtranRelationFDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellFDDLTE,UtranRelationFDDLTE,description,refExternalUtranCellTDDLTE,refExternalUtranCellFDDLTE
UtranRelationTDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,CUEUtranCellTDDLTE,UtranRelationTDDLTE,description,refExternalUtranCellTDDLTE,refExternalUtranCellFDDLTE
ExternalUtranCellFDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,ExternalUtranCellFDDLTE,description,mcc,mnc,rncId,cId,freqBandInd,uarfcnUl,uarfcnDl
ExternalUtranCellTDDLTE=ldn,SubNetwork,ManagedElement,ENBCUCPFunction,ExternalUtranCellTDDLTE,description,mcc,mnc,rncId,cId,freqBandInd,uarfcn,uarfcn
#DualConnectionBear
DualConnectionBearLTE=ldn,SubNetwork,ManagedElement,qCI,dCBearerType
#ENDCX2Ap
ENDCX2Ap=ldn,SubNetwork,ManagedElement,refSctp
#ExternalNrCell
ExternalNrCellLTE=ldn,SubNetwork,ManagedElement,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,centerFreqDL,pci,sSBSCS,nrPointA,nrPointA,mcclist,mnclist,enDcCfgTAC
ExternalNrCellFDDLTE=ldn,SubNetwork,ManagedElement,cellLocalId,enDcCfgTAC,freqBandInd,gNodeBId,mcc,mnc,centerFreqDL,pci,sSBSCS,nrPointAUL,nrPointADL,mcclist,mnclist,enDcCfgTAC
#GlobleSwitchInformation
GlobalSwitch=ldn,SubNetwork,ManagedElement,switchForUserInactivity
#IpLayerConfig
Ip=ldn,SubNetwork,ManagedElement,ipAddress
#NrNiborRela
NrNiborRelaFDDLTE=ldn,SubNetwork,ManagedElement,swchNoENDC,refExternalNrCellLTE,refExternalNrCellFDDLTE
NrNiborRelaTDDLTE=ldn,SubNetwork,ManagedElement,swchNoENDC,refExternalNrCellLTE,refExternalNrCellFDDLTE
#QoSServiceClass
QoSServiceClassLTE=ldn,SubNetwork,ManagedElement,qCI,rlcMode,sequenNumLenth,ueRATType
#Sctp
Sctp=ldn,SubNetwork,ManagedElement,localPort,refIp,remoteIp,remotePort,assoType
