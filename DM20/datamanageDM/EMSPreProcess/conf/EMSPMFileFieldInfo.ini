
[cellkpi]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,Subnetwork,Cell,eNodeB,Product,RRCConnSuccs,RRCConnAttems,ERABEstSuccs,ERABEstAttems,HOOUTGOSuccs,HOOUTGOAttems,RRCRelAbnormals,RRCRelAll,ERABRelAbnormals,ERABRelAll,MaxRRCConUserNum,

[hotcell]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,SubNetWork,Cell,eNodeB,Product,C373343804,C373343805,C373343807,C373343806,C373424600,C373424601,C373424602,C373424603,C373424636,C373424640,C373596957,C373596956,C373424610,C373424611,C373384348,C374107514,C374107515,C374107516,DlConPrb<PERSON>atio,UlConPrbRatio,C373200030,C373424608,<PERSON>373424609,C373454802,<PERSON>373454803,<PERSON>373454800,C373454801,C373454822,ULMCS_ScheduleNum,C373454823,DLMCS_ScheduleNum,C373454807,C373454805,C373454814,C373454815,C373414597,VoLTE_Call_Setup_Rate_Numerator,VoLTE_Call_Setup_Rate_Denominator,VoLTE_Call_Drop_Rate_Numerator,VoLTE_Call_Drop_Rate_Denominator,CSFB_FallBack_Rate_Numerator,CSFB_FallBack_Rate_Denominator,

[neigbor]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,SubNetwork,CellID,eNodeBID,Relation,Product,Handovers,Handover_success_ratio,intra_hoout_try_times,intra_hoout_success_times,inter_hoout_try_times,inter_hoout_success_times,intra_ho_success_times,intra_ho_failure_times,

[supercell]
ColumnInfo=Index,StartTime,EndTime,,,CellID,eNodeB,,Mean_Number_of_RRC_Connection_User,Average_Cell_DL_PHY_Throughput_Kbps,PRB_Number_Available_on_PDSCH_Channel,Average_Cell_DL_PDCP_Throughput_Mbps,Average_Active_User_Number_on_User_Plane,PRB_Number_Used_on_PDSCH_Channel,Cell_DL_Aggregated_Volume_High_Mbit,Cell_DL_Aggregated_Volume_Low_Mbit

[fmreport]
ColumnInfo=Severity,AlarmType,,,,,AlarmCode,,RaisedTime,,,,,,,,,,,,,,,,,,,,,Duration,,ClearedTime,,,,,,,,CustomAttribute1,,CustomAttribute3,CustomAttribute4,,,,,,,,,,

[counter]
ColumnInfo=Index,StartTime,EndTime,QueryGranularity,Subnetwork,eNodeB,Product,PacketLoss,PacketOutOfOrder,PacketTotal,

[hotkpicell]
ColumnInfo=Index,StartTime,EndTime,Cell,eNodeB,product,DownlinkTraffic,ULlinkTraffic,CU,AU,InterRATHandover,Caps,ULPRBUsage,DLPRBUsage,

[hotkpienodeb]
ColumnInfo=Index,StartTime,EndTime,eNodeB,product,CPUPeakUtilization,CPUAverageUtilization,DurationRate,
[capacitymanagementcell]
ColumnInfo=Index,StartTime,EndTime,CellID,CellName,eNodeB,eNodeBName,,mtAccessRRCConSucNum,moDataRRCConSucNum,highPriorityAccessRRCConSucNum,emergencyRRCConSucNum,delayTolerantAccessRRCConSucNum,RRCConTime,ulAvgActiveUserNum,dlAvgActiveUserNum,RRCConAvgUserNum,MACAvgActUENum,cellULQCI1ActUENum,cellDLQCI1ActUENum,QCI1InitERABConSucNum,QCI1InitERABConFailNumENB,QCI1InitERABConFailNumAir,QCI1InitERABConFailNumSafeAct,QCI1InitERABConFailNumPara,QCI1InitERABConFailNumOther,QCI1InitERABConFailNumReCon,QCI1AddERABConSucNum,QCI1AddERABConFailNumENB,QCI1AddERABConFailNumAir,QCI1AddERABConFailNumHandOver,QCI1AddERABConFailNumPara,QCI1AddERABConFailNumOther,QCI1InitERABConFailNumSend,QCI1AddERABConFailNumReCon,QCI1AddERABConFailNumSend,QCI2InitERABConSucNum,QCI2InitERABConFailNumENB,QCI2InitERABConFailNumAir,QCI2InitERABConFailNumSafeAct,QCI2InitERABConFailNumPara,QCI2InitERABConFailNumOther,QCI3InitERABConSucNum,QCI3InitERABConFailNumENB,QCI3InitERABConFailNumAir,QCI3InitERABConFailNumSafeAct,QCI3InitERABConFailNumPara,QCI3InitERABConFailNumOther,QCI4InitERABConSucNum,QCI4InitERABConFailNumENB,QCI4InitERABConFailNumAir,QCI4InitERABConFailNumSafeAct,QCI4InitERABConFailNumPara,QCI4InitERABConFailNumOther,QCI5InitERABConSucNum,QCI5InitERABConFailNumENB,QCI5InitERABConFailNumAir,QCI5InitERABConFailNumSafeAct,QCI5InitERABConFailNumPara,QCI5InitERABConFailNumOther,QCI6InitERABConSucNum,QCI6InitERABConFailNumENB,QCI6InitERABConFailNumAir,QCI6InitERABConFailNumSafeAct,QCI6InitERABConFailNumPara,QCI6InitERABConFailNumOther,QCI7InitERABConSucNum,QCI7InitERABConFailNumENB,QCI7InitERABConFailNumAir,QCI7InitERABConFailNumSafeAct,QCI7InitERABConFailNumPara,QCI7InitERABConFailNumOther,QCI8InitERABConSucNum,QCI8InitERABConFailNumENB,QCI8InitERABConFailNumAir,QCI8InitERABConFailNumSafeAct,QCI8InitERABConFailNumPara,QCI8InitERABConFailNumOther,QCI9InitERABConSucNum,QCI9InitERABConFailNumENB,QCI9InitERABConFailNumAir,QCI9InitERABConFailNumSafeAct,QCI9InitERABConFailNumPara,QCI9InitERABConFailNumOther,QCI2AddERABConSucNum,QCI2AddERABConFailNumENB,QCI2AddERABConFailNumAir,QCI2AddERABConFailNumHandOver,QCI2AddERABConFailNumPara,QCI2AddERABConFailNumOther,QCI3AddERABConSucNum,QCI3AddERABConFailNumENB,QCI3AddERABConFailNumAir,QCI3AddERABConFailNumHandOver,QCI3AddERABConFailNumPara,QCI3AddERABConFailNumOther,QCI4AddERABConSucNum,QCI4AddERABConFailNumENB,QCI4AddERABConFailNumAir,QCI4AddERABConFailNumHandOver,QCI4AddERABConFailNumPara,QCI4AddERABConFailNumOther,QCI5AddERABConSucNum,QCI5AddERABConFailNumENB,QCI5AddERABConFailNumAir,QCI5AddERABConFailNumHandOver,QCI5AddERABConFailNumPara,QCI5AddERABConFailNumOther,QCI6AddERABConSucNum,QCI6AddERABConFailNumENB,QCI6AddERABConFailNumAir,QCI6AddERABConFailNumHandOver,QCI6AddERABConFailNumPara,QCI6AddERABConFailNumOther,QCI7AddERABConSucNum,QCI7AddERABConFailNumENB,QCI7AddERABConFailNumAir,QCI7AddERABConFailNumHandOver,QCI7AddERABConFailNumPara,QCI7AddERABConFailNumOther,QCI8AddERABConSucNum,QCI8AddERABConFailNumENB,QCI8AddERABConFailNumAir,QCI8AddERABConFailNumHandOver,QCI8AddERABConFailNumPara,QCI8AddERABConFailNumOther,QCI9AddERABConSucNum,QCI9AddERABConFailNumENB,QCI9AddERABConFailNumAir,QCI9AddERABConFailNumHandOver,QCI9AddERABConFailNumPara,QCI9AddERABConFailNumOther,cellULQCI1PDCHSDUDataLow,cellULQCI1DataSendTime,cellULQCI2PDCHSDUDataHigh,cellULQCI2PDCHSDUDataLow,cellULQCI2DataSendTime,cellULQCI3PDCHSDUDataHigh,cellULQCI3PDCHSDUDataLow,cellULQCI3DataSendTime,cellULQCI4PDCHSDUDataHigh,cellULQCI4PDCHSDUDataLow,cellULQCI4DataSendTime,cellULQCI5PDCHSDUDataHigh,cellULQCI5PDCHSDUDataLow,cellULQCI5DataSendTime,cellULQCI6PDCHSDUDataHigh,cellULQCI6PDCHSDUDataLow,cellULQCI6DataSendTime,cellULQCI7PDCHSDUDataHigh,cellULQCI7PDCHSDUDataLow,cellULQCI7DataSendTime,cellULQCI8PDCHSDUDataHigh,cellULQCI8PDCHSDUDataLow,cellULQCI8DataSendTime,cellULQCI9PDCHSDUDataHigh,cellULQCI9PDCHSDUDataLow,cellULQCI9DataSendTime,cellDLQCI1PDCHSDUDataHigh,cellDLQCI1PDCHSDUDataLow,cellDLQCI1DataSendTime,cellDLQCI2PDCHSDUDataHigh,cellDLQCI2PDCHSDUDataLow,cellDLQCI2DataSendTime,cellDLQCI3PDCHSDUDataHigh,cellDLQCI3PDCHSDUDataLow,cellDLQCI3DataSendTime,cellDLQCI4PDCHSDUDataHigh,cellDLQCI4PDCHSDUDataLow,cellDLQCI4DataSendTime,cellDLQCI5PDCHSDUDataHigh,cellDLQCI5PDCHSDUDataLow,cellDLQCI5DataSendTime,cellDLQCI6PDCHSDUDataHigh,cellDLQCI6PDCHSDUDataLow,cellDLQCI6DataSendTime,cellDLQCI7PDCHSDUDataHigh,cellDLQCI7PDCHSDUDataLow,cellDLQCI7DataSendTime,cellDLQCI8PDCHSDUDataHigh,cellDLQCI8PDCHSDUDataLow,cellDLQCI8DataSendTime,cellDLQCI9PDCHSDUDataHigh,cellDLQCI9PDCHSDUDataLow,cellDLQCI9DataSendTime,cellULQCI1PDCHSDUDataHigh,cellULPDCPAvgThrough,cellDLPDCPAvgThrough,cellULServicePRBNum,cellULPRBNum,cellDLServicePRBNum,cellDLPRBNum,cellPDCCHServiceCCENum,cellPDCCHCCENum,ULAvgActERABNum,DLAvgActERABNum,eNB1cell2ConfreHandoverInSucNum,eNB2X2cell2ConfrehandoverInSucNum,eNB2S1cell2ConfrehandoverInSucNum,eNB1cell2DisfreHandoverInSucNum,eNB2X2cell2DisfrehandoverInSucNum,eNB2S1cell2DisfrehandoverInSucNum,UTRANToLTEHandoverInSucNum,CMDAToLTEHandoverInSucNum,GSMToLTEHandoverInSucNum,eNB1cell2ConfreHandoverOutSucNum,eNB2X2cell2ConfreHandoverOutSucNum,eNB2S1cell2ConfreHandoverOutSucNum,eNB1cell2DisfreHandoverOutSucNum,eNB2X2cell2DisfreHandoverOutSucNum,eNB2S1cell2DisfreHandoverOutSucNum,LTEToUTRANHandoverOutSucNum,LTEToCMDAHandoverOutSucNum,LTEToGSMHandoverOutSucNum,CellPagingCongestionNum,CellPagingReceivedNum,ULQPSKTBNum,UL16QAMTBNum,UL64QAMTBNum,DLQPSKTBNum,DL16QAMTBNum,DL64QAMTBNum,PUCCHSR,PUCCHCQI,PUCCHCQIAll,PUCCHSRAll,RRCConMAXUserNum,QCI2InitERABConFailNumReCon,QCI3InitERABConFailNumReCon,QCI4InitERABConFailNumReCon,QCI5InitERABConFailNumReCon,QCI6InitERABConFailNumReCon,QCI7InitERABConFailNumReCon,QCI8InitERABConFailNumReCon,QCI9InitERABConFailNumReCon,QCI2AddERABConFailNumReCon,QCI3AddERABConFailNumReCon,QCI4AddERABConFailNumReCon,QCI5AddERABConFailNumReCon,QCI6AddERABConFailNumReCon,QCI7AddERABConFailNumReCon,QCI8AddERABConFailNumReCon,QCI9AddERABConFailNumReCon,QCI2InitERABConFailNumSend,QCI3InitERABConFailNumSend,QCI4InitERABConFailNumSend,QCI5InitERABConFailNumSend,QCI6InitERABConFailNumSend,QCI7InitERABConFailNumSend,QCI8InitERABConFailNumSend,QCI9InitERABConFailNumSend,QCI2AddERABConFailNumSend,QCI3AddERABConFailNumSend,QCI4AddERABConFailNumSend,QCI5AddERABConFailNumSend,QCI6AddERABConFailNumSend,QCI7AddERABConFailNumSend,QCI8AddERABConFailNumSend,QCI9AddERABConFailNumSend,moSignallingRRCConSucNum,cellULQCI2ActUENum,cellULQCI3ActUENum,cellULQCI4ActUENum,cellULQCI5ActUENum,cellULQCI6ActUENum,cellULQCI7ActUENum,cellULQCI8ActUENum,cellULQCI9ActUENum,cellDLQCI2ActUENum,cellDLQCI3ActUENum,cellDLQCI4ActUENum,cellDLQCI5ActUENum,cellDLQCI6ActUENum,cellDLQCI7ActUENum,cellDLQCI8ActUENum,cellDLQCI9ActUENum,RRCConnectionCurrentUserNum,RRCConnectionLicenseUserNum,ULPRBAverageNum,ULPUSCHPRBAverageNum,DLPRBAverageNum,DLPUSCHPRBAverageNum
[capacitymanagementENB]
ColumnInfo=Index,StartTime,EndTime,eNodeB,eNodeBName,,eNBCCAvgCPUUtilizationRate,eNBDLGtpuPackLossNum,eNBDLGtpuPackOutOrderNum,eNBDLGtpuPackTotalNum,
[CellCoverage]
ColumnInfo=Index,StartTime,EndTime,CellID,CellName,eNodeB,eNodeBName,,ULPDCPSDULossNumberQCI1,ULPDCPSDULossNumberQCI2,ULPDCPSDULossNumberQCI3,ULPDCPSDULossNumberQCI4,ULPDCPSDULossNumberQCI5,ULPDCPSDULossNumberQCI6,ULPDCPSDULossNumberQCI7,ULPDCPSDULossNumberQCI8,ULPDCPSDULossNumberQCI9,DLPDCPSDULossNumberQCI1,DLPDCPSDULossNumberQCI2,DLPDCPSDULossNumberQCI3,DLPDCPSDULossNumberQCI4,DLPDCPSDULossNumberQCI5,DLPDCPSDULossNumberQCI6,DLPDCPSDULossNumberQCI7,DLPDCPSDULossNumberQCI8,DLPDCPSDULossNumberQCI9,DLPDCPSDUDiscardNumberQCI1,DLPDCPSDUDiscardNumberQCI2,DLPDCPSDUDiscardNumberQCI3,DLPDCPSDUDiscardNumberQCI4,DLPDCPSDUDiscardNumberQCI5,DLPDCPSDUDiscardNumberQCI6,DLPDCPSDUDiscardNumberQCI7,DLPDCPSDUDiscardNumberQCI8,DLPDCPSDUDiscardNumberQCI9,ULPDCPSDUNumberQCI1,ULPDCPSDUNumberQCI2,ULPDCPSDUNumberQCI3,ULPDCPSDUNumberQCI4,ULPDCPSDUNumberQCI5,ULPDCPSDUNumberQCI6,ULPDCPSDUNumberQCI7,ULPDCPSDUNumberQCI8,ULPDCPSDUNumberQCI9,DLPDCPSDUNumberQCI1,DLPDCPSDUNumberQCI2,DLPDCPSDUNumberQCI3,DLPDCPSDUNumberQCI4,DLPDCPSDUNumberQCI5,DLPDCPSDUNumberQCI6,DLPDCPSDUNumberQCI7,DLPDCPSDUNumberQCI8,DLPDCPSDUNumberQCI9,
[worstkpicell]
ColumnInfo=Index,StartTime,EndTime,,,,,,Cell,,eNodeB,,product,RRC_SR,ERAB_SR,ERAB_V_SR,S1_Setup_SR,SSSR,Volte_HO_TransR,CSSR_CSFB,CSSR_Volte,Call_Drop_Rate,IntraF_HO_Out_SR,InterF_HO_Out_SR,HO_In_SR,Redirectionto3G_Rate,RRC_Connected_User_MAX,RRC_Connected_User_AVG,DLRB_UsageRate,ULRB_UsageRate,Scheduling_Thrp_DL,Scheduling_Thrp_UL,AVG_CQI_DL,Traffic_Volume_DL,Traffic_Volume_UL,Active_User_MAX,Active_User_AVG,
[worstkpienodeb]
ColumnInfo=Index,StartTime,EndTime,,,,,,eNodeB,,,eNB_CPU_Usage_MAX,eNB_CPU_Usage_AVG,
[capacitymanagement15mincell]
ColumnInfo=Index,StartTime,EndTime,,,,,,CellID,,eNodeB,,RRCConnectionCurrentUserNum,RRCConnectionLicenseUserNum,ULPRBAverageNum,ULPUSCHPRBAverageNum,DLPRBAverageNum,DLPUSCHPRBAverageNum
[rssi15mincell]
ColumnInfo=,StartTime,EndTime,,Subnetwork,,ManagedElement,,Rack,Shelf,Slot,Channel,Product,,,,MaxRSSIRRUCarrier1,MinRSSIRRUCarrier1,AvgRSSIRRUCarrier1
[kpicell]
ColumnInfo=,StartTime,EndTime,Cell,eNodeB,,ErabVolume,RRCAU,PUSCHUsage,PDSCHUsage,PDCCHUsage,ULVolume,DLVolume,CellULQCI1AvgActUENum,CellULQCI1MaxActUENum,CellDLQCI1AvgActUENum,CellDLQCI1MaxActUENum,MaxActUENum,RRCConMaxUENum,RRCConAvgUENum,C373230700,C373230701,C373230702,C373230703,C373230704,C373230705,C373230706

[intersysinterference]
ColumnInfo=,StartTime,EndTime,,CellID,,eNodeBID,,,rb03AvgNI,rb34AvgNI,rb96AvgNI,auDuration,auCount,rbAvgNI,ulPrbUseRatio