#!/bin/sh

#当前目录netmax/platform/datamanage/DM20/datamanageDM
SourcePath=$( cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
#ngi/doc目录
ngiOpenDBPath=../../../../../ngi/doc
#前端代码路径(本地代码路径需要注意修改)
NovPath=../DM-NOVisualization
#输入版本号
Version=$1
# Started by AICoder, pid:gf8efl46c1zc4f514c01094c50a5d32a42f64df5
#编译前端
#切前端代码目录
cd "${NovPath}"
#call npm install
#npm config set sass_binary_site https://artnj.zte.com.cn/artifactory/api/npm/zenap-npm-virtual/
echo "当前用户："
whoami
npm cache clean --force
npm cache verify --force
rm -rf node_modules package-lock.json dist
npm install --unsafe-perm --force
npm run build --unsafe-perm
if [ $? -ne 0 ]; then
        echo "前端安装编译失败"
        exit -1
fi
# Ended by AICoder, pid:gf8efl46c1zc4f514c01094c50a5d32a42f64df5
#rd /s /q %{SourcePath}%/src/main/resources
rm -rf "${SourcePath}/src/main/resources"
#md %{SourcePath}%/src/main/resources/DM-NOVisualization
mkdir -p "${SourcePath}/src/main/resources/DM-NOVisualization"

#xcopy "${NovPath}/dist/*" "${SourcePath}/src/main/resources/DM-NOVisualization"
cp -rf "${NovPath}/dist/." "${SourcePath}/src/main/resources/DM-NOVisualization"

#编译后端代码
cd "${SourcePath}"
#call mvn clean package
mvn clean package
if [ $? -ne 0 ]; then
        exit -1
fi

#移除Swagger服务
zip -d "${SourcePath}/target/datamanage-newcode-1.0-SNAPSHOT.jar" com/zte/ums/zenap/swagger/SwaggerManager.class

#编译SFTP
cd "${SourcePath}/../SFTPServer"
mvn clean package
#编译NGI监控
cd "${SourcePath}/../NGIDBMonitor"
mvn clean package

#编译DataDownloadMonitor
cd "${SourcePath}/../DataDownloadMonitor"
mvn clean package

#编译alertManager
cd "${SourcePath}/../alertManager"
mvn clean package

#"copy jar..."
cd "${SourcePath}"
mkdir "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/target/datamanage-newcode-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest"

#"copy files..."
cp -rf "${SourcePath}/StartDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/StartDatamanageNew.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/StopDatamanageNew.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/featureusagestat.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/datamanage.keystore" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/UnistallDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/UnistallDatamanagefirst.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/gpsshDatamanageStart.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/gpsshDatamanageStop.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/version.cc" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/openssl.cnf" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/BackupDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/OperateTableSpace.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/SSDVolumePath.cfg" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/EmsDispatcher.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/clearCMrecord.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/dataReuse.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/collectLog.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/rebootStart.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/rebootStart.cron" "${SourcePath}/datamanagetest"

mkdir "${SourcePath}/datamanagetest/sql_script"
cp -rf  "${SourcePath}/sql_script/." "${SourcePath}/datamanagetest/sql_script"

mkdir "${SourcePath}/datamanagetest/data"
cp -rf "${SourcePath}/data/." "${SourcePath}/datamanagetest/data"

mkdir "${SourcePath}/datamanagetest/conf"
cp -rf "${SourcePath}/conf/." "${SourcePath}/datamanagetest/conf"
mkdir "${SourcePath}/datamanagetest/bin"
cp -rf "${SourcePath}/bin/." "${SourcePath}/datamanagetest/bin"
# 更新file2sp.xml文件
zip -u -j "${SourcePath}/datamanagetest/bin/DataManage/ImportData_LTE.jar" "${SourcePath}/Server/LTE/DataManage/ImportData_GP/src/main/resources/ltefile2sp.xml"
zip -u -j "${SourcePath}/datamanagetest/bin/DataManage/ImportData_5G.jar" "${SourcePath}/Server/5G/DataManage/ImportData_GP/src/main/resources/g5file2sp.xml"
mkdir "${SourcePath}/datamanagetest/versionbak"
cp -rf "${SourcePath}/versionbak/." "${SourcePath}/datamanagetest/versionbak"
mkdir "${SourcePath}/datamanagetest/DB"
cp -rf "${SourcePath}/../DB/." "${SourcePath}/datamanagetest/DB"
mkdir "${SourcePath}/datamanagetest/EMSPreProcess"
cp -rf "${SourcePath}/EMSPreProcess/." "${SourcePath}/datamanagetest/EMSPreProcess"
mkdir "${SourcePath}/datamanagetest/NRUMEPreProcess"
cp -rf "${SourcePath}/NRUMEPreProcess/." "${SourcePath}/datamanagetest/NRUMEPreProcess"

mkdir "${SourcePath}/datamanagetest/SFTPServer"
cp -rf "${SourcePath}/SFTPServer/." "${SourcePath}/datamanagetest/SFTPServer"
cp -rf "${SourcePath}/../SFTPServer/target/lib/." "${SourcePath}/datamanagetest/SFTPServer/lib"
# 更新 sshd-sftp-2.1.0.jar 文件，解决zxjdk8u282及之后版本不兼容LinkOption.NOFOLLOW_LINKS，导致sftp命令上传文件失败的问题
# https://i.zte.com.cn/#/space/ee6e3a1534c6478ab944c0664639253f/wiki/page/66dcd7ba5d2d4ce69619ee53521aa770/view
replaceFilePath="${SourcePath}/../SFTPServer/sshd-sftp-2.1.0.jar.zipe"
if [ -f "$replaceFilePath" ];then
    zipKey=$(unzip -q -z "$replaceFilePath" | awk '{print $2}')
    if [ "$zipKey" ];then
        unzip -o -P "$zipKey" "$replaceFilePath" -d "${SourcePath}/datamanagetest/SFTPServer/lib"
    fi
fi
cp -rf "${SourcePath}/../SFTPServer/target/SFTPServer-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/SFTPServer/lib"

#NGIDBMonitor打包 
mkdir "${SourcePath}/datamanagetest/NGIDBMonitor"
cp -rf "${SourcePath}/NGIDBMonitor/." "${SourcePath}/datamanagetest/NGIDBMonitor"
cp -rf "${SourcePath}/../NGIDBMonitor/target/lib/." "${SourcePath}/datamanagetest/NGIDBMonitor/lib"
cp -rf "${SourcePath}/../NGIDBMonitor/target/NGIDBMonitor-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/NGIDBMonitor/bin"

#alertManager打包 
mkdir "${SourcePath}/datamanagetest/alertOperator"
cp -rf "${SourcePath}/alertOperator/." "${SourcePath}/datamanagetest/alertOperator"
mv -f "${SourcePath}/../alertManager/target/alertManager-1.0-SNAPSHOT.jar" "${SourcePath}/../alertManager/target/alertOperator-1.0-SNAPSHOT.jar"
cp -rf "${SourcePath}/../alertManager/target/alertOperator-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/alertOperator/bin"

#DataDownloadMonitor打包 
mkdir "${SourcePath}/datamanagetest/DataDownloadMonitor"
cp -rf "${SourcePath}/DataDownloadMonitor/." "${SourcePath}/datamanagetest/DataDownloadMonitor"
cp -rf "${SourcePath}/../DataDownloadMonitor/target/lib/." "${SourcePath}/datamanagetest/DataDownloadMonitor/lib"
cp -rf "${SourcePath}/../DataDownloadMonitor/target/DataDownloadMonitor-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/DataDownloadMonitor/bin"

#CMJobQueue打包
mkdir "${SourcePath}/datamanagetest/CMJobQueue"
cp -rf "${SourcePath}/CMJobQueue/." "${SourcePath}/datamanagetest/CMJobQueue/"

#ApPackage打包
mkdir "${SourcePath}/datamanagetest/ApPackage"
cp -rvf "${SourcePath}/ApPackage/." "${SourcePath}/datamanagetest/ApPackage/"

#"zip files..."
rm -rf datamanage-newcode.zip
rm -rf datamanage-service.zip

cd "${SourcePath}/datamanagetest"
zip -r "datamanage-newcode.zip" *
cp "datamanage-newcode.zip" "${SourcePath}"
cd "${SourcePath}"
zip -r "datamanage-service.zip" "datamanage-newcode.zip" "InstallDatamanage.sh" "SSDVolumePath.cfg"

#"clean..."
rm -rf datamanage-newcode.zip
rm -rf "${SourcePath}/datamanagetest"

#将datamanage-service.zip打入ngi数据库包
mkdir -p "${ngiOpenDBPath}/s1"
mkdir -p "${ngiOpenDBPath}/s2"
mkdir -p "${ngiOpenDBPath}/s3"
cp -rf "datamanage-service.zip" "${ngiOpenDBPath}/s1"
mv -f "${ngiOpenDBPath}/gpdb/GP-INSTALL-2.0/change.list" "${ngiOpenDBPath}/s2"
cp -rf "${ngiOpenDBPath}/gpdb/GP-INSTALL-2.0/." "${ngiOpenDBPath}/s1"

cd "${ngiOpenDBPath}/s1"
tar czf ../s2/ngi-open-gpdb-${Version}.tar.gz *

cd "../s2"
tar czf ../s3/ngi-open-gpdb-${Version}.tar.gz *



#"===== build ServerAndCilent End====="
