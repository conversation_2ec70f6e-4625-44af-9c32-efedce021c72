# 数据管理工程 - DDD完整代码示例

## 📋 目录结构

```
src/main/java/com/zte/
├── domain/
│   ├── datamanage/
│   │   ├── aggregate/
│   │   │   ├── DataImportTask.java
│   │   │   └── DataCleanupTask.java
│   │   ├── valueobject/
│   │   │   ├── DataSource.java
│   │   │   ├── DataRange.java
│   │   │   ├── ImportProgress.java
│   │   │   ├── TaskId.java
│   │   │   ├── TaskName.java
│   │   │   └── Creator.java
│   │   ├── entity/
│   │   │   ├── ExecutionStrategy.java
│   │   │   ├── TaskLog.java
│   │   │   └── CleanupStrategy.java
│   │   ├── service/
│   │   │   ├── DataImportService.java
│   │   │   └── DataCleanupService.java
│   │   └── repository/
│   │       └── TaskRepository.java
│   └── common/
│       ├── valueobject/
│       │   ├── TaskStatus.java
│       │   ├── TaskType.java
│       │   └── LogLevel.java
│       └── exception/
│           └── DomainException.java
├── application/
│   ├── dto/
│   │   ├── CreateImportTaskCommand.java
│   │   └── ImportResultDTO.java
│   └── service/
│       └── DataImportApplicationService.java
└── infrastructure/
    ├── repository/
    │   └── TaskRepositoryImpl.java
    └── adapter/
        └── DataSourceAdapter.java
```

## 🏗️ 值对象（Value Objects）实现

### 1. 基础值对象

#### TaskId.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.util.Objects;

/**
 * 任务ID值对象
 */
@Value
public class TaskId {
    private final String value;
    
    public TaskId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        this.value = value.trim();
    }
    
    public static TaskId generate() {
        return new TaskId(java.util.UUID.randomUUID().toString());
    }
    
    public static TaskId from(String value) {
        return new TaskId(value);
    }
}
```

#### TaskName.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.util.Objects;

/**
 * 任务名称值对象
 */
@Value
public class TaskName {
    private final String value;
    
    public TaskName(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (value.trim().length() > 100) {
            throw new IllegalArgumentException("任务名称不能超过100个字符");
        }
        this.value = value.trim();
    }
    
    public static TaskName from(String value) {
        return new TaskName(value);
    }
}
```

#### Creator.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.util.Objects;

/**
 * 创建者值对象
 */
@Value
public class Creator {
    private final String value;
    
    public Creator(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("创建者不能为空");
        }
        if (value.trim().length() > 50) {
            throw new IllegalArgumentException("创建者名称不能超过50个字符");
        }
        this.value = value.trim();
    }
    
    public static Creator from(String value) {
        return new Creator(value);
    }
}
```

### 2. 业务值对象

#### DataSource.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 数据源值对象
 */
@Value
public class DataSource {
    private final String protocol;
    private final String host;
    private final Integer port;
    private final String username;
    private final String encryptedPassword;
    private final String path;
    private final DataSourceType type;
    
    // 支持的协议类型
    public enum DataSourceType {
        NDS, PM, CM, FM, VMAX
    }
    
    public DataSource(String protocol, String host, Integer port, 
                     String username, String encryptedPassword, 
                     String path, DataSourceType type) {
        // 参数验证
        validateProtocol(protocol);
        validateHost(host);
        validatePort(port);
        validateUsername(username);
        validateType(type);
        
        this.protocol = protocol.trim().toUpperCase();
        this.host = host.trim();
        this.port = port;
        this.username = username.trim();
        this.encryptedPassword = encryptedPassword;
        this.path = path != null ? path.trim() : "";
        this.type = type;
    }
    
    private void validateProtocol(String protocol) {
        if (protocol == null || protocol.trim().isEmpty()) {
            throw new IllegalArgumentException("协议类型不能为空");
        }
        String validProtocol = protocol.trim().toUpperCase();
        if (!validProtocol.equals("SFTP") && !validProtocol.equals("FTP") && 
            !validProtocol.equals("HTTP") && !validProtocol.equals("HTTPS")) {
            throw new IllegalArgumentException("不支持的协议类型: " + protocol);
        }
    }
    
    private void validateHost(String host) {
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("主机地址不能为空");
        }
        // 简单的IP地址或域名验证
        String hostPattern = "^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*" +
                           "([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9])$|" +
                           "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
                           "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        if (!Pattern.matches(hostPattern, host.trim())) {
            throw new IllegalArgumentException("无效的主机地址: " + host);
        }
    }
    
    private void validatePort(Integer port) {
        if (port == null || port < 1 || port > 65535) {
            throw new IllegalArgumentException("端口号必须在1-65535之间");
        }
    }
    
    private void validateUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
    }
    
    private void validateType(DataSourceType type) {
        if (type == null) {
            throw new IllegalArgumentException("数据源类型不能为空");
        }
    }
    
    // 业务方法
    
    /**
     * 验证数据源配置是否有效
     */
    public boolean isValid() {
        return !protocol.isEmpty() && !host.isEmpty() && 
               port != null && port > 0 && !username.isEmpty();
    }
    
    /**
     * 获取连接字符串
     */
    public String getConnectionString() {
        return String.format("%s://%s:%d%s", protocol, host, port, path);
    }
    
    /**
     * 检查是否支持指定协议
     */
    public boolean supportsProtocol(String protocolToCheck) {
        return this.protocol.equalsIgnoreCase(protocolToCheck);
    }
    
    /**
     * 获取连接信息
     */
    public ConnectionInfo getConnectionInfo() {
        return new ConnectionInfo(protocol, host, port, username, encryptedPassword);
    }
    
    /**
     * 连接信息内部类
     */
    @Value
    public static class ConnectionInfo {
        String protocol;
        String host;
        Integer port;
        String username;
        String encryptedPassword;
    }
    
    /**
     * 创建数据源构建器
     */
    public static DataSourceBuilder builder() {
        return new DataSourceBuilder();
    }
    
    /**
     * 数据源构建器
     */
    public static class DataSourceBuilder {
        private String protocol;
        private String host;
        private Integer port;
        private String username;
        private String encryptedPassword;
        private String path;
        private DataSourceType type;
        
        public DataSourceBuilder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }
        
        public DataSourceBuilder host(String host) {
            this.host = host;
            return this;
        }
        
        public DataSourceBuilder port(Integer port) {
            this.port = port;
            return this;
        }
        
        public DataSourceBuilder username(String username) {
            this.username = username;
            return this;
        }
        
        public DataSourceBuilder encryptedPassword(String encryptedPassword) {
            this.encryptedPassword = encryptedPassword;
            return this;
        }
        
        public DataSourceBuilder path(String path) {
            this.path = path;
            return this;
        }
        
        public DataSourceBuilder type(DataSourceType type) {
            this.type = type;
            return this;
        }
        
        public DataSource build() {
            return new DataSource(protocol, host, port, username, 
                               encryptedPassword, path, type);
        }
    }
}
```

#### DataRange.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;

/**
 * 数据范围值对象
 */
@Value
public class DataRange {
    private final TimeRangeType timeRangeType;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final TimeGranularity granularity;
    private final List<String> cellList;
    private final List<String> frequencyList;
    
    // 时间范围类型
    public enum TimeRangeType {
        RELATIVE,    // 相对时间
        ABSOLUTE     // 绝对时间
    }
    
    // 时间粒度
    public enum TimeGranularity {
        MINUTE(60),      // 分钟
        HOUR(3600),      // 小时
        DAY(86400),      // 天
        WEEK(604800),    // 周
        MONTH(2592000);  // 月
        
        private final int seconds;
        
        TimeGranularity(int seconds) {
            this.seconds = seconds;
        }
        
        public int getSeconds() {
            return seconds;
        }
    }
    
    public DataRange(TimeRangeType timeRangeType, LocalDateTime startTime, 
                    LocalDateTime endTime, TimeGranularity granularity,
                    List<String> cellList, List<String> frequencyList) {
        // 参数验证
        validateTimeRange(timeRangeType, startTime, endTime);
        validateGranularity(granularity);
        
        this.timeRangeType = timeRangeType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.granularity = granularity;
        this.cellList = cellList != null ? new ArrayList<>(cellList) : new ArrayList<>();
        this.frequencyList = frequencyList != null ? new ArrayList<>(frequencyList) : new ArrayList<>();
    }
    
    private void validateTimeRange(TimeRangeType timeRangeType, 
                                 LocalDateTime startTime, LocalDateTime endTime) {
        if (timeRangeType == null) {
            throw new IllegalArgumentException("时间范围类型不能为空");
        }
        if (startTime == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        if (endTime == null) {
            throw new IllegalArgumentException("结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        
        // 验证时间范围是否合理
        Duration duration = Duration.between(startTime, endTime);
        if (duration.toDays() > 365) {
            throw new IllegalArgumentException("时间范围不能超过一年");
        }
    }
    
    private void validateGranularity(TimeGranularity granularity) {
        if (granularity == null) {
            throw new IllegalArgumentException("时间粒度不能为空");
        }
    }
    
    // 业务方法
    
    /**
     * 验证时间范围是否有效
     */
    public boolean isValidRange() {
        return startTime != null && endTime != null && 
               startTime.isBefore(endTime) && granularity != null;
    }
    
    /**
     * 获取时间范围持续时间
     */
    public Duration getDuration() {
        return Duration.between(startTime, endTime);
    }
    
    /**
     * 检查指定时间是否在范围内
     */
    public boolean contains(LocalDateTime time) {
        if (time == null) return false;
        return !time.isBefore(startTime) && !time.isAfter(endTime);
    }
    
    /**
     * 检查是否与另一个时间范围重叠
     */
    public boolean overlapsWith(DataRange other) {
        if (other == null) return false;
        return !this.endTime.isBefore(other.startTime) && 
               !this.startTime.isAfter(other.endTime);
    }
    
    /**
     * 按时间粒度分割时间范围
     */
    public List<DataRange> splitByGranularity() {
        List<DataRange> ranges = new ArrayList<>();
        LocalDateTime currentStart = startTime;
        
        while (currentStart.isBefore(endTime)) {
            LocalDateTime currentEnd = currentStart.plusSeconds(granularity.getSeconds());
            if (currentEnd.isAfter(endTime)) {
                currentEnd = endTime;
            }
            
            ranges.add(new DataRange(timeRangeType, currentStart, currentEnd, 
                                   granularity, cellList, frequencyList));
            currentStart = currentEnd;
        }
        
        return ranges;
    }
    
    /**
     * 获取网元列表
     */
    public List<String> getCellList() {
        return Collections.unmodifiableList(cellList);
    }
    
    /**
     * 获取频点列表
     */
    public List<String> getFrequencyList() {
        return Collections.unmodifiableList(frequencyList);
    }
    
    /**
     * 检查是否包含指定网元
     */
    public boolean containsCell(String cell) {
        return cellList.contains(cell);
    }
    
    /**
     * 检查是否包含指定频点
     */
    public boolean containsFrequency(String frequency) {
        return frequencyList.contains(frequency);
    }
    
    /**
     * 获取时间范围描述
     */
    public String getDescription() {
        return String.format("%s时间范围: %s 至 %s (粒度: %s)", 
            timeRangeType == TimeRangeType.RELATIVE ? "相对" : "绝对",
            startTime.toString(), endTime.toString(), granularity);
    }
    
    /**
     * 创建数据范围构建器
     */
    public static DataRangeBuilder builder() {
        return new DataRangeBuilder();
    }
    
    /**
     * 数据范围构建器
     */
    public static class DataRangeBuilder {
        private TimeRangeType timeRangeType;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private TimeGranularity granularity;
        private List<String> cellList;
        private List<String> frequencyList;
        
        public DataRangeBuilder timeRangeType(TimeRangeType timeRangeType) {
            this.timeRangeType = timeRangeType;
            return this;
        }
        
        public DataRangeBuilder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }
        
        public DataRangeBuilder endTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }
        
        public DataRangeBuilder granularity(TimeGranularity granularity) {
            this.granularity = granularity;
            return this;
        }
        
        public DataRangeBuilder cellList(List<String> cellList) {
            this.cellList = cellList;
            return this;
        }
        
        public DataRangeBuilder frequencyList(List<String> frequencyList) {
            this.frequencyList = frequencyList;
            return this;
        }
        
        public DataRange build() {
            return new DataRange(timeRangeType, startTime, endTime, 
                               granularity, cellList, frequencyList);
        }
    }
}
```

#### ImportProgress.java
```java
package com.zte.domain.datamanage.valueobject;

import lombok.Value;
import java.util.Objects;

/**
 * 导入进度值对象
 */
@Value
public class ImportProgress {
    private final int processedRecords;
    private final int totalRecords;
    
    public ImportProgress(int processedRecords, int totalRecords) {
        if (processedRecords < 0) {
            throw new IllegalArgumentException("已处理记录数不能为负数");
        }
        if (totalRecords < 0) {
            throw new IllegalArgumentException("总记录数不能为负数");
        }
        if (processedRecords > totalRecords) {
            throw new IllegalArgumentException("已处理记录数不能超过总记录数");
        }
        
        this.processedRecords = processedRecords;
        this.totalRecords = totalRecords;
    }
    
    // 业务方法
    
    /**
     * 获取完成百分比
     */
    public double getPercentage() {
        if (totalRecords == 0) return 0.0;
        return (double) processedRecords / totalRecords * 100;
    }
    
    /**
     * 获取剩余记录数
     */
    public int getRemainingRecords() {
        return totalRecords - processedRecords;
    }
    
    /**
     * 检查是否完成
     */
    public boolean isCompleted() {
        return processedRecords == totalRecords && totalRecords > 0;
    }
    
    /**
     * 检查是否已开始
     */
    public boolean isStarted() {
        return processedRecords > 0;
    }
    
    /**
     * 获取进度描述
     */
    public String getDescription() {
        if (totalRecords == 0) {
            return "等待开始";
        }
        return String.format("%d/%d (%.1f%%)", processedRecords, totalRecords, getPercentage());
    }
    
    /**
     * 创建进度更新
     */
    public ImportProgress withProcessedRecords(int newProcessedRecords) {
        return new ImportProgress(newProcessedRecords, totalRecords);
    }
    
    /**
     * 增加已处理记录数
     */
    public ImportProgress incrementProcessedRecords(int increment) {
        return new ImportProgress(processedRecords + increment, totalRecords);
    }
    
    /**
     * 设置总记录数
     */
    public ImportProgress withTotalRecords(int newTotalRecords) {
        return new ImportProgress(Math.min(processedRecords, newTotalRecords), newTotalRecords);
    }
    
    /**
     * 创建初始进度
     */
    public static ImportProgress initial() {
        return new ImportProgress(0, 0);
    }
    
    /**
     * 创建已知总记录数的初始进度
     */
    public static ImportProgress withTotal(int totalRecords) {
        return new ImportProgress(0, totalRecords);
    }
}
```

## 🏛️ 实体（Entities）实现

### 1. ExecutionStrategy.java
```java
package com.zte.domain.datamanage.entity;

import com.zte.domain.datamanage.valueobject.TaskId;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 执行策略实体
 */
@Data
@Slf4j
public class ExecutionStrategy {
    private String strategyId;
    private TaskId taskId;
    private ExecutionType executionType;
    private ScheduleConfig scheduleConfig;
    private RetryPolicy retryPolicy;
    private NotificationConfig notificationConfig;
    private LocalDateTime createTime;
    private LocalDateTime lastModifiedTime;
    
    // 执行类型枚举
    public enum ExecutionType {
        IMMEDIATE,   // 立即执行
        SCHEDULED,   // 定时执行
        PERIODIC     // 周期执行
    }
    
    /**
     * 创建立即执行策略
     */
    public static ExecutionStrategy createImmediate(TaskId taskId) {
        ExecutionStrategy strategy = new ExecutionStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.executionType = ExecutionType.IMMEDIATE;
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    /**
     * 创建定时执行策略
     */
    public static ExecutionStrategy createScheduled(TaskId taskId, ScheduleConfig scheduleConfig) {
        ExecutionStrategy strategy = new ExecutionStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.executionType = ExecutionType.SCHEDULED;
        strategy.scheduleConfig = scheduleConfig;
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    /**
     * 创建周期执行策略
     */
    public static ExecutionStrategy createPeriodic(TaskId taskId, ScheduleConfig scheduleConfig) {
        ExecutionStrategy strategy = new ExecutionStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.executionType = ExecutionType.PERIODIC;
        strategy.scheduleConfig = scheduleConfig;
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    // 业务方法
    
    /**
     * 检查是否应该立即执行
     */
    public boolean shouldExecuteNow() {
        switch (executionType) {
            case IMMEDIATE:
                return true;
            case SCHEDULED:
                return scheduleConfig != null && 
                       LocalDateTime.now().isAfter(scheduleConfig.getExecuteTime());
            case PERIODIC:
                return scheduleConfig != null && 
                       shouldExecuteBasedOnCron();
            default:
                return false;
        }
    }
    
    /**
     * 获取下次执行时间
     */
    public LocalDateTime getNextExecutionTime() {
        if (executionType == ExecutionType.IMMEDIATE) {
            return LocalDateTime.now();
        }
        
        if (scheduleConfig == null) {
            return null;
        }
        
        if (executionType == ExecutionType.SCHEDULED) {
            return scheduleConfig.getExecuteTime();
        }
        
        if (executionType == ExecutionType.PERIODIC) {
            return calculateNextExecutionTime();
        }
        
        return null;
    }
    
    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryPolicy != null && 
               retryPolicy.getCurrentRetryCount() < retryPolicy.getMaxRetryCount();
    }
    
    /**
     * 更新重试次数
     */
    public void updateRetryCount() {
        if (retryPolicy != null) {
            retryPolicy.incrementRetryCount();
            this.lastModifiedTime = LocalDateTime.now();
        }
    }
    
    /**
     * 验证调度配置
     */
    public boolean validateSchedule() {
        if (executionType == ExecutionType.IMMEDIATE) {
            return true;
        }
        
        if (scheduleConfig == null) {
            return false;
        }
        
        return scheduleConfig.isValid();
    }
    
    /**
     * 重置重试计数
     */
    public void resetRetryCount() {
        if (retryPolicy != null) {
            retryPolicy.reset();
            this.lastModifiedTime = LocalDateTime.now();
        }
    }
    
    /**
     * 更新调度配置
     */
    public void updateScheduleConfig(ScheduleConfig newConfig) {
        this.scheduleConfig = newConfig;
        this.lastModifiedTime = LocalDateTime.now();
        log.info("执行策略调度配置已更新: strategyId={}", strategyId);
    }
    
    // 私有方法
    
    private boolean shouldExecuteBasedOnCron() {
        if (scheduleConfig.getCronExpression() == null) {
            return false;
        }
        
        try {
            // 这里应该使用Cron表达式解析库
            // 简化的实现：检查是否到了执行时间
            return LocalDateTime.now().isAfter(scheduleConfig.getExecuteTime());
        } catch (Exception e) {
            log.error("解析Cron表达式失败: {}", e.getMessage());
            return false;
        }
    }
    
    private LocalDateTime calculateNextExecutionTime() {
        // 简化的实现：基于上一次执行时间计算下次执行时间
        if (scheduleConfig.getExecuteTime() == null) {
            return LocalDateTime.now();
        }
        
        // 这里应该使用Cron表达式解析库
        // 简化实现：增加一天
        return scheduleConfig.getExecuteTime().plusDays(1);
    }
}

/**
 * 调度配置值对象
 */
@Data
class ScheduleConfig {
    private LocalDateTime executeTime;
    private String cronExpression;
    private String timezone;
    
    public boolean isValid() {
        return executeTime != null || 
               (cronExpression != null && !cronExpression.trim().isEmpty());
    }
}

/**
 * 重试策略值对象
 */
@Data
class RetryPolicy {
    private int maxRetryCount;
    private int currentRetryCount;
    private long retryInterval; // 重试间隔（毫秒）
    
    public void incrementRetryCount() {
        this.currentRetryCount++;
    }
    
    public void reset() {
        this.currentRetryCount = 0;
    }
    
    public boolean shouldRetry() {
        return currentRetryCount < maxRetryCount;
    }
}

/**
 * 通知配置值对象
 */
@Data
class NotificationConfig {
    private boolean emailNotification;
    private String[] emailAddresses;
    private boolean smsNotification;
    private String[] phoneNumbers;
    private boolean webhookNotification;
    private String webhookUrl;
}
```

### 2. TaskLog.java
```java
package com.zte.domain.datamanage.entity;

import com.zte.domain.common.valueobject.LogLevel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 任务日志实体
 */
@Data
@Slf4j
public class TaskLog {
    private String logId;
    private String taskId;
    private String message;
    private LogLevel level;
    private LocalDateTime timestamp;
    private String stackTrace;
    private String category;
    
    /**
     * 创建信息日志
     */
    public static TaskLog createInfoLog(String taskId, String message) {
        return createLog(taskId, message, LogLevel.INFO, null, "GENERAL");
    }
    
    /**
     * 创建警告日志
     */
    public static TaskLog createWarningLog(String taskId, String message) {
        return createLog(taskId, message, LogLevel.WARNING, null, "GENERAL");
    }
    
    /**
     * 创建错误日志
     */
    public static TaskLog createErrorLog(String taskId, String message, String stackTrace) {
        return createLog(taskId, message, LogLevel.ERROR, stackTrace, "GENERAL");
    }
    
    /**
     * 创建调试日志
     */
    public static TaskLog createDebugLog(String taskId, String message) {
        return createLog(taskId, message, LogLevel.DEBUG, null, "GENERAL");
    }
    
    /**
     * 创建分类日志
     */
    public static TaskLog createCategoryLog(String taskId, String message, LogLevel level, String category) {
        return createLog(taskId, message, level, null, category);
    }
    
    /**
     * 创建日志的通用方法
     */
    private static TaskLog createLog(String taskId, String message, LogLevel level, String stackTrace, String category) {
        TaskLog log = new TaskLog();
        log.logId = UUID.randomUUID().toString();
        log.taskId = taskId;
        log.message = message != null ? message : "";
        log.level = level != null ? level : LogLevel.INFO;
        log.timestamp = LocalDateTime.now();
        log.stackTrace = stackTrace;
        log.category = category != null ? category : "GENERAL";
        
        return log;
    }
    
    // 业务方法
    
    /**
     * 检查是否为错误日志
     */
    public boolean isError() {
        return level == LogLevel.ERROR;
    }
    
    /**
     * 检查是否为警告日志
     */
    public boolean isWarning() {
        return level == LogLevel.WARNING;
    }
    
    /**
     * 检查是否为信息日志
     */
    public boolean isInfo() {
        return level == LogLevel.INFO;
    }
    
    /**
     * 检查是否为调试日志
     */
    public boolean isDebug() {
        return level == LogLevel.DEBUG;
    }
    
    /**
     * 获取格式化的日志消息
     */
    public String getFormattedMessage() {
        return String.format("[%s] [%s] [%s] %s", 
            timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            level,
            category,
            message);
    }
    
    /**
     * 获取简化的日志消息
     */
    public String getSimpleMessage() {
        return String.format("[%s] %s", 
            timestamp.format(DateTimeFormatter.ofPattern("HH:mm:ss")),
            message);
    }
    
    /**
     * 获取完整的日志信息（包括堆栈跟踪）
     */
    public String getFullLogInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(getFormattedMessage());
        
        if (stackTrace != null && !stackTrace.trim().isEmpty()) {
            sb.append("\n堆栈跟踪:\n").append(stackTrace);
        }
        
        return sb.toString();
    }
    
    /**
     * 检查日志是否包含指定关键词
     */
    public boolean containsKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return false;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return message.toLowerCase().contains(lowerKeyword) ||
               (stackTrace != null && stackTrace.toLowerCase().contains(lowerKeyword));
    }
    
    /**
     * 检查日志是否在指定时间范围内
     */
    public boolean isInTimeRange(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return false;
        }
        return !timestamp.isBefore(start) && !timestamp.isAfter(end);
    }
    
    /**
     * 获取日志的时间戳字符串
     */
    public String getTimestampString() {
        return timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    /**
     * 复制日志（用于历史记录）
     */
    public TaskLog copy() {
        TaskLog copy = new TaskLog();
        copy.logId = UUID.randomUUID().toString(); // 新的ID
        copy.taskId = this.taskId;
        copy.message = this.message;
        copy.level = this.level;
        copy.timestamp = this.timestamp; // 保持原时间戳
        copy.stackTrace = this.stackTrace;
        copy.category = this.category;
        return copy;
    }
    
    /**
     * 创建日志摘要
     */
    public String createSummary(int maxLength) {
        if (message.length() <= maxLength) {
            return message;
        }
        
        return message.substring(0, maxLength - 3) + "...";
    }
}
```

### 3. CleanupStrategy.java
```java
package com.zte.domain.datamanage.entity;

import com.zte.domain.datamanage.valueobject.TaskId;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 清除策略实体
 */
@Data
@Slf4j
public class CleanupStrategy {
    private String strategyId;
    private TaskId taskId;
    private CleanupType cleanupType;
    private CleanupScope scope;
    private RetentionPolicy retentionPolicy;
    private SafetyCheck safetyCheck;
    private LocalDateTime createTime;
    private LocalDateTime lastModifiedTime;
    
    // 清除类型枚举
    public enum CleanupType {
        BY_TIME,     // 按时间清除
        BY_COUNT,    // 按数量清除
        BY_CONDITION // 按条件清除
    }
    
    /**
     * 创建按时间清除的策略
     */
    public static CleanupStrategy createByTime(TaskId taskId, CleanupScope scope, 
                                             RetentionPolicy retentionPolicy) {
        CleanupStrategy strategy = new CleanupStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.cleanupType = CleanupType.BY_TIME;
        strategy.scope = scope;
        strategy.retentionPolicy = retentionPolicy;
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    /**
     * 创建按数量清除的策略
     */
    public static CleanupStrategy createByCount(TaskId taskId, CleanupScope scope, 
                                              int maxRecordsToKeep) {
        CleanupStrategy strategy = new CleanupStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.cleanupType = CleanupType.BY_COUNT;
        strategy.scope = scope;
        strategy.retentionPolicy = new RetentionPolicy(maxRecordsToKeep, 0);
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    /**
     * 创建按条件清除的策略
     */
    public static CleanupStrategy createByCondition(TaskId taskId, CleanupScope scope, 
                                                   String condition) {
        CleanupStrategy strategy = new CleanupStrategy();
        strategy.strategyId = UUID.randomUUID().toString();
        strategy.taskId = taskId;
        strategy.cleanupType = CleanupType.BY_CONDITION;
        strategy.scope = scope;
        strategy.retentionPolicy = new RetentionPolicy(0, 0, condition);
        strategy.createTime = LocalDateTime.now();
        strategy.lastModifiedTime = LocalDateTime.now();
        return strategy;
    }
    
    // 业务方法
    
    /**
     * 检查是否应该清除指定数据
     */
    public boolean shouldCleanup(DataInfo data) {
        if (data == null) {
            return false;
        }
        
        switch (cleanupType) {
            case BY_TIME:
                return shouldCleanupByTime(data);
            case BY_COUNT:
                return shouldCleanupByCount(data);
            case BY_CONDITION:
                return shouldCleanupByCondition(data);
            default:
                return false;
        }
    }
    
    /**
     * 生成清除计划
     */
    public CleanupPlan generateCleanupPlan() {
        CleanupPlan plan = new CleanupPlan();
        plan.setStrategyId(strategyId);
        plan.setCleanupType(cleanupType);
        plan.setScope(scope);
        plan.setCreateTime(LocalDateTime.now());
        
        switch (cleanupType) {
            case BY_TIME:
                plan.setTimeBasedCleanup(true);
                plan.setRetentionPeriod(retentionPolicy.getRetentionDays());
                break;
            case BY_COUNT:
                plan.setCountBasedCleanup(true);
                plan.setMaxRecordsToKeep(retentionPolicy.getMaxRecordsToKeep());
                break;
            case BY_CONDITION:
                plan.setConditionBasedCleanup(true);
                plan.setCleanupCondition(retentionPolicy.getCustomCondition());
                break;
        }
        
        return plan;
    }
    
    /**
     * 检查清除是否安全
     */
    public boolean isSafeToCleanup() {
        if (safetyCheck == null) {
            return true; // 如果没有安全检查，默认为安全
        }
        
        return safetyCheck.performCheck();
    }
    
    /**
     * 获取清除类型描述
     */
    public String getCleanupTypeDescription() {
        switch (cleanupType) {
            case BY_TIME:
                return "按时间清除";
            case BY_COUNT:
                return "按数量清除";
            case BY_CONDITION:
                return "按条件清除";
            default:
                return "未知清除类型";
        }
    }
    
    /**
     * 更新安全检查配置
     */
    public void updateSafetyCheck(SafetyCheck newSafetyCheck) {
        this.safetyCheck = newSafetyCheck;
        this.lastModifiedTime = LocalDateTime.now();
        log.info("清除策略安全检查配置已更新: strategyId={}", strategyId);
    }
    
    // 私有方法
    
    private boolean shouldCleanupByTime(DataInfo data) {
        if (retentionPolicy == null || retentionPolicy.getRetentionDays() <= 0) {
            return false;
        }
        
        LocalDateTime cutoffTime = LocalDateTime.now()
            .minusDays(retentionPolicy.getRetentionDays());
        return data.getCreateTime().isBefore(cutoffTime);
    }
    
    private boolean shouldCleanupByCount(DataInfo data) {
        // 这个方法需要额外的上下文信息，比如当前记录总数
        // 简化实现，总是返回true，实际应该根据当前记录数判断
        return true;
    }
    
    private boolean shouldCleanupByCondition(DataInfo data) {
        if (retentionPolicy == null || retentionPolicy.getCustomCondition() == null) {
            return false;
        }
        
        // 这里应该实现条件解析和匹配逻辑
        // 简化实现，总是返回false
        return false;
    }
}

/**
 * 清除范围值对象
 */
@Data
class CleanupScope {
    private String targetTable;
    private String[] targetColumns;
    private String whereClause;
    private boolean includeSystemData;
    
    public boolean isValid() {
        return targetTable != null && !targetTable.trim().isEmpty();
    }
}

/**
 * 保留策略值对象
 */
@Data
class RetentionPolicy {
    private int maxRecordsToKeep;  // 最大保留记录数
    private int retentionDays;    // 保留天数
    private String customCondition; // 自定义条件
    
    public RetentionPolicy(int maxRecordsToKeep, int retentionDays) {
        this.maxRecordsToKeep = maxRecordsToKeep;
        this.retentionDays = retentionDays;
    }
    
    public RetentionPolicy(int maxRecordsToKeep, int retentionDays, String customCondition) {
        this.maxRecordsToKeep = maxRecordsToKeep;
        this.retentionDays = retentionDays;
        this.customCondition = customCondition;
    }
}

/**
 * 安全检查值对象
 */
@Data
class SafetyCheck {
    private boolean enabled;
    private String[] protectedTables;
    private int maxRecordsToDelete;
    private boolean requireBackup;
    
    public boolean performCheck() {
        if (!enabled) {
            return true;
        }
        
        // 实现安全检查逻辑
        // 检查是否在保护表中、是否超过最大删除记录数等
        return true;
    }
}

/**
 * 清除计划值对象
 */
@Data
class CleanupPlan {
    private String strategyId;
    private CleanupStrategy.CleanupType cleanupType;
    private CleanupScope scope;
    private LocalDateTime createTime;
    private boolean timeBasedCleanup;
    private int retentionPeriod;
    private boolean countBasedCleanup;
    private int maxRecordsToKeep;
    private boolean conditionBasedCleanup;
    private String cleanupCondition;
    
    public boolean isValid() {
        return strategyId != null && !strategyId.trim().isEmpty() &&
               cleanupType != null && scope != null && scope.isValid();
    }
}

/**
 * 数据信息值对象
 */
@Data
class DataInfo {
    private String dataId;
    private LocalDateTime createTime;
    private String tableName;
    private long recordCount;
    private String metadata;
}
```

## 🔄 领域服务（Domain Services）实现

### 1. DataImportService.java
```java
package com.zte.domain.datamanage.service;

import com.zte.domain.datamanage.aggregate.DataImportTask;
import com.zte.domain.datamanage.valueobject.*;
import com.zte.domain.datamanage.repository.TaskRepository;
import com.zte.domain.common.exception.DomainException;
import com.zte.infrastructure.adapter.DataSourceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据导入领域服务
 * 
 * 职责：
 * 1. 处理跨聚合的业务逻辑
 * 2. 协调多个聚合的交互
 * 3. 封装复杂的数据导入流程
 */
@Service
@Slf4j
public class DataImportService {
    
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataSourceAdapter dataSourceAdapter;
    
    /**
     * 执行数据导入
     */
    @Transactional
    public ImportResult importData(DataImportTask task) {
        log.info("开始执行数据导入任务: taskId={}, taskName={}", 
                task.getTaskId().getValue(), task.getTaskName().getValue());
        
        try {
            // 1. 验证任务状态
            if (!task.canExecute()) {
                String errorMsg = "任务状态不允许执行: " + task.getStatus();
                log.error(errorMsg);
                return ImportResult.failed(errorMsg);
            }
            
            // 2. 验证数据源
            DataSourceValidationResult validation = validateDataSource(task.getDataSource());
            if (!validation.isValid()) {
                String errorMsg = "数据源验证失败: " + validation.getErrorMessage();
                log.error(errorMsg);
                return ImportResult.failed(errorMsg);
            }
            
            // 3. 验证数据范围
            if (!task.getDataRange().isValidRange()) {
                String errorMsg = "数据范围配置无效";
                log.error(errorMsg);
                return ImportResult.failed(errorMsg);
            }
            
            // 4. 开始执行任务
            task.startExecution();
            taskRepository.save(task);
            
            // 5. 执行数据导入
            ImportResult result = executeDataImport(task);
            
            // 6. 更新任务状态
            if (result.isSuccess()) {
                task.completeExecution();
                log.info("数据导入任务执行成功: taskId={}, 处理记录数={}", 
                        task.getTaskId().getValue(), result.getProcessedRecords());
            } else {
                task.failExecution(result.getErrorMessage());
                log.error("数据导入任务执行失败: taskId={}, 错误信息={}", 
                        task.getTaskId().getValue(), result.getErrorMessage());
            }
            
            taskRepository.save(task);
            return result;
            
        } catch (Exception e) {
            String errorMsg = "导入过程中发生异常: " + e.getMessage();
            log.error("数据导入任务执行异常: taskId={}", task.getTaskId().getValue(), e);
            
            try {
                task.failExecution(errorMsg);
                taskRepository.save(task);
            } catch (Exception saveException) {
                log.error("保存任务状态失败", saveException);
            }
            
            return ImportResult.failed(errorMsg);
        }
    }
    
    /**
     * 验证数据源配置
     */
    public DataSourceValidationResult validateDataSource(DataSource dataSource) {
        if (dataSource == null) {
            return DataSourceValidationResult.failed("数据源配置不能为空");
        }
        
        try {
            // 基本配置验证
            if (!dataSource.isValid()) {
                return DataSourceValidationResult.failed("数据源基本配置无效");
            }
            
            // 连接测试
            ConnectionTestResult connectionTest = dataSourceAdapter.testConnection(dataSource);
            if (!connectionTest.isSuccess()) {
                return DataSourceValidationResult.failed("连接测试失败: " + connectionTest.getErrorMessage());
            }
            
            // 权限验证
            PermissionTestResult permissionTest = dataSourceAdapter.testPermissions(dataSource);
            if (!permissionTest.hasReadPermission()) {
                return DataSourceValidationResult.failed("缺少读取权限");
            }
            
            // 路径验证
            if (dataSource.getPath() != null && !dataSource.getPath().trim().isEmpty()) {
                PathTestResult pathTest = dataSourceAdapter.testPath(dataSource);
                if (!pathTest.isPathAccessible()) {
                    return DataSourceValidationResult.failed("路径不可访问: " + pathTest.getErrorMessage());
                }
            }
            
            return DataSourceValidationResult.success();
            
        } catch (Exception e) {
            log.error("验证数据源时发生异常", e);
            return DataSourceValidationResult.failed("验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 预览数据
     */
    public DataPreview previewData(DataImportTask task, int maxRecords) {
        if (task == null) {
            throw new DomainException("任务不能为空");
        }
        
        if (maxRecords <= 0 || maxRecords > 1000) {
            throw new DomainException("预览记录数必须在1-1000之间");
        }
        
        try {
            // 验证数据源
            DataSourceValidationResult validation = validateDataSource(task.getDataSource());
            if (!validation.isValid()) {
                throw new DomainException("数据源验证失败: " + validation.getErrorMessage());
            }
            
            // 获取数据预览
            return dataSourceAdapter.previewData(task.getDataSource(), task.getDataRange(), maxRecords);
            
        } catch (Exception e) {
            log.error("获取数据预览失败", e);
            throw new DomainException("获取数据预览失败: " + e.getMessage());
        }
    }
    
    /**
     * 监控导入进度
     */
    public ImportProgressMonitor monitorImport(DataImportTask task) {
        if (task == null) {
            throw new DomainException("任务不能为空");
        }
        
        ImportProgressMonitor monitor = new ImportProgressMonitor();
        monitor.setTaskId(task.getTaskId().getValue());
        monitor.setTaskName(task.getTaskName().getValue());
        monitor.setStatus(task.getStatus().name());
        monitor.setStartTime(task.getStartTime());
        monitor.setProgress(task.getProgress());
        
        // 如果任务正在运行，获取实时进度
        if (task.getStatus() == DataImportTask.TaskStatus.RUNNING) {
            try {
                RealTimeProgress realTimeProgress = dataSourceAdapter.getRealTimeProgress(task.getTaskId());
                if (realTimeProgress != null) {
                    monitor.setRealTimeProgress(realTimeProgress);
                }
            } catch (Exception e) {
                log.warn("获取实时进度失败", e);
            }
        }
        
        return monitor;
    }
    
    /**
     * 处理导入错误
     */
    public void handleImportError(DataImportTask task, Exception error) {
        if (task == null || error == null) {
            return;
        }
        
        // 记录错误日志
        String errorMessage = "导入错误: " + error.getMessage();
        task.addLog(errorMessage, com.zte.domain.common.valueobject.LogLevel.ERROR);
        
        // 检查是否可以重试
        if (task.getStrategy() != null && task.getStrategy().canRetry()) {
            task.getStrategy().updateRetryCount();
            task.addLog("准备重试，当前重试次数: " + task.getStrategy().getRetryPolicy().getCurrentRetryCount(), 
                        com.zte.domain.common.valueobject.LogLevel.WARNING);
        } else {
            // 无法重试，标记为失败
            task.failExecution(errorMessage);
        }
        
        // 保存任务状态
        try {
            taskRepository.save(task);
        } catch (Exception e) {
            log.error("保存任务状态失败", e);
        }
    }
    
    /**
     * 取消导入任务
     */
    public void cancelImport(DataImportTask task) {
        if (task == null) {
            throw new DomainException("任务不能为空");
        }
        
        if (task.getStatus() != DataImportTask.TaskStatus.RUNNING) {
            throw new DomainException("只有运行中的任务才能取消");
        }
        
        try {
            // 通知数据源适配器停止导入
            dataSourceAdapter.cancelImport(task.getTaskId());
            
            // 更新任务状态
            task.failExecution("用户取消导入");
            taskRepository.save(task);
            
            log.info("数据导入任务已取消: taskId={}", task.getTaskId().getValue());
            
        } catch (Exception e) {
            log.error("取消导入任务失败", e);
            throw new DomainException("取消导入任务失败: " + e.getMessage());
        }
    }
    
    // 私有方法
    
    private ImportResult executeDataImport(DataImportTask task) {
        try {
            // 根据数据源类型选择不同的导入策略
            switch (task.getDataSource().getType()) {
                case NDS:
                case PM:
                case CM:
                case FM:
                    return executeDatabaseImport(task);
                case VMAX:
                    return executeVmaxImport(task);
                default:
                    return ImportResult.failed("不支持的数据源类型: " + task.getDataSource().getType());
            }
        } catch (Exception e) {
            log.error("执行数据导入失败", e);
            return ImportResult.failed("执行数据导入失败: " + e.getMessage());
        }
    }
    
    private ImportResult executeDatabaseImport(DataImportTask task) {
        // 实现数据库导入逻辑
        // 这里可以调用原有的ImportJob逻辑，但需要进行封装
        
        // 模拟导入过程
        try {
            // 1. 创建目标表
            String targetTable = createTargetTable(task);
            
            // 2. 执行数据导入
            ImportExecutionResult executionResult = dataSourceAdapter.executeImport(
                task.getDataSource(), task.getDataRange(), targetTable);
            
            // 3. 更新进度
            task.updateProgress(executionResult.getProcessedRecords(), executionResult.getTotalRecords());
            
            return ImportResult.success(executionResult.getProcessedRecords(), 
                                       executionResult.getTotalRecords());
            
        } catch (Exception e) {
            log.error("数据库导入失败", e);
            return ImportResult.failed("数据库导入失败: " + e.getMessage());
        }
    }
    
    private ImportResult executeVmaxImport(DataImportTask task) {
        // 实现VMAX导入逻辑
        // 这里可以调用特定的VMAX导入逻辑
        
        try {
            VmaxImportResult vmaxResult = dataSourceAdapter.executeVmaxImport(
                task.getDataSource(), task.getDataRange());
            
            task.updateProgress(vmaxResult.getProcessedRecords(), vmaxResult.getTotalRecords());
            
            return ImportResult.success(vmaxResult.getProcessedRecords(), 
                                       vmaxResult.getTotalRecords());
            
        } catch (Exception e) {
            log.error("VMAX导入失败", e);
            return ImportResult.failed("VMAX导入失败: " + e.getMessage());
        }
    }
    
    private String createTargetTable(DataImportTask task) {
        // 生成目标表名
        return "tbl_" + task.getTaskId().getValue().substring(0, 8) + "_" + 
               System.currentTimeMillis() % 10000;
    }
}

/**
 * 数据导入结果
 */
@Data
class ImportResult {
    private boolean success;
    private String errorMessage;
    private int processedRecords;
    private int totalRecords;
    
    public static ImportResult success(int processedRecords, int totalRecords) {
        ImportResult result = new ImportResult();
        result.success = true;
        result.processedRecords = processedRecords;
        result.totalRecords = totalRecords;
        return result;
    }
    
    public static ImportResult success() {
        return success(0, 0);
    }
    
    public static ImportResult failed(String errorMessage) {
        ImportResult result = new ImportResult();
        result.success = false;
        result.errorMessage = errorMessage;
        return result;
    }
}

/**
 * 数据源验证结果
 */
@Data
class DataSourceValidationResult {
    private boolean valid;
    private String errorMessage;
    
    public static DataSourceValidationResult success() {
        DataSourceValidationResult result = new DataSourceValidationResult();
        result.valid = true;
        return result;
    }
    
    public static DataSourceValidationResult failed(String errorMessage) {
        DataSourceValidationResult result = new DataSourceValidationResult();
        result.valid = false;
        result.errorMessage = errorMessage;
        return result;
    }
}

/**
 * 数据预览结果
 */
@Data
class DataPreview {
    private List<String> columnNames;
    private List<List<Object>> sampleData;
    private int totalRecords;
    private DataRange actualRange;
}

/**
 * 导入进度监控器
 */
@Data
class ImportProgressMonitor {
    private String taskId;
    private String taskName;
    private String status;
    private LocalDateTime startTime;
    private ImportProgress progress;
    private RealTimeProgress realTimeProgress;
}

/**
 * 实时进度信息
 */
@Data
class RealTimeProgress {
    private int currentProcessedRecords;
    private double processingSpeed; // 记录/秒
    private LocalDateTime lastUpdateTime;
    private LocalDateTime estimatedCompletionTime;
}

// 其他结果类定义...
```

### 2. DataCleanupService.java
```java
package com.zte.domain.datamanage.service;

import com.zte.domain.datamanage.aggregate.DataCleanupTask;
import com.zte.domain.datamanage.entity.CleanupStrategy;
import com.zte.domain.datamanage.valueobject.TaskId;
import com.zte.domain.datamanage.repository.TaskRepository;
import com.zte.domain.common.exception.DomainException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据清除领域服务
 * 
 * 职责：
 * 1. 处理数据清除的业务逻辑
 * 2. 执行安全检查
 * 3. 协调清除策略的执行
 */
@Service
@Slf4j
public class DataCleanupService {
    
    @Inject
    private TaskRepository taskRepository;
    
    /**
     * 执行数据清除
     */
    @Transactional
    public CleanupResult cleanupData(DataCleanupTask task) {
        log.info("开始执行数据清除任务: taskId={}, taskName={}", 
                task.getTaskId().getValue(), task.getTaskName().getValue());
        
        try {
            // 1. 安全性检查
            SafetyCheckResult safetyCheck = performSafetyCheck(task);
            if (!safetyCheck.isSafe()) {
                String errorMsg = "安全检查失败: " + safetyCheck.getReason();
                log.error(errorMsg);
                return CleanupResult.failed(errorMsg);
            }
            
            // 2. 验证清除范围
            ScopeValidationResult scopeValidation = validateCleanupScope(task);
            if (!scopeValidation.isValid()) {
                String errorMsg = "清除范围验证失败: " + scopeValidation.getErrorMessage();
                log.error(errorMsg);
                return CleanupResult.failed(errorMsg);
            }
            
            // 3. 开始清除任务
            task.startCleanup();
            taskRepository.save(task);
            
            // 4. 生成清除计划
            CleanupPlan plan = generateCleanupPlan(task);
            
            // 5. 执行数据清除
            CleanupResult result = executeCleanup(task, plan);
            
            // 6. 更新任务状态
            if (result.isSuccess()) {
                task.completeCleanup();
                log.info("数据清除任务执行成功: taskId={}, 清除记录数={}", 
                        task.getTaskId().getValue(), result.getClearedRecords());
            } else {
                task.failCleanup(result.getErrorMessage());
                log.error("数据清除任务执行失败: taskId={}, 错误信息={}", 
                        task.getTaskId().getValue(), result.getErrorMessage());
            }
            
            taskRepository.save(task);
            return result;
            
        } catch (Exception e) {
            String errorMsg = "清除过程中发生异常: " + e.getMessage();
            log.error("数据清除任务执行异常: taskId={}", task.getTaskId().getValue(), e);
            
            try {
                task.failCleanup(errorMsg);
                taskRepository.save(task);
            } catch (Exception saveException) {
                log.error("保存任务状态失败", saveException);
            }
            
            return CleanupResult.failed(errorMsg);
        }
    }
    
    /**
     * 执行安全检查
     */
    public SafetyCheckResult performSafetyCheck(DataCleanupTask task) {
        if (task == null) {
            return SafetyCheckResult.failed("任务不能为空");
        }
        
        try {
            // 1. 检查任务状态
            if (!task.canCleanup()) {
                return SafetyCheckResult.failed("任务当前状态不允许执行清除");
            }
            
            // 2. 检查清除策略
            CleanupStrategy strategy = task.getCleanupStrategy();
            if (strategy == null) {
                return SafetyCheckResult.failed("清除策略不能为空");
            }
            
            // 3. 执行策略安全检查
            if (!strategy.isSafeToCleanup()) {
                return SafetyCheckResult.failed("清除策略安全检查未通过");
            }
            
            // 4. 检查系统资源
            SystemResourceCheck resourceCheck = checkSystemResources();
            if (!resourceCheck.isSufficient()) {
                return SafetyCheckResult.failed("系统资源不足: " + resourceCheck.getReason());
            }
            
            // 5. 检查备份状态（如果需要）
            if (strategy.getSafetyCheck() != null && strategy.getSafetyCheck().isRequireBackup()) {
                BackupCheckResult backupCheck = checkBackupStatus();
                if (!backupCheck.isBackupAvailable()) {
                    return SafetyCheckResult.failed("备份检查失败: " + backupCheck.getReason());
                }
            }
            
            return SafetyCheckResult.success();
            
        } catch (Exception e) {
            log.error("执行安全检查时发生异常", e);
            return SafetyCheckResult.failed("安全检查过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 验证清除范围
     */
    public ScopeValidationResult validateCleanupScope(DataCleanupTask task) {
        if (task == null || task.getCleanupStrategy() == null) {
            return ScopeValidationResult.failed("任务或清除策略不能为空");
        }
        
        try {
            CleanupStrategy strategy = task.getCleanupStrategy();
            
            // 验证清除范围配置
            if (strategy.getScope() == null || !strategy.getScope().isValid()) {
                return ScopeValidationResult.failed("清除范围配置无效");
            }
            
            // 验证目标表是否存在
            TableCheckResult tableCheck = checkTableExists(strategy.getScope().getTargetTable());
            if (!tableCheck.exists()) {
                return ScopeValidationResult.failed("目标表不存在: " + strategy.getScope().getTargetTable());
            }
            
            // 验证清除权限
            PermissionCheckResult permissionCheck = checkCleanupPermissions(strategy.getScope().getTargetTable());
            if (!permissionCheck.hasDeletePermission()) {
                return ScopeValidationResult.failed("缺少清除权限");
            }
            
            // 验证数据范围（如果指定）
            if (strategy.getScope().getWhereClause() != null && 
                !strategy.getScope().getWhereClause().trim().isEmpty()) {
                RangeCheckResult rangeCheck = checkDataRange(strategy.getScope().getTargetTable(), 
                                                           strategy.getScope().getWhereClause());
                if (!rangeCheck.isValid()) {
                    return ScopeValidationResult.failed("数据范围验证失败: " + rangeCheck.getReason());
                }
            }
            
            return ScopeValidationResult.success();
            
        } catch (Exception e) {
            log.error("验证清除范围时发生异常", e);
            return ScopeValidationResult.failed("验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 生成清除计划
     */
    public CleanupPlan generateCleanupPlan(DataCleanupTask task) {
        if (task == null || task.getCleanupStrategy() == null) {
            throw new DomainException("任务或清除策略不能为空");
        }
        
        CleanupStrategy strategy = task.getCleanupStrategy();
        CleanupPlan plan = strategy.generateCleanupPlan();
        
        // 添加额外的计划信息
        plan.setEstimatedExecutionTime(estimateExecutionTime(task));
        plan.setResourceRequirements(calculateResourceRequirements(task));
        
        return plan;
    }
    
    /**
     * 验证清除结果
     */
    public CleanupVerificationResult verifyCleanupResult(DataCleanupTask task, CleanupResult result) {
        if (task == null || result == null) {
            return CleanupVerificationResult.failed("参数不能为空");
        }
        
        try {
            if (!result.isSuccess()) {
                return CleanupVerificationResult.failed("清除执行失败");
            }
            
            // 验证实际清除的记录数
            ActualCleanupCount actualCount = getActualCleanupCount(task);
            if (actualCount.getCount() != result.getClearedRecords()) {
                return CleanupVerificationResult.failed("清除记录数不匹配");
            }
            
            // 验证数据完整性
            IntegrityCheckResult integrityCheck = checkDataIntegrity(task);
            if (!integrityCheck.isIntact()) {
                return CleanupVerificationResult.failed("数据完整性检查失败");
            }
            
            return CleanupVerificationResult.success();
            
        } catch (Exception e) {
            log.error("验证清除结果时发生异常", e);
            return CleanupVerificationResult.failed("验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 取清除统计信息
     */
    public CleanupStatistics getCleanupStatistics(TaskId taskId) {
        // 实现获取清除统计信息的逻辑
        CleanupStatistics stats = new CleanupStatistics();
        stats.setTaskId(taskId.getValue());
        stats.setTotalCleanupTasks(getTotalCleanupTasks());
        stats.setSuccessfulCleanupTasks(getSuccessfulCleanupTasks());
        stats.setFailedCleanupTasks(getFailedCleanupTasks());
        stats.setTotalClearedRecords(getTotalClearedRecords());
        stats.setAverageExecutionTime(getAverageExecutionTime());
        
        return stats;
    }
    
    // 私有方法
    
    private CleanupResult executeCleanup(DataCleanupTask task, CleanupPlan plan) {
        try {
            // 根据清除类型选择不同的清除策略
            switch (plan.getCleanupType()) {
                case BY_TIME:
                    return executeTimeBasedCleanup(task, plan);
                case BY_COUNT:
                    return executeCountBasedCleanup(task, plan);
                case BY_CONDITION:
                    return executeConditionBasedCleanup(task, plan);
                default:
                    return CleanupResult.failed("不支持的清除类型: " + plan.getCleanupType());
            }
        } catch (Exception e) {
            log.error("执行数据清除失败", e);
            return CleanupResult.failed("执行数据清除失败: " + e.getMessage());
        }
    }
    
    private CleanupResult executeTimeBasedCleanup(DataCleanupTask task, CleanupPlan plan) {
        // 实现按时间清除的逻辑
        try {
            TimeBasedCleanupResult result = performTimeBasedCleanup(
                plan.getScope().getTargetTable(),
                plan.getRetentionPeriod()
            );
            
            return CleanupResult.success(result.getClearedRecords(), result.getExecutionTime());
            
        } catch (Exception e) {
            log.error("按时间清除失败", e);
            return CleanupResult.failed("按时间清除失败: " + e.getMessage());
        }
    }
    
    private CleanupResult executeCountBasedCleanup(DataCleanupTask task, CleanupPlan plan) {
        // 实现按数量清除的逻辑
        try {
            CountBasedCleanupResult result = performCountBasedCleanup(
                plan.getScope().getTargetTable(),
                plan.getMaxRecordsToKeep()
            );
            
            return CleanupResult.success(result.getClearedRecords(), result.getExecutionTime());
            
        } catch (Exception e) {
            log.error("按数量清除失败", e);
            return CleanupResult.failed("按数量清除失败: " + e.getMessage());
        }
    }
    
    private CleanupResult executeConditionBasedCleanup(DataCleanupTask task, CleanupPlan plan) {
        // 实现按条件清除的逻辑
        try {
            ConditionBasedCleanupResult result = performConditionBasedCleanup(
                plan.getScope().getTargetTable(),
                plan.getCleanupCondition()
            );
            
            return CleanupResult.success(result.getClearedRecords(), result.getExecutionTime());
            
        } catch (Exception e) {
            log.error("按条件清除失败", e);
            return CleanupResult.failed("按条件清除失败: " + e.getMessage());
        }
    }
    
    // 其他私有方法...
}

// 清除相关的结果类定义
@Data
class CleanupResult {
    private boolean success;
    private String errorMessage;
    private int clearedRecords;
    private long executionTime; // 执行时间（毫秒）
    
    public static CleanupResult success(int clearedRecords, long executionTime) {
        CleanupResult result = new CleanupResult();
        result.success = true;
        result.clearedRecords = clearedRecords;
        result.executionTime = executionTime;
        return result;
    }
    
    public static CleanupResult failed(String errorMessage) {
        CleanupResult result = new CleanupResult();
        result.success = false;
        result.errorMessage = errorMessage;
        return result;
    }
}

@Data
class SafetyCheckResult {
    private boolean safe;
    private String reason;
    
    public static SafetyCheckResult success() {
        SafetyCheckResult result = new SafetyCheckResult();
        result.safe = true;
        return result;
    }
    
    public static SafetyCheckResult failed(String reason) {
        SafetyCheckResult result = new SafetyCheckResult();
        result.safe = false;
        result.reason = reason;
        return result;
    }
}

@Data
class ScopeValidationResult {
    private boolean valid;
    private String errorMessage;
    
    public static ScopeValidationResult success() {
        ScopeValidationResult result = new ScopeValidationResult();
        result.valid = true;
        return result;
    }
    
    public static ScopeValidationResult failed(String errorMessage) {
        ScopeValidationResult result = new ScopeValidationResult();
        result.valid = false;
        result.errorMessage = errorMessage;
        return result;
    }
}

@Data
class CleanupVerificationResult {
    private boolean verified;
    private String errorMessage;
    
    public static CleanupVerificationResult success() {
        CleanupVerificationResult result = new CleanupVerificationResult();
        result.verified = true;
        return result;
    }
    
    public static CleanupVerificationResult failed(String errorMessage) {
        CleanupVerificationResult result = new CleanupVerificationResult();
        result.verified = false;
        result.errorMessage = errorMessage;
        return result;
    }
}

@Data
class CleanupStatistics {
    private String taskId;
    private int totalCleanupTasks;
    private int successfulCleanupTasks;
    private int failedCleanupTasks;
    private long totalClearedRecords;
    private double averageExecutionTime;
}
```

## 🗄️ 仓储模式（Repository Pattern）实现

### 1. TaskRepository.java
```java
package com.zte.domain.datamanage.repository;

import com.zte.domain.datamanage.aggregate.DataImportTask;
import com.zte.domain.datamanage.aggregate.DataCleanupTask;
import com.zte.domain.datamanage.valueobject.*;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 任务仓储接口
 * 
 * 职责：
 * 1. 封装数据访问逻辑
 * 2. 提供聚合根的持久化操作
 * 3. 隔离领域层与基础设施层
 */
public interface TaskRepository {
    
    // 数据导入任务操作
    DataImportTask findImportTaskById(TaskId taskId);
    void saveImportTask(DataImportTask task);
    void deleteImportTask(TaskId taskId);
    
    // 数据清除任务操作
    DataCleanupTask findCleanupTaskById(TaskId taskId);
    void saveCleanupTask(DataCleanupTask task);
    void deleteCleanupTask(TaskId taskId);
    
    // 查询方法
    List<DataImportTask> findImportTasksByStatus(DataImportTask.TaskStatus status);
    List<DataImportTask> findImportTasksByCreator(Creator creator);
    List<DataImportTask> findImportTasksByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    List<DataImportTask> findImportTasksByDataSourceType(DataSource.DataSourceType type);
    
    List<DataCleanupTask> findCleanupTasksByStatus(DataCleanupTask.TaskStatus status);
    List<DataCleanupTask> findCleanupTasksByCreator(Creator creator);
    List<DataCleanupTask> findCleanupTasksByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    // 统计方法
    long countImportTasksByStatus(DataImportTask.TaskStatus status);
    long countCleanupTasksByStatus(DataCleanupTask.TaskStatus status);
    
    // 批量操作
    void batchSaveImportTasks(List<DataImportTask> tasks);
    void batchSaveCleanupTasks(List<DataCleanupTask> tasks);
}
```

### 2. TaskRepositoryImpl.java
```java
package com.zte.infrastructure.repository;

import com.zte.domain.datamanage.aggregate.DataImportTask;
import com.zte.domain.datamanage.aggregate.DataCleanupTask;
import com.zte.domain.datamanage.repository.TaskRepository;
import com.zte.domain.datamanage.valueobject.*;
import com.zte.infrastructure.mapper.TaskMapper;
import com.zte.infrastructure.mapper.TaskLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务仓储实现
 * 
 * 职责：
 * 1. 实现仓储接口定义的方法
 * 2. 处理领域对象与数据实体的转换
 * 3. 协调多个数据访问操作
 */
@Repository
@Slf4j
public class TaskRepositoryImpl implements TaskRepository {
    
    @Inject
    private TaskMapper taskMapper;
    
    @Inject
    private TaskLogMapper taskLogMapper;
    
    @Override
    @Transactional
    public DataImportTask findImportTaskById(TaskId taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            TaskEntity entity = taskMapper.findImportTaskById(taskId.getValue());
            if (entity == null) {
                return null;
            }
            
            return entityToImportTask(entity);
            
        } catch (Exception e) {
            log.error("查询数据导入任务失败: taskId={}", taskId.getValue(), e);
            throw new RuntimeException("查询数据导入任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public void saveImportTask(DataImportTask task) {
        if (task == null) {
            throw new IllegalArgumentException("任务不能为空");
        }
        
        try {
            TaskEntity entity = importTaskToEntity(task);
            
            // 检查是否已存在
            TaskEntity existing = taskMapper.findImportTaskById(task.getTaskId().getValue());
            if (existing == null) {
                // 新增
                taskMapper.insertImportTask(entity);
                log.info("新增数据导入任务: taskId={}", task.getTaskId().getValue());
            } else {
                // 更新
                taskMapper.updateImportTask(entity);
                log.info("更新数据导入任务: taskId={}", task.getTaskId().getValue());
            }
            
            // 保存任务日志
            saveTaskLogs(task);
            
        } catch (Exception e) {
            log.error("保存数据导入任务失败: taskId={}", task.getTaskId().getValue(), e);
            throw new RuntimeException("保存数据导入任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public void deleteImportTask(TaskId taskId) {
        if (taskId == null) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        try {
            // 删除任务日志
            taskLogMapper.deleteByTaskId(taskId.getValue());
            
            // 删除任务
            taskMapper.deleteImportTask(taskId.getValue());
            
            log.info("删除数据导入任务: taskId={}", taskId.getValue());
            
        } catch (Exception e) {
            log.error("删除数据导入任务失败: taskId={}", taskId.getValue(), e);
            throw new RuntimeException("删除数据导入任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public DataCleanupTask findCleanupTaskById(TaskId taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            TaskEntity entity = taskMapper.findCleanupTaskById(taskId.getValue());
            if (entity == null) {
                return null;
            }
            
            return entityToCleanupTask(entity);
            
        } catch (Exception e) {
            log.error("查询数据清除任务失败: taskId={}", taskId.getValue(), e);
            throw new RuntimeException("查询数据清除任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public void saveCleanupTask(DataCleanupTask task) {
        if (task == null) {
            throw new IllegalArgumentException("任务不能为空");
        }
        
        try {
            TaskEntity entity = cleanupTaskToEntity(task);
            
            // 检查是否已存在
            TaskEntity existing = taskMapper.findCleanupTaskById(task.getTaskId().getValue());
            if (existing == null) {
                // 新增
                taskMapper.insertCleanupTask(entity);
                log.info("新增数据清除任务: taskId={}", task.getTaskId().getValue());
            } else {
                // 更新
                taskMapper.updateCleanupTask(entity);
                log.info("更新数据清除任务: taskId={}", task.getTaskId().getValue());
            }
            
            // 保存任务日志
            saveTaskLogs(task);
            
        } catch (Exception e) {
            log.error("保存数据清除任务失败: taskId={}", task.getTaskId().getValue(), e);
            throw new RuntimeException("保存数据清除任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public void deleteCleanupTask(TaskId taskId) {
        if (taskId == null) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        try {
            // 删除任务日志
            taskLogMapper.deleteByTaskId(taskId.getValue());
            
            // 删除任务
            taskMapper.deleteCleanupTask(taskId.getValue());
            
            log.info("删除数据清除任务: taskId={}", taskId.getValue());
            
        } catch (Exception e) {
            log.error("删除数据清除任务失败: taskId={}", taskId.getValue(), e);
            throw new RuntimeException("删除数据清除任务失败", e);
        }
    }
    
    @Override
    public List<DataImportTask> findImportTasksByStatus(DataImportTask.TaskStatus status) {
        if (status == null) {
            throw new IllegalArgumentException("任务状态不能为空");
        }
        
        try {
            List<TaskEntity> entities = taskMapper.findImportTasksByStatus(status.name());
            return entities.stream()
                    .map(this::entityToImportTask)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("按状态查询数据导入任务失败: status={}", status, e);
            throw new RuntimeException("按状态查询数据导入任务失败", e);
        }
    }
    
    @Override
    public List<DataImportTask> findImportTasksByCreator(Creator creator) {
        if (creator == null) {
            throw new IllegalArgumentException("创建者不能为空");
        }
        
        try {
            List<TaskEntity> entities = taskMapper.findImportTasksByCreator(creator.getValue());
            return entities.stream()
                    .map(this::entityToImportTask)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("按创建者查询数据导入任务失败: creator={}", creator.getValue(), e);
            throw new RuntimeException("按创建者查询数据导入任务失败", e);
        }
    }
    
    @Override
    public List<DataImportTask> findImportTasksByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("时间范围不能为空");
        }
        
        try {
            List<TaskEntity> entities = taskMapper.findImportTasksByTimeRange(startTime, endTime);
            return entities.stream()
                    .map(this::entityToImportTask)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("按时间范围查询数据导入任务失败: startTime={}, endTime={}", startTime, endTime, e);
            throw new RuntimeException("按时间范围查询数据导入任务失败", e);
        }
    }
    
    @Override
    public List<DataImportTask> findImportTasksByDataSourceType(DataSource.DataSourceType type) {
        if (type == null) {
            throw new IllegalArgumentException("数据源类型不能为空");
        }
        
        try {
            List<TaskEntity> entities = taskMapper.findImportTasksByDataSourceType(type.name());
            return entities.stream()
                    .map(this::entityToImportTask)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("按数据源类型查询数据导入任务失败: type={}", type, e);
            throw new RuntimeException("按数据源类型查询数据导入任务失败", e);
        }
    }
    
    // 其他方法实现...
    
    // 私有方法 - 领域对象与数据实体转换
    
    private DataImportTask entityToImportTask(TaskEntity entity) {
        // 实现从数据实体到领域对象的转换
        DataImportTask task = new DataImportTask(
            new TaskName(entity.getTaskName()),
            new Creator(entity.getCreator()),
            // 其他参数...
        );
        
        // 设置其他属性
        // ...
        
        return task;
    }
    
    private TaskEntity importTaskToEntity(DataImportTask task) {
        // 实现从领域对象到数据实体的转换
        TaskEntity entity = new TaskEntity();
        entity.setTaskId(task.getTaskId().getValue());
        entity.setTaskName(task.getTaskName().getValue());
        entity.setCreator(task.getCreator().getValue());
        entity.setStatus(task.getStatus().name());
        // 其他属性...
        
        return entity;
    }
    
    private DataCleanupTask entityToCleanupTask(TaskEntity entity) {
        // 实现从数据实体到领域对象的转换
        // ...
        return null;
    }
    
    private TaskEntity cleanupTaskToEntity(DataCleanupTask task) {
        // 实现从领域对象到数据实体的转换
        // ...
        return null;
    }
    
    private void saveTaskLogs(DataImportTask task) {
        // 保存任务日志
        for (TaskLog log : task.getLogs()) {
            TaskLogEntity logEntity = new TaskLogEntity();
            logEntity.setLogId(log.getLogId());
            logEntity.setTaskId(log.getTaskId());
            logEntity.setMessage(log.getMessage());
            logEntity.setLevel(log.getLevel().name());
            logEntity.setTimestamp(log.getTimestamp());
            logEntity.setStackTrace(log.getStackTrace());
            logEntity.setCategory(log.getCategory());
            
            taskLogMapper.insert(logEntity);
        }
    }
    
    private void saveTaskLogs(DataCleanupTask task) {
        // 保存任务日志
        for (TaskLog log : task.getLogs()) {
            TaskLogEntity logEntity = new TaskLogEntity();
            logEntity.setLogId(log.getLogId());
            logEntity.setTaskId(log.getTaskId());
            logEntity.setMessage(log.getMessage());
            logEntity.setLevel(log.getLevel().name());
            logEntity.setTimestamp(log.getTimestamp());
            logEntity.setStackTrace(log.getStackTrace());
            logEntity.setCategory(log.getCategory());
            
            taskLogMapper.insert(logEntity);
        }
    }
}
```

## 🎯 应用服务层（Application Layer）实现

### 1. DataImportApplicationService.java
```java
package com.zte.application.service;

import com.zte.domain.datamanage.aggregate.DataImportTask;
import com.zte.domain.datamanage.service.DataImportService;
import com.zte.domain.datamanage.repository.TaskRepository;
import com.zte.domain.datamanage.valueobject.*;
import com.zte.application.dto.CreateImportTaskCommand;
import com.zte.application.dto.ImportResultDTO;
import com.zte.domain.common.exception.DomainException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据导入应用服务
 * 
 * 职责：
 * 1. 协调领域对象完成用例
 * 2. 处理DTO与领域对象的转换
 * 3. 管理事务边界
 * 4. 处理横切关注点（如安全、日志）
 */
@Service
@Slf4j
public class DataImportApplicationService {
    
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataImportService dataImportService;
    
    /**
     * 创建数据导入任务
     */
    @Transactional
    public TaskId createImportTask(CreateImportTaskCommand command) {
        log.info("创建数据导入任务: taskName={}, creator={}", 
                command.getTaskName(), command.getCreator());
        
        try {
            // 1. 参数验证
            validateCreateCommand(command);
            
            // 2. 创建值对象
            DataSource dataSource = createDataSource(command);
            DataRange dataRange = createDataRange(command);
            ExecutionStrategy strategy = createExecutionStrategy(command);
            
            // 3. 创建聚合根
            DataImportTask task = new DataImportTask(
                new TaskName(command.getTaskName()),
                new Creator(command.getCreator()),
                dataSource,
                strategy,
                dataRange
            );
            
            // 4. 保存聚合根
            taskRepository.saveImportTask(task);
            
            log.info("数据导入任务创建成功: taskId={}", task.getTaskId().getValue());
            
            return task.getTaskId();
            
        } catch (DomainException e) {
            log.error("创建数据导入任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建数据导入任务失败", e);
            throw new RuntimeException("创建数据导入任务失败", e);
        }
    }
    
    /**
     * 执行数据导入任务
     */
    @Transactional
    public ImportResultDTO executeImportTask(TaskId taskId) {
        log.info("执行数据导入任务: taskId={}", taskId.getValue());
        
        try {
            // 1. 查询任务
            DataImportTask task = taskRepository.findImportTaskById(taskId);
            if (task == null) {
                throw new DomainException("任务不存在: " + taskId.getValue());
            }
            
            // 2. 执行导入
            ImportResult result = dataImportService.importData(task);
            
            // 3. 转换结果
            ImportResultDTO dto = new ImportResultDTO();
            dto.setSuccess(result.isSuccess());
            dto.setErrorMessage(result.getErrorMessage());
            dto.setProcessedRecords(result.getProcessedRecords());
            dto.setTotalRecords(result.getTotalRecords());
            dto.setTaskId(taskId.getValue());
            dto.setTaskName(task.getTaskName().getValue());
            dto.setExecutionTime(LocalDateTime.now());
            
            log.info("数据导入任务执行完成: taskId={}, success={}", 
                    taskId.getValue(), result.isSuccess());
            
            return dto;
            
        } catch (DomainException e) {
            log.error("执行数据导入任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("执行数据导入任务失败", e);
            throw new RuntimeException("执行数据导入任务失败", e);
        }
    }
    
    /**
     * 预览数据
     */
    public DataPreviewDTO previewData(TaskId taskId, int maxRecords) {
        log.info("预览数据: taskId={}, maxRecords={}", taskId.getValue(), maxRecords);
        
        try {
            // 1. 查询任务
            DataImportTask task = taskRepository.findImportTaskById(taskId);
            if (task == null) {
                throw new DomainException("任务不存在: " + taskId.getValue());
            }
            
            // 2. 预览数据
            DataPreview preview = dataImportService.previewData(task, maxRecords);
            
            // 3. 转换结果
            DataPreviewDTO dto = new DataPreviewDTO();
            dto.setColumnNames(preview.getColumnNames());
            dto.setSampleData(preview.getSampleData());
            dto.setTotalRecords(preview.getTotalRecords());
            dto.setTaskId(taskId.getValue());
            
            return dto;
            
        } catch (DomainException e) {
            log.error("预览数据失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("预览数据失败", e);
            throw new RuntimeException("预览数据失败", e);
        }
    }
    
    /**
     * 获取任务列表
     */
    public List<ImportTaskDTO> getImportTaskList(TaskQuery query) {
        log.info("查询数据导入任务列表: {}", query);
        
        try {
            List<DataImportTask> tasks;
            
            // 根据查询条件查询任务
            if (query.getStatus() != null) {
                tasks = taskRepository.findImportTasksByStatus(
                    DataImportTask.TaskStatus.valueOf(query.getStatus()));
            } else if (query.getCreator() != null) {
                tasks = taskRepository.findImportTasksByCreator(new Creator(query.getCreator()));
            } else if (query.getStartTime() != null && query.getEndTime() != null) {
                tasks = taskRepository.findImportTasksByTimeRange(query.getStartTime(), query.getEndTime());
            } else {
                // 默认查询最近的任务
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime = endTime.minusDays(30);
                tasks = taskRepository.findImportTasksByTimeRange(startTime, endTime);
            }
            
            // 转换为DTO
            return tasks.stream()
                    .map(this::convertToImportTaskDTO)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("查询数据导入任务列表失败", e);
            throw new RuntimeException("查询数据导入任务列表失败", e);
        }
    }
    
    /**
     * 取消导入任务
     */
    @Transactional
    public void cancelImportTask(TaskId taskId) {
        log.info("取消数据导入任务: taskId={}", taskId.getValue());
        
        try {
            // 1. 查询任务
            DataImportTask task = taskRepository.findImportTaskById(taskId);
            if (task == null) {
                throw new DomainException("任务不存在: " + taskId.getValue());
            }
            
            // 2. 取消任务
            dataImportService.cancelImport(task);
            
            log.info("数据导入任务已取消: taskId={}", taskId.getValue());
            
        } catch (DomainException e) {
            log.error("取消数据导入任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("取消数据导入任务失败", e);
            throw new RuntimeException("取消数据导入任务失败", e);
        }
    }
    
    // 私有方法
    
    private void validateCreateCommand(CreateImportTaskCommand command) {
        if (command == null) {
            throw new DomainException("创建命令不能为空");
        }
        
        if (command.getTaskName() == null || command.getTaskName().trim().isEmpty()) {
            throw new DomainException("任务名称不能为空");
        }
        
        if (command.getCreator() == null || command.getCreator().trim().isEmpty()) {
            throw new DomainException("创建者不能为空");
        }
        
        if (command.getProtocol() == null || command.getProtocol().trim().isEmpty()) {
            throw new DomainException("协议类型不能为空");
        }
        
        if (command.getHost() == null || command.getHost().trim().isEmpty()) {
            throw new DomainException("主机地址不能为空");
        }
        
        if (command.getPort() == null || command.getPort() < 1 || command.getPort() > 65535) {
            throw new DomainException("端口号必须在1-65535之间");
        }
    }
    
    private DataSource createDataSource(CreateImportTaskCommand command) {
        return DataSource.builder()
                .protocol(command.getProtocol())
                .host(command.getHost())
                .port(command.getPort())
                .username(command.getUsername())
                .encryptedPassword(command.getEncryptedPassword())
                .path(command.getPath())
                .type(DataSource.DataSourceType.valueOf(command.getDataSourceType()))
                .build();
    }
    
    private DataRange createDataRange(CreateImportTaskCommand command) {
        return DataRange.builder()
                .timeRangeType(DataRange.TimeRangeType.valueOf(command.getTimeRangeType()))
                .startTime(command.getStartTime())
                .endTime(command.getEndTime())
                .granularity(DataRange.TimeGranularity.valueOf(command.getGranularity()))
                .cellList(command.getCellList())
                .frequencyList(command.getFrequencyList())
                .build();
    }
    
    private ExecutionStrategy createExecutionStrategy(CreateImportTaskCommand command) {
        ExecutionStrategy strategy;
        
        if ("IMMEDIATE".equals(command.getExecutionType())) {
            strategy = ExecutionStrategy.createImmediate(null);
        } else if ("SCHEDULED".equals(command.getExecutionType())) {
            // 创建调度配置
            ScheduleConfig config = new ScheduleConfig();
            config.setExecuteTime(command.getScheduledTime());
            strategy = ExecutionStrategy.createScheduled(null, config);
        } else {
            throw new DomainException("不支持的执行类型: " + command.getExecutionType());
        }
        
        return strategy;
    }
    
    private ImportTaskDTO convertToImportTaskDTO(DataImportTask task) {
        ImportTaskDTO dto = new ImportTaskDTO();
        dto.setTaskId(task.getTaskId().getValue());
        dto.setTaskName(task.getTaskName().getValue());
        dto.setCreator(task.getCreator().getValue());
        dto.setStatus(task.getStatus().name());
        dto.setCreateTime(task.getCreateTime());
        dto.setStartTime(task.getStartTime());
        dto.setEndTime(task.getEndTime());
        dto.setProgress(task.getProgress().getPercentage());
        dto.setProcessedRecords(task.getProgress().getProcessedRecords());
        dto.setTotalRecords(task.getProgress().getTotalRecords());
        
        return dto;
    }
}
```

这个完整的DDD代码示例展示了如何将现有的数据管理工程重构为符合DDD原则的现代化架构。通过值对象、实体、聚合根、领域服务、仓储模式和应用服务的完整实现，可以显著提升代码的可维护性、可扩展性和业务对齐度。