#!/bin/bash
#su - gpadmin 
dba_user=gpadmin;
current_path=`pwd`;
chown -R gpadmin.gpadmin ${current_path}
gp_path=/home/<USER>



uninstalldatamanage(){
echo ${dba_user};
echo ${gp_path};
echo ${current_path};
#su - ${dba_user}
gpssh -f /home/<USER>/tmp_hosts_file.cc -v -e 'cd ${current_path}; chmod +x  ./StopDatamanageNew.sh; ./StopDatamanageNew.sh;   rm -rf *;'

echo "uninstall complete!!";
}

uninstall_gpdb(){
   echo ${gp_path};
   #pkill -9 -u $dba_user;  
   gpssh -f /home/<USER>/tmp_hosts_file.cc -v -e 'cd ${current_path}; chmod +x  ./StopDatamanageNew.sh; ./StopDatamanageNew.sh;'
   gpstop -a -M fast;
   gpssh -f /home/<USER>/tmp_hosts_file.cc -v -e 'cd  ${gp_path}; rm -rf *;';
}

uninstallServer(){
read -t 100 -p "Do you want to  remove the $dba_user database.[N/Y]:" CONTINUE_INSTALL
        if [ "$CONTINUE_INSTALL" == "Y" ] || [ "$CONTINUE_INSTALL" == "y" ] 
        then
            uninstall_gpdb 
        else
            uninstalldatamanage
        fi
}

uninstallServer