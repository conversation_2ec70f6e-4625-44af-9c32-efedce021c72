# ZTE OKR系统自动化脚本 - Playwright版本

## 简介
这是一个使用Playwright自动化工具来操作ZTE OKR系统的Python脚本。Playwright是一个现代化的浏览器自动化工具，比Selenium更稳定、更快速。

## 主要功能
- 自动登录到ZTE OKR系统
- 自动查找并点击PBC部分
- 自动选择上一个月
- 支持截图调试功能

## 安装步骤

### 1. 安装Python依赖
```bash
pip install playwright
```

### 2. 安装浏览器
首次运行需要安装浏览器引擎：
```bash
playwright install
```

或者安装特定浏览器：
```bash
playwright install chromium  # Chrome内核
playwright install firefox    # Firefox
playwright install webkit     # Safari内核
```

## 使用方法

### 运行脚本
```bash
python zte_okr_playwright.py
```

### 输入凭据
脚本运行后会提示输入：
- 用户名
- 密码

### 自动化流程
1. 自动打开浏览器并访问OKR系统
2. 自动填写登录信息并登录
3. 自动查找PBC部分并点击
4. 自动选择上一个月
5. 完成后等待用户按回车键关闭浏览器

## 配置选项

### 浏览器设置
在`setup_browser`方法中可以修改：
- `headless=False`: 设置为`True`可隐藏浏览器窗口
- 浏览器类型：可选择chromium、firefox或webkit
- 窗口大小：默认1920x1080

### 等待时间
可以调整各种等待时间：
- 页面加载等待：`wait_until="networkidle"`
- 元素查找超时：`timeout=10000`
- 操作间隔：`asyncio.sleep(3)`

## 调试功能

### 截图保存
如果找不到特定元素，脚本会自动截图保存：
- `pbc_not_found.png`: PBC部分未找到时的截图
- `month_selector_not_found.png`: 月份选择器未找到时的截图

### 日志输出
脚本会输出详细的执行日志，包括：
- 每个步骤的执行状态
- 找到的元素信息
- 错误详情

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装Playwright：`pip install playwright`
   - 确保已安装浏览器：`playwright install`

2. **元素查找失败**
   - 检查页面是否完全加载
   - 查看保存的截图文件
   - 调整等待时间

3. **登录失败**
   - 检查用户名和密码是否正确
   - 检查网络连接
   - 检查系统是否有验证码

### 手动调试
如果自动化失败，可以：
1. 查看截图文件了解页面状态
2. 手动打开浏览器访问系统
3. 使用浏览器开发者工具检查元素

## 安全注意事项

- 不要在代码中硬编码用户名和密码
- 脚本运行期间请勿手动操作浏览器
- 使用完毕后及时关闭浏览器

## 系统要求

- Python 3.7+
- Windows 10/11, macOS, 或 Linux
- 稳定的网络连接
- 有效的ZTE OKR系统账号

## 更新日志

- v1.0: 初始版本，支持基本登录和PBC操作
- 使用Playwright替代Selenium，提高稳定性
- 添加截图调试功能
- 优化元素查找策略
