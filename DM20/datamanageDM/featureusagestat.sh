#!/bin/bash

function __readINI() {
  ITEM=$1

  if [ -f featureusagestat.ini ]; then
    _readIni=`awk -F '=' '/\['config'\]/{a=1}a==1&&$1~/'$ITEM'/{print $2;exit}' featureusagestat.ini`
    echo ${_readIni}
  else
    echo ""
  fi
}

function __getmac() {
  host=`hostname`
  ip=`ping -c 1 $host |head -1 |awk -F'[()]' '{print $2}'`
  mac=`ifconfig |grep -A 2 "$ip" |grep ether |awk '{print $2}'|sed "s/://g"`
  echo $mac
}

function queryData() {
  echo "StatMonth,Product,NetWork,ProductVersion,ClientVersion,ModuleName,FeatureName,EventName,UserCount,UseTimes" > $file_name_tmp
  psql -A -F "," -t -c "SELECT Timegranularity,Product,NetWork,ProductVersion,ClientVersion,ModuleName,FeatureName,EventName,MAX(UserCount),SUM(SumCount) FROM FrequencyRecord_Stat GROUP BY Timegranularity,Product,NetWork,ProductVersion,ClientVersion,ModuleName,FeatureName,EventName ORDER BY Timegranularity,Product,NetWork,ProductVersion,ClientVersion,ModuleName,FeatureName,EventName" >> $file_name_tmp
}

lastQueryWeek=0

pid=$$
echo $pid > featureusagestat.pid

while [ -f featureusagestat.pid ]
do
  currpid=`cat featureusagestat.pid`

  if [ "x$pid" != "x$currpid" ]; then
    break
  fi
  currentTime=$( date +%s )
  weekday=$( date +%w )

  todayTime=$(( currentTime % 86400 ))

  if [ $weekday -gt 0 ] || [ $todayTime -gt 7200 ]; then
    currentWeek=$(( ( currentTime - 3 * 86400 ) / 7 / 86400 ))

    if [ $currentWeek -gt $lastQueryWeek ]; then
      lastQueryWeek=$currentWeek
      unitname=( $( __readINI unitname ) )
      if [ "x$unitname" == "x" ]; then
        unitname=( $( __getmac ) )
      fi
      file_name="NGI_FeatureUsageStat_$unitname.csv"
      file_name_tmp="$file_name.tmp"
    
      queryData

      path=( $( __readINI path ) )

      if [ "x$path" == "x" ]; then
        path="/home/<USER>/ngi-gpdb/ext/dm/emsdata/featureusagestat"
      fi
      if [ ! -d $path/ ]; then
        mkdir -p $path
      fi
      if [ -f $path/$file_name ]; then
        rm -f $path/$file_name
      fi
      mv $file_name_tmp $path/$file_name
    fi
  fi

  sleep 60s
done
