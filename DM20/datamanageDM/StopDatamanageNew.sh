#!/bin/bash

#关闭SFTPServer
shutdown_sftpServer()
{
	if [ "$user" == "gpadmin" ]
	then
		gpdbInstallFlag=`psql -l 2>/dev/null | grep netmaxdb | grep -v grep | wc -l`
		if [[ $gpdbInstallFlag -eq 1 ]];  then
			local RESULT=""
			if [ -f "$SFTPSERVER_HOME/stopsftp.sh" ]
			then 
			   echo "shutdown sftpServer process....."
			   RESULT=`cd $SFTPSERVER_HOME; ./stopsftp.sh 15434`
			   sleep  2
			fi
		   
			echo "shutdown sftpServer completely"
		fi
	else
		gpdbInstallFlag=`su - gpadmin -c "psql -l" 2>/dev/null | grep netmaxdb | grep -v grep | wc -l`
		if [[ $gpdbInstallFlag -eq 1 ]];  then
			local RESULT=""
			if [ -f "$SFTPSERVER_HOME/stopsftp.sh" ]
			then 
			   echo "shutdown sftpServer process....."
			   RESULT=`cd $SFTPSERVER_HOME; ./stopsftp.sh 15434`
			   sleep  2
			fi
		   
			echo "shutdown sftpServer completely"
		fi
		
	fi
}



DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
user=`whoami`;

SFTPSERVER_HOME="${HOME}/SFTPServer"

#关闭SFTPServer
shutdown_sftpServer

process_id=$(ps -ef | grep datamanage-newcode | grep -v "grep" | awk '{print $2}')

if [[ ! -z "$process_id" ]]
then
kill -9 $process_id
#else
#echo empty
fi
echo "Stop datamanage-newcode process end"
#cd  $HOME/bin/DataManage/
#关闭数据管理下载和入库服务
chmod +x $HOME/bin/DataManage/stopstandalone.sh
$HOME/bin/DataManage/stopstandalone.sh $1

#关闭EMS下载
chmod +x  $HOME/EMSPreProcess/bin/shutdown_EMSPreProcess.sh
$HOME/EMSPreProcess/bin/shutdown_EMSPreProcess.sh

#关闭NRUME下载
chmod +x  $HOME/NRUMEPreProcess/bin/shutdown_NRUMEPreProcess.sh
$HOME/NRUMEPreProcess/bin/shutdown_NRUMEPreProcess.sh

#关闭NGI使用频次数据导出脚本
rm -vf $HOME/featureusagestat.pid

chmod +x  $HOME/NGIDBMonitor/bin/shutdown_NGIDBMonitor.sh
#关闭NGIDBMonitor下载
if [ "$1"x != "-987789"x ]
then 
  $HOME/NGIDBMonitor/bin/shutdown_NGIDBMonitor.sh
fi

#关闭CM队列
cmTaskList=`ps -ef|grep queue.sh|grep -v grep|grep -v "vi"|awk '{print $2}'`
if [ -n "$cmTaskList" ]
then
    execute=`echo "$cmTaskList"|xargs kill -9`
fi

echo "Stop all process end"



