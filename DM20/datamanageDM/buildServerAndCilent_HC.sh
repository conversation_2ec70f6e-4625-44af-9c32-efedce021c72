#!/bin/sh

#当前目录netmax/platform/datamanage/DM20/datamanageDM
SourcePath=$( cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
#ngi/doc目录
ngiOpenDBPath=../../../../../ngi/doc
#ngi/resource目录
ngiResourcePath=../../../../../ngi/resource
#前端代码路径(本地代码路径需要注意修改)
NovPath=../DM-NOVisualization
#输入版本号
Version=$1

#编译前端
cat >/root/.nmprc <<'EOF'
registry=https://artsh.zte.com.cn/artifactory/api/npm/ume-npm-virtual/

_auth = dW1lLWNpOkFLQ3A1ZWp4eFVoUnVUdHBqSmtkRGVZcmpqQzZTUThBOFZQckpaenpwVnVqTFN2eWV1enJ3U1RUOHVTRmQzWXR5TjRmWnV3OHI=

always-auth = true

email = <EMAIL>
EOF

#切前端代码目录
cd "${NovPath}"
#call npm install
#npm config set sass_binary_site https://artnj.zte.com.cn/artifactory/api/npm/zenap-npm-virtual/
npm install --unsafe-perm
npm run build
if [ $? -ne 0 ]; then
    exit -1
fi

#rd /s /q %{SourcePath}%/src/main/resources
rm -rf "${SourcePath}/src/main/resources"
#md %{SourcePath}%/src/main/resources/DM-NOVisualization
mkdir -p "${SourcePath}/src/main/resources/DM-NOVisualization"

#xcopy "${NovPath}/dist/*" "${SourcePath}/src/main/resources/DM-NOVisualization"
cp -rf "${NovPath}/dist/." "${SourcePath}/src/main/resources/DM-NOVisualization"

#编译后端代码
cd "${SourcePath}"
#call mvn clean package
mvn clean package
if [ $? -ne 0 ]; then
    exit -1
fi

#移除Swagger服务
zip -d "${SourcePath}/target/datamanage-newcode-1.0-SNAPSHOT.jar" com/zte/ums/zenap/swagger/SwaggerManager.class

#编译SFTP
cd "${SourcePath}/../SFTPServer"
mvn clean package
#编译NGI监控
cd "${SourcePath}/../NGIDBMonitor"
mvn clean package
#编译DataDownloadMonitor
cd "${SourcePath}/../DataDownloadMonitor"
mvn clean package

#编译alertManager
cd "${SourcePath}/../alertManager"
mvn clean package


#"copy jar..."
cd "${SourcePath}"
mkdir "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/target/datamanage-newcode-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest"

#"copy files..."
cp -rf "${SourcePath}/StartDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/StartDatamanageNew.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/StopDatamanageNew.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/featureusagestat.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/datamanage.keystore" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/UnistallDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/UnistallDatamanagefirst.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/gpsshDatamanageStart.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/gpsshDatamanageStop.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/version.cc" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/openssl.cnf" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/BackupDatamanage.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/OperateTableSpace.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/SSDVolumePath.cfg" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/EmsDispatcher.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/clearCMrecord.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/dataReuse.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/collectLog.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/rebootStart.sh" "${SourcePath}/datamanagetest"
cp -rf "${SourcePath}/rebootStart.cron" "${SourcePath}/datamanagetest"

mkdir "${SourcePath}/datamanagetest/sql_script"
cp -rf  "${SourcePath}/sql_script/." "${SourcePath}/datamanagetest/sql_script"

mkdir "${SourcePath}/datamanagetest/data"
cp -rf "${SourcePath}/data/." "${SourcePath}/datamanagetest/data"

mkdir "${SourcePath}/datamanagetest/lib"
cp -rf "${SourcePath}/lib/." "${SourcePath}/datamanagetest/lib"
mkdir "${SourcePath}/datamanagetest/conf"
cp -rf "${SourcePath}/conf/." "${SourcePath}/datamanagetest/conf"
mkdir "${SourcePath}/datamanagetest/bin"
cp -rf "${SourcePath}/bin/." "${SourcePath}/datamanagetest/bin"
# 更新file2sp.xml文件
zip -u -j "${SourcePath}/datamanagetest/bin/DataManage/ImportData_LTE.jar" "${SourcePath}/Server/LTE/DataManage/ImportData_GP/src/main/resources/ltefile2sp.xml"
zip -u -j "${SourcePath}/datamanagetest/bin/DataManage/ImportData_5G.jar" "${SourcePath}/Server/5G/DataManage/ImportData_GP/src/main/resources/g5file2sp.xml"
mkdir "${SourcePath}/datamanagetest/versionbak"
cp -rf "${SourcePath}/versionbak/." "${SourcePath}/datamanagetest/versionbak"
mkdir "${SourcePath}/datamanagetest/DB"
cp -rf "${SourcePath}/../DB/." "${SourcePath}/datamanagetest/DB"
mkdir "${SourcePath}/datamanagetest/EMSPreProcess"
cp -rf "${SourcePath}/EMSPreProcess/." "${SourcePath}/datamanagetest/EMSPreProcess"
mkdir "${SourcePath}/datamanagetest/NRUMEPreProcess"
cp -rf "${SourcePath}/NRUMEPreProcess/." "${SourcePath}/datamanagetest/NRUMEPreProcess"

mkdir "${SourcePath}/datamanagetest/SFTPServer"
cp -rf "${SourcePath}/SFTPServer/." "${SourcePath}/datamanagetest/SFTPServer"
cp -rf "${SourcePath}/../SFTPServer/target/lib/." "${SourcePath}/datamanagetest/SFTPServer/lib"
# 更新 sshd-sftp-2.1.0.jar 文件，解决zxjdk8u282及之后版本不兼容LinkOption.NOFOLLOW_LINKS，导致sftp命令上传文件失败的问题
# https://i.zte.com.cn/#/space/ee6e3a1534c6478ab944c0664639253f/wiki/page/66dcd7ba5d2d4ce69619ee53521aa770/view
replaceFilePath="${SourcePath}/../SFTPServer/sshd-sftp-2.1.0.jar.zipe"
if [ -f "$replaceFilePath" ];then
    zipKey=$(unzip -q -z "$replaceFilePath" | awk '{print $2}')
    if [ "$zipKey" ];then
        unzip -o -P "$zipKey" "$replaceFilePath" -d "${SourcePath}/datamanagetest/SFTPServer/lib"
    fi
fi
cp -rf "${SourcePath}/../SFTPServer/target/SFTPServer-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/SFTPServer/lib"

#NGIDBMonitor打包
mkdir "${SourcePath}/datamanagetest/NGIDBMonitor"
cp -rf "${SourcePath}/NGIDBMonitor/." "${SourcePath}/datamanagetest/NGIDBMonitor"
cp -rf "${SourcePath}/../NGIDBMonitor/target/lib/." "${SourcePath}/datamanagetest/NGIDBMonitor/lib"
cp -rf "${SourcePath}/../NGIDBMonitor/target/NGIDBMonitor-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/NGIDBMonitor/bin"

#alertManager 
mkdir "${SourcePath}/datamanagetest/alertOperator"
cp -rf "${SourcePath}/alertOperator/." "${SourcePath}/datamanagetest/alertOperator"
mv -f "${SourcePath}/../alertManager/target/alertManager-1.0-SNAPSHOT.jar" "${SourcePath}/../alertManager/target/alertOperator-1.0-SNAPSHOT.jar"
cp -rf "${SourcePath}/../alertManager/target/alertOperator-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/alertOperator/bin"


#DataDownloadMonitor打包 
mkdir "${SourcePath}/datamanagetest/DataDownloadMonitor"
cp -rf "${SourcePath}/DataDownloadMonitor/." "${SourcePath}/datamanagetest/DataDownloadMonitor"
cp -rf "${SourcePath}/../DataDownloadMonitor/target/lib/." "${SourcePath}/datamanagetest/DataDownloadMonitor/lib"
cp -rf "${SourcePath}/../DataDownloadMonitor/target/DataDownloadMonitor-1.0-SNAPSHOT.jar" "${SourcePath}/datamanagetest/DataDownloadMonitor/bin"

#CMJobQueue
mkdir "${SourcePath}/datamanagetest/CMJobQueue"
cp -rf "${SourcePath}/CMJobQueue/." "${SourcePath}/datamanagetest/CMJobQueue/"

#"zip files..."
rm -rf datamanage-newcode.zip
rm -rf datamanage-service.zip
rm -rf ranoss_ngi-dm_*.tar.gz

cd "${SourcePath}/datamanagetest"
zip -r "datamanage-newcode.zip" *
cp "datamanage-newcode.zip" "${SourcePath}"
cd "${SourcePath}"
zip -r "datamanage-service.zip" "datamanage-newcode.zip" "InstallDatamanage.sh" "SSDVolumePath.cfg"

#生成TCF版本包，目录结构如下
#ranoss_ngi-dm_Master.tar.gz.d
#└── ngi-dm
#    ├── business-images
#    │   └── ngi-dm-service
#    │       ├── ngi-dm-service.tar.gz    -->ngi-dm-service (ngi-dm-service.tar.gz解压后的目录)
#    │       │                               ├── blueprint
#    │       │                               │   └── template_ngi-dm-service.json
#    │       │                               └── datamanage-sl
#    │       │                                   ├── change.list
#    │       │                                   ├── datamanager-updatePaas.zip
#    │       │                                   ├── datamanage-service.zip
#    │       │                                   ├── ant.zip
#    │       │                                   └── Dockerfile
#    │       └── persistentvolume.properties
#    │       └── commonservice-instance-config.xml
#    └── config
#        ├── ngi-dm.desc
#        └── ngi-dm.spd
if [ -f datamanage-service.zip ];then
    rm -rf ngi-dm
    
    mkdir -p ngi-dm/business-images/ngi-dm-service/ngi-dm-service/{blueprint,datamanage-sl}
    mkdir -p ngi-dm/config
    
    if [ -d dockerfile/ant ];then
        (cd dockerfile && zip -r ../ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/ant.zip ant)
    else
        echo "${SourcePath}/dockerfile/ant directory does not exists"
        exit 3
    fi
    
    cp -v $ngiResourcePath/app/ngi-dm-hucang/ngi-dm-service/blueprint/template_ngi-dm-service.json ngi-dm/business-images/ngi-dm-service/ngi-dm-service/blueprint/    
    cp -v datamanage-service.zip ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/
    sed -i 's/javaaf:.*/javaaf:'"$javaaf"'/g' dockerfile/Dockerfile
    cp -v dockerfile/Dockerfile ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/
    cp -v dockerfile/shell/* ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/
    cp -v version.cc ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/
    touch ngi-dm/business-images/ngi-dm-service/ngi-dm-service/datamanage-sl/change.list
    
    (cd ngi-dm/business-images/ngi-dm-service && tar -zcf ngi-dm-service.tar.gz ngi-dm-service && rm -rf ngi-dm-service)
    
    if [ -f ngi-dm/business-images/ngi-dm-service/ngi-dm-service.tar.gz ];then
        cp -v $ngiResourcePath/app/ngi-dm-hucang/ngi-dm-service/persistentvolume.properties ngi-dm/business-images/ngi-dm-service/
		cp -v $ngiResourcePath/app/ngi-dm-hucang/ngi-dm-service/commonservice-instance-config.xml ngi-dm/business-images/ngi-dm-service/
        cp -v $ngiResourcePath/app/ngi-dm-hucang/ngi-dm.desc ngi-dm/config/
        cp -v $ngiResourcePath/app/ngi-dm-hucang/ngi-dm.spd ngi-dm/config/
        #生成最终的安装包
        tar -zcvf ranoss_ngi-dm_${Version}.tar.gz ngi-dm
        echo "Package success: ${SourcePath}/ranoss_ngi-dm_${Version}.tar.gz"
    else
        echo "${SourcePath}/ngi-dm/business-images/ngi-dm-service/ngi-dm-service.tar.gz does not exists"
        exit 4
    fi
else
    echo "${SourcePath}/datamanage-service.zip does not exists"
    exit 2
fi

#"clean..."
rm -rf datamanage-newcode.zip
rm -rf "${SourcePath}/datamanagetest"

#"===== build ServerAndCilent End====="
