#!/bin/bash

#参数初始化
u31lte="false"
umelte="false"
umenr="false"

#接受入参
if [[ "$1" = "true" ]]
then
    u31lte=$1
fi

if [[ "$2" = "true" ]]
then
    umelte=$2
fi

if [[ "$3" = "true" ]]
then
    umenr=$3
fi

#if [[ "${umelte}" = "true" ]]
#hen
#    echo "EmsDispatcher.sh: Start run LTE EMS UME"
#    chmod +x /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/bin/runEMSPreProcess.sh
#    /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/bin/runEMSPreProcess.sh "run" "true"
#    echo "EmsDispatcher.sh: LTE EMS UME end"
#fi &
echo "EmsDispatcher.sh: u31lte:${u31lte} umelte:${umelte} umenr:${umenr}"

# 删除可能的历史遗留文件，避免影响任务进程判断
rm -rf /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/*
rm -rf /home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/sourcedata/*

# 获取配置文件中超时时间
conf=$(cat /home/<USER>/ngi-gpdb/ext/dm/conf/common_dataimport.conf|grep "protection_timeout_minutes")
#echo "conf: ${conf}"
protection_timeout_minutes=$(echo "$conf" | awk -F '=' '{print $2}')
echo "protection_timeout_minutes: ${protection_timeout_minutes}"

if [[ "${u31lte}" = "true" ]] || [[ "${umelte}" = "true" ]]
then
    echo "EmsDispatcher.sh: Start run LTE EMS"
    chmod +x /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/bin/runEMSPreProcess.sh
    timeout ${protection_timeout_minutes}m /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/bin/runEMSPreProcess.sh "run" "${umelte}" "${u31lte}"
    if [ $? -eq 124 ]; then
       echo "LTE EMS ERROR runEMSPreProcess.sh timeout"
       # 防止异常超时后sourcedata目录下残留的文件影响任务进程判断
       # rm -rf /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/*
    fi
    echo "EmsDispatcher.sh: LTE EMS end"
fi & 


if [ "${umenr}" = "true" ]
then
   echo "EmsDispatcher.sh: Start run NR EMS"
   chmod +x /home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/bin/runCMPreProcess.sh
   timeout ${protection_timeout_minutes}m /home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/bin/runCMPreProcess.sh
   if [ $? -eq 124 ]; then
      echo "NR EMS ERROR runEMSPreProcess.sh timeout"
      # 防止异常超时后sourcedata目录下残留的文件影响任务进程判断
      # rm -rf /home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/sourcedata/*
   fi
   echo "EmsDispatcher.sh: NR EMS end"
fi &

# 等待所有后台进程完成
wait

# 删除可能由于异常导致的遗留文件，避免影响任务进程判断
rm -rf /home/<USER>/ngi-gpdb/ext/dm/EMSPreProcess/sourcedata/*
rm -rf /home/<USER>/ngi-gpdb/ext/dm/NRUMEPreProcess/sourcedata/*

echo "EmsDispatcher.sh: All tasks completed"