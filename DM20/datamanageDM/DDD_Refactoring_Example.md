# 数据管理工程 - DDD重构示例

## 📋 重构概述

基于现有的数据管理工程代码，本文档展示如何将传统的面向过程代码重构为DDD（领域驱动设计）架构。

## 🔄 现有代码分析

### 当前代码结构
```java
// 现有的Task类 - 典型的贫血模型
@Data
public class Task {
    private String taskId;        // 任务的唯一标识
    private String taskName;      // 任务名
    private String creator;       // 创建者
    private String creatTime;     // 创建时间
    private String startTime;     // 开始时间
    private String endTime;       // 结束时间
    private String strategy;      // 策略（JSON字符串）
    private String taskStatus;     // 任务状态
    private String taskTypeId;    // 任务类型ID
    private String taskType_en;   // 任务类型英文
    private String taskType_zh;   // 任务类型中文
    private TaskExecParams taskExecParams; // 执行参数
    private String taskParam;     // 任务参数
    private String process;        // 进度
    private String result;         // 结果
}
```

### 现有问题
1. **贫血模型**: 只有数据，没有业务逻辑
2. **类型不安全**: 使用String表示各种类型
3. **状态管理混乱**: 状态转换没有约束
4. **业务逻辑分散**: 业务逻辑散落在各个Service中

## 🏗️ DDD重构方案

### 1. 聚合根重构

#### 重构后的DataImportTask聚合根
```java
// 聚合根 - 数据导入任务
public class DataImportTask {
    private final TaskId taskId;
    private final TaskName taskName;
    private final Creator creator;
    private final TaskType taskType;
    
    private TaskStatus status;
    private DataSource dataSource;        // 值对象
    private ExecutionStrategy strategy;    // 实体
    private DataRange dataRange;          // 值对象
    private ImportProgress progress;      // 值对象
    private List<TaskLog> logs;           // 实体集合
    
    private LocalDateTime createTime;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    // 业务方法
    public void startExecution() {
        if (!canExecute()) {
            throw new IllegalStateException("任务当前状态不允许执行: " + status);
        }
        this.status = TaskStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.addLog("任务开始执行", LogLevel.INFO);
    }
    
    public void completeExecution() {
        if (status != TaskStatus.RUNNING) {
            throw new IllegalStateException("只有运行中的任务才能完成");
        }
        this.status = TaskStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        this.addLog("任务执行完成", LogLevel.INFO);
    }
    
    public boolean canExecute() {
        return status == TaskStatus.CREATED || status == TaskStatus.PAUSED;
    }
}
```

### 2. 值对象设计

#### DataSource值对象
```java
// 值对象 - 数据源
public class DataSource {
    private final String protocol;            // 协议类型 (SFTP/FTP/HTTP)
    private final String host;                // 主机地址
    private final Integer port;               // 端口号
    private final String username;            // 用户名
    private final String encryptedPassword;    // 加密密码
    private final String path;                // 路径
    private final DataSourceType type;        // 数据源类型 (NDS/PM/CM/FM/VMAX)
    
    // 业务方法
    public ConnectionInfo getConnectionInfo() {
        return new ConnectionInfo(protocol, host, port, username, encryptedPassword);
    }
    
    public boolean isValid() {
        return host != null && !host.isEmpty() && 
               port != null && port > 0 && 
               username != null && !username.isEmpty();
    }
    
    // 重写equals和hashCode确保值对象语义
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataSource that = (DataSource) o;
        return Objects.equals(protocol, that.protocol) &&
               Objects.equals(host, that.host) &&
               Objects.equals(port, that.port) &&
               Objects.equals(username, that.username) &&
               Objects.equals(encryptedPassword, that.encryptedPassword) &&
               Objects.equals(path, that.path) &&
               type == that.type;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(protocol, host, port, username, encryptedPassword, path, type);
    }
}
```

#### DataRange值对象
```java
// 值对象 - 数据范围
public class DataRange {
    private final TimeRangeType timeRangeType; // 时间范围类型 (相对/绝对)
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final TimeGranularity granularity; // 时间粒度
    private final List<String> cellList;      // 网元列表
    private final List<String> frequencyList;  // 频点列表
    
    // 业务方法
    public boolean isValidRange() {
        return startTime != null && endTime != null && 
               startTime.isBefore(endTime) &&
               granularity != null;
    }
    
    public Duration getDuration() {
        return Duration.between(startTime, endTime);
    }
    
    public boolean contains(LocalDateTime time) {
        return !time.isBefore(startTime) && !time.isAfter(endTime);
    }
}
```

### 3. 实体设计

#### ExecutionStrategy实体
```java
// 实体 - 执行策略
public class ExecutionStrategy {
    private StrategyId strategyId;
    private ExecutionType executionType;      // 执行类型 (立即/定时/周期)
    private ScheduleConfig scheduleConfig;    // 调度配置
    private RetryPolicy retryPolicy;          // 重试策略
    private NotificationConfig notificationConfig; // 通知配置
    
    // 业务方法
    public boolean shouldExecuteNow() {
        if (executionType == ExecutionType.IMMEDIATE) {
            return true;
        }
        if (executionType == ExecutionType.SCHEDULED) {
            return LocalDateTime.now().isAfter(scheduleConfig.getExecuteTime());
        }
        if (executionType == ExecutionType.PERIODIC) {
            return shouldExecuteBasedOnCron();
        }
        return false;
    }
    
    public boolean canRetry() {
        return retryPolicy != null && 
               retryPolicy.getCurrentRetryCount() < retryPolicy.getMaxRetryCount();
    }
    
    public void updateRetryCount() {
        if (retryPolicy != null) {
            retryPolicy.incrementRetryCount();
        }
    }
}
```

#### TaskLog实体
```java
// 实体 - 任务日志
public class TaskLog {
    private String logId;
    private String message;
    private LogLevel level;
    private LocalDateTime timestamp;
    
    // 业务方法
    public boolean isError() {
        return level == LogLevel.ERROR;
    }
    
    public boolean isWarning() {
        return level == LogLevel.WARNING;
    }
    
    public String getFormattedMessage() {
        return String.format("[%s] [%s] %s", 
            timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            level,
            message);
    }
}
```

### 4. 领域服务

#### DataImportService领域服务
```java
// 领域服务 - 数据导入
@Service
public class DataImportService {
    
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataSourceAdapter dataSourceAdapter;
    
    public ImportResult importData(DataImportTask task) {
        try {
            // 1. 验证任务状态
            if (!task.canExecute()) {
                return ImportResult.failed("任务状态不允许执行");
            }
            
            // 2. 验证数据源
            if (!validateDataSource(task.getDataSource())) {
                return ImportResult.failed("数据源验证失败");
            }
            
            // 3. 开始执行任务
            task.startExecution();
            taskRepository.save(task);
            
            // 4. 执行数据导入
            ImportResult result = executeDataImport(task);
            
            // 5. 更新任务状态
            if (result.isSuccess()) {
                task.completeExecution();
            } else {
                task.failExecution(result.getErrorMessage());
            }
            
            taskRepository.save(task);
            return result;
            
        } catch (Exception e) {
            task.failExecution("导入过程中发生异常: " + e.getMessage());
            taskRepository.save(task);
            return ImportResult.failed("导入过程中发生异常: " + e.getMessage());
        }
    }
    
    private boolean validateDataSource(DataSource dataSource) {
        return dataSourceAdapter.testConnection(dataSource);
    }
    
    private ImportResult executeDataImport(DataImportTask task) {
        // 实现具体的数据导入逻辑
        // 这里可以调用原有的ImportJob逻辑，但需要进行封装
        return ImportResult.success();
    }
}
```

### 5. 仓储模式

#### TaskRepository接口
```java
// 仓储接口 - 任务
public interface TaskRepository {
    DataImportTask findImportTaskById(TaskId taskId);
    DataCleanupTask findCleanupTaskById(TaskId taskId);
    void saveImportTask(DataImportTask task);
    void saveCleanupTask(DataCleanupTask task);
    List<DataImportTask> findImportTasksByStatus(DataImportTask.TaskStatus status);
    List<DataImportTask> findImportTasksByCreator(Creator creator);
    List<DataImportTask> findImportTasksByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
}
```

#### TaskRepositoryImpl实现
```java
// 仓储实现 - 任务
@Repository
public class TaskRepositoryImpl implements TaskRepository {
    
    @Inject
    private TaskMapper taskMapper;  // MyBatis或JPA映射器
    
    @Override
    public DataImportTask findImportTaskById(TaskId taskId) {
        TaskEntity entity = taskMapper.findById(taskId.getValue());
        return entityToDomain(entity);
    }
    
    @Override
    public void saveImportTask(DataImportTask task) {
        TaskEntity entity = domainToEntity(task);
        if (taskMapper.findById(task.getTaskId().getValue()) == null) {
            taskMapper.insert(entity);
        } else {
            taskMapper.update(entity);
        }
    }
    
    // 领域对象与数据实体之间的转换
    private DataImportTask entityToDomain(TaskEntity entity) {
        // 转换逻辑
        return new DataImportTask(...);
    }
    
    private TaskEntity domainToEntity(DataImportTask task) {
        // 转换逻辑
        return new TaskEntity(...);
    }
}
```

### 6. 应用服务层

#### DataImportApplicationService
```java
// 应用服务 - 数据导入
@Service
public class DataImportApplicationService {
    
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataImportService dataImportService;
    
    @Inject
    private TaskScheduleService taskScheduleService;
    
    public TaskId createImportTask(CreateImportTaskCommand command) {
        // 1. 创建值对象
        DataSource dataSource = new DataSource(
            command.getProtocol(),
            command.getHost(),
            command.getPort(),
            command.getUsername(),
            command.getEncryptedPassword(),
            command.getPath(),
            command.getDataSourceType()
        );
        
        DataRange dataRange = new DataRange(
            command.getTimeRangeType(),
            command.getStartTime(),
            command.getEndTime(),
            command.getGranularity(),
            command.getCellList(),
            command.getFrequencyList()
        );
        
        ExecutionStrategy strategy = new ExecutionStrategy(
            command.getExecutionType(),
            command.getScheduleConfig(),
            command.getRetryPolicy(),
            command.getNotificationConfig()
        );
        
        // 2. 创建聚合根
        DataImportTask task = new DataImportTask(
            new TaskName(command.getTaskName()),
            new Creator(command.getCreator()),
            dataSource,
            strategy,
            dataRange
        );
        
        // 3. 保存聚合根
        taskRepository.saveImportTask(task);
        
        // 4. 如果是定时任务，创建调度
        if (strategy.getExecutionType() == ExecutionType.SCHEDULED) {
            TaskSchedule schedule = createSchedule(task);
            taskScheduleService.scheduleTask(schedule);
        }
        
        return task.getTaskId();
    }
    
    public void executeImportTask(TaskId taskId) {
        DataImportTask task = taskRepository.findImportTaskById(taskId);
        dataImportService.importData(task);
    }
}
```

## 🔄 从现有代码到DDD的映射

### ImportJob重构对比

#### 原有ImportJob
```java
public class ImportJob extends Job {
    @Override
    public int run() {
        if(ErrorCode.OP_FAILED == createTable()){
            log.error("ImportJob: create table [" + tableName + "] error");
            return ErrorCode.OP_FAILED;
        }
        
        try{
            String url = GreenPlumDBI.getUrl();
            String userName = GreenPlumDBI.getInstance().getUsername();
            String passWord = GreenPlumDBI.getInstance().getPassword();
            DBCopyManager.copyFromFile(tableName, fileName, url, userName, passWord);
        } catch (Exception e) {
            log.error("ImportJob error");
            return ErrorCode.OP_FAILED;
        }
        return ErrorCode.OP_SUCCESS;
    }
}
```

#### DDD重构后的ImportJob
```java
public class ImportJob {
    @Inject
    private DataImportService dataImportService;
    
    @Inject
    private TaskRepository taskRepository;
    
    public ImportResult execute(TaskId taskId) {
        DataImportTask task = taskRepository.findImportTaskById(taskId);
        return dataImportService.importData(task);
    }
}
```

### TaskDelete重构对比

#### 原有TaskDelete
```java
public class TaskDelete {
    public int start() {
        String taskInfo = String.join(",", "taskID=", taskExecParams.getTaskId(), ...);
        log.info("delete task start:" + taskInfo);
        
        List<String> taskLogId = getDeleteTaskLogId();
        deleteTask();
        staticTaskLogServce.updateTaskProcess("30", taskExecParams.getTaskLogId());
        dropTaskResultTable(taskLogId);
        staticTaskLogServce.updateTaskProcess("60", taskExecParams.getTaskLogId());
        deleteGisInfo(taskLogId);
        staticTaskLogServce.updateTaskProcess("100", taskExecParams.getTaskLogId());
        
        log.info("delete task end");
        return 0;
    }
}
```

#### DDD重构后的DataCleanupService
```java
@Service
public class DataCleanupService {
    
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private CleanupService cleanupService;
    
    public CleanupResult cleanupData(DataCleanupTask task) {
        try {
            // 1. 安全性检查
            SafetyCheckResult safetyCheck = performSafetyCheck(task);
            if (!safetyCheck.isSafe()) {
                return CleanupResult.failed("安全检查失败: " + safetyCheck.getReason());
            }
            
            // 2. 生成清除计划
            CleanupPlan plan = generateCleanupPlan(task);
            
            // 3. 开始清除任务
            task.startCleanup();
            taskRepository.save(task);
            
            // 4. 执行数据清除
            CleanupResult result = cleanupService.executeCleanup(plan);
            
            // 5. 更新任务状态
            if (result.isSuccess()) {
                task.completeCleanup();
            } else {
                task.failCleanup(result.getErrorMessage());
            }
            
            taskRepository.save(task);
            return result;
            
        } catch (Exception e) {
            task.failCleanup("清除过程中发生异常: " + e.getMessage());
            taskRepository.save(task);
            return CleanupResult.failed("清除过程中发生异常: " + e.getMessage());
        }
    }
}
```

## 🎯 重构收益

### 1. 代码质量提升
- **类型安全**: 使用值对象替代原始类型
- **业务封装**: 业务逻辑集中在聚合根中
- **状态管理**: 明确的状态转换约束
- **测试友好**: 依赖注入和接口抽象

### 2. 可维护性提升
- **职责分离**: 清晰的层次结构
- **模块化**: 每个聚合都有明确的边界
- **可扩展性**: 新功能可以通过扩展聚合实现

### 3. 业务对齐
- **领域语言**: 代码直接反映业务概念
- **业务规则**: 业务逻辑集中在领域层
- **一致性**: 聚合根保证内部一致性

## 📋 实施建议

### 1. 重构优先级
1. **高优先级**: DataImportTask聚合根和核心业务逻辑
2. **中优先级**: DataCleanupTask聚合和相关服务
3. **低优先级**: 任务调度和监控功能

### 2. 重构策略
1. **渐进式重构**: 逐步替换现有代码
2. **并行运行**: 新旧系统并行运行一段时间
3. **充分测试**: 确保重构后的功能正确性

### 3. 技术选型
- **框架**: Spring Boot
- **持久化**: Spring Data JPA
- **验证**: Bean Validation
- **测试**: JUnit + Mockito

这个DDD重构示例为数据管理工程提供了现代化的架构设计，有助于提高代码质量、可维护性和业务对齐度。