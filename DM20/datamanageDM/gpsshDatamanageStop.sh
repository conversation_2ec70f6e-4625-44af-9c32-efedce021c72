#!/bin/bash
#执行并校验gpssh执行结果
# Param1: host type(h,f)
# Param2: ip or file
# Param3: cmd
# Call example: gpsshResultCheck "h" "${gpMasterIP}" "${cmd}"
function gpsshResultCheck()
{
    echo "Start executing gpssh..."
    if [ $# -lt 3 ];then
        echo "Error: invalid parameters, exitting"
        return 1
    fi
    type=$1
    hosts=$2
    cmd=$3
    #dba_user="gpadmin"
    echo "Input parameter for gpssh...${1} ${2} ${3}"
    for((i=1;i<=20;++i))
       do
         #ret=`su - ${dba_user} -c "gpssh -${type} ${hosts} -v -e '${cmd}'"`
         if [ "$user" == "gpadmin" ]
         then
             ret=`gpssh -${type} ${hosts} -v -e "${cmd}" 2>&1`
         else
             ret=`su - ${dba_user} -c "gpssh -${type} ${hosts} -v -e '${cmd}' 2>&1"`
         fi
         #echo "${ret}"
         echo "After gpssh execution, check result..."
         resultCheck=$(echo "${ret}"|grep "Traceback")
         if [[ "$resultCheck" != "" ]]
         then
             if [ $i -lt 20 ];then
                 echo "Result check failed for executing gpssh, begin retry ($i)..."
                 sleep 10
             else
                 echo "ERROR: execute ${cmd} in gpssh failed, exitting..."
                 exit 1
             fi
         else
             echo "Result check for gpssh success."
             break;
         fi
    done
}
DIRNAME=`dirname $0`
current_path=`cd $DIRNAME/; pwd`

dba_user=gpadmin;
gp_path=/home/<USER>
user=`whoami`;

#echo ${user};
#echo ${current_path}
#echo ${gp_path}


if [ "$user" == "gpadmin" ]
then
  #gpadmin用户，停止服务
  #echo ${user};
  cd ${current_path};
  chmod +x  StopDatamanageNew.sh
  ./StopDatamanageNew.sh $1; 
  if [ -f "${gp_path}/tmp_hosts_seg.cc" ]; then  
      echo "+++++++++++++1" 
      #gpssh -f ${gp_path}/tmp_hosts_seg.cc -v -e "chmod +x ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh"
      cmd="chmod +x ${current_path}/bin/DataManage/stopstandalone.sh; ${current_path}/bin/DataManage/stopstandalone.sh $1";

	  echo ${cmd}
	  echo "-------------"
      gpsshResultCheck "f" "${gp_path}/tmp_hosts_seg.cc" "${cmd}"
  elif [ -f "${gp_path}/paas_hosts_seg.cc" ]; then
      echo "+++++++++++++2"
      #gpssh -f ${gp_path}/paas_hosts_seg.cc -v -e "chmod +x ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh"
      cmd="chmod +x ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh $1";
	  echo "+++++++++++++2"
	  echo ${cmd}
	  echo "-------------"
      gpsshResultCheck "f" "${gp_path}/paas_hosts_seg.cc" "${cmd}"
  fi
else
  #非gpadmin用户
  #echo ${user};
  #echo ${current_path};
  #集群停止服务
  if [ -f "${gp_path}/tmp_hosts_seg.cc" ]; then
      echo "+++++++++++++3"
      su - ${dba_user} -c "cd ${current_path}; ./StopDatamanageNew.sh $1;"   
      #gpssh -f ${gp_path}/tmp_hosts_seg.cc -v -e "cd ${current_path}; echo ${current_path}; chmod +x  ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh" "
      cmd="cd ${current_path}; chmod +x  ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh $1";
	  echo "+++++++++++++3"
	  echo ${cmd}
	  echo "-------------"
      gpsshResultCheck "f" "${gp_path}/paas_hosts_seg.cc" "${cmd}"
  elif [ -f "${gp_path}/paas_hosts_seg.cc" ]; then
      echo "+++++++++++++4"
      su - ${dba_user} -c "cd ${current_path}; ./StopDatamanageNew.sh $1;" 
      #gpssh -f ${gp_path}/paas_hosts_seg.cc -v -e "cd ${current_path}; echo ${current_path}; chmod +x  ${current_path}/bin/DataManage/stopstandalone.sh;  ${current_path}/bin/DataManage/stopstandalone.sh" 
      cmd="cd ${current_path}; chmod +x  ${current_path}/bin/DataManage/stopstandalone.sh; ${current_path}/bin/DataManage/stopstandalone.sh $1";
	  echo "+++++++++++++4"
	  echo ${cmd}
	  echo "-------------"
      gpsshResultCheck "f" "${gp_path}/paas_hosts_seg.cc" "${cmd}"
  else
      echo "+++++++++++++5" 
      su - ${dba_user} -c "cd ${current_path}; ./StopDatamanageNew.sh $1;"
  fi
fi 

echo "gpsshDatamanageStop complete...";