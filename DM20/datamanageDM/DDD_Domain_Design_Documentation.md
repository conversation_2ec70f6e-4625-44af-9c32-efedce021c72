# 数据管理工程 - DDD领域驱动设计文档

## 📋 领域划分

### 核心子域（Core Domain）- 数据管理组件
**业务价值**: 最高的业务价值，包含核心竞争力和差异化特性

### 支撑子域（Supporting Domain）
- **NDS (Network Data Source)**: 网络数据源管理
- **PM (Performance Management)**: 性能管理数据处理
- **CM (Configuration Management)**: 配置管理数据处理
- **FM (Fault Management)**: 故障管理数据处理
- **VMAX**: 虚拟化管理平台数据源

### 通用子域（Generic Domain）
- **安全**: 认证、授权、加密
- **日志**: 日志记录、监控、审计
- **文件操作**: 文件传输、存储、压缩
- **任务调度**: 定时任务、批处理

## 🏗️ 领域模型设计

### 1. 数据导入任务聚合（DataImportTask Aggregate）

#### 聚合根：DataImportTask
```java
// 聚合根 - 数据导入任务
public class  {
    private TaskId taskId;                    // 任务唯一标识
    private TaskName taskName;                // 任务名称
    private Creator creator;                   // 创建者
    private TaskStatus status;                 // 任务状态
    private DataSource dataSource;            // 数据源值对象
    private ExecutionStrategy strategy;        // 执行策略实体
    private DataRange dataRange;              // 数据范围值对象
    private ImportProgress progress;          // 导入进度值对象
    private List<TaskLog> logs;               // 任务日志实体集合
    private LocalDateTime createTime;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    // 业务方法
    public void startExecution() { /*...*/ }
    public void pauseExecution() { /*...*/ }
    public void resumeExecution() { /*...*/ }
    public void completeExecution() { /*...*/ }
    public void failExecution(String reason) { /*...*/ }
    public boolean canExecute() { /*...*/ }
}
```

#### 值对象：DataSource
```java
// 值对象 - 数据源
public class DataSource {
    private final String protocol;            // 协议类型 (SFTP/FTP/HTTP)
    private final String host;                // 主机地址
    private final Integer port;               // 端口号
    private final String username;            // 用户名
    private final String encryptedPassword;    // 加密密码
    private final String path;                // 路径
    private final DataSourceType type;        // 数据源类型 (NDS/PM/CM/FM/VMAX)
    
    // 业务方法
    public ConnectionInfo getConnectionInfo() { /*...*/ }
    public boolean isValid() { /*...*/ }
    public String getConnectionString() { /*...*/ }
}
```

#### 实体：ExecutionStrategy
```java
// 实体 - 执行策略
public class ExecutionStrategy {
    private StrategyId strategyId;
    private ExecutionType executionType;      // 执行类型 (立即/定时/周期)
    private ScheduleConfig scheduleConfig;    // 调度配置
    private RetryPolicy retryPolicy;          // 重试策略
    private NotificationConfig notificationConfig; // 通知配置
    
    // 业务方法
    public boolean shouldExecuteNow() { /*...*/ }
    public LocalDateTime getNextExecutionTime() { /*...*/ }
    public boolean canRetry() { /*...*/ }
}
```

#### 值对象：DataRange
```java
// 值对象 - 数据范围
public class DataRange {
    private final TimeRangeType timeRangeType; // 时间范围类型 (相对/绝对)
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final TimeGranularity granularity; // 时间粒度
    private final List<String> cellList;      // 网元列表
    private final List<String> frequencyList;  // 频点列表
    
    // 业务方法
    public boolean isValidRange() { /*...*/ }
    public Duration getDuration() { /*...*/ }
    public boolean contains(LocalDateTime time) { /*...*/ }
}
```

### 2. 数据清除任务聚合（DataCleanupTask Aggregate）

#### 聚合根：DataCleanupTask
```java
// 聚合根 - 数据清除任务
public class DataCleanupTask {
    private TaskId taskId;
    private TaskName taskName;
    private Creator creator;
    private TaskStatus status;
    private DataSource targetDataSource;     // 目标数据源
    private CleanupStrategy cleanupStrategy;  // 清除策略
    private CleanupProgress progress;
    private List<TaskLog> logs;
    private LocalDateTime createTime;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    // 业务方法
    public void startCleanup() { /*...*/ }
    public void pauseCleanup() { /*...*/ }
    public void validateCleanupScope() { /*...*/ }
    public boolean canCleanup() { /*...*/ }
}
```

#### 实体：CleanupStrategy
```java
// 实体 - 清除策略
public class CleanupStrategy {
    private StrategyId strategyId;
    private CleanupType cleanupType;          // 清除类型 (按时间/按数量/按条件)
    private CleanupScope scope;                // 清除范围
    private RetentionPolicy retentionPolicy;  // 保留策略
    private SafetyCheck safetyCheck;           // 安全检查
    
    // 业务方法
    public boolean shouldCleanup(DataInfo data) { /*...*/ }
    public CleanupPlan generateCleanupPlan() { /*...*/ }
    public boolean isSafeToCleanup() { /*...*/ }
}
```

### 3. 任务调度聚合（TaskSchedule Aggregate）

#### 聚合根：TaskSchedule
```java
// 聚合根 - 任务调度
public class TaskSchedule {
    private ScheduleId scheduleId;
    private TaskId taskId;
    private ScheduleType scheduleType;        // 调度类型
    private CronExpression cronExpression;    // Cron表达式
    private ScheduleStatus status;
    private LocalDateTime lastExecutionTime;
    private LocalDateTime nextExecutionTime;
    private ExecutionHistory executionHistory;
    
    // 业务方法
    public void activateSchedule() { /*...*/ }
    public void deactivateSchedule() { /*...*/ }
    public boolean shouldExecuteNow() { /*...*/ }
    public void recordExecution(ExecutionResult result) { /*...*/ }
}
```

## 🔄 领域服务

### 1. 数据导入服务（DataImportService）
```java
// 领域服务 - 数据导入
public class DataImportService {
    public ImportResult importData(DataImportTask task) {
        // 1. 验证任务状态
        // 2. 建立数据源连接
        // 3. 执行数据导入
        // 4. 更新任务状态
        // 5. 记录执行日志
    }
    
    public boolean validateDataSource(DataSource dataSource) {
        // 验证数据源配置
    }
    
    public DataPreview previewData(DataImportTask task) {
        // 预览数据
    }
}
```

### 2. 数据清除服务（DataCleanupService）
```java
// 领域服务 - 数据清除
public class DataCleanupService {
    public CleanupResult cleanupData(DataCleanupTask task) {
        // 1. 安全性检查
        // 2. 生成清除计划
        // 3. 执行数据清除
        // 4. 验证清除结果
        // 5. 记录清除日志
    }
    
    public SafetyCheckResult performSafetyCheck(DataCleanupTask task) {
        // 执行安全检查
    }
}
```

### 3. 任务调度服务（TaskScheduleService）
```java
// 领域服务 - 任务调度
public class TaskScheduleService {
    public void scheduleTask(TaskSchedule schedule) {
        // 调度任务
    }
    
    public void executeScheduledTasks() {
        // 执行定时任务
    }
    
    public List<TaskSchedule> getPendingSchedules() {
        // 获取待执行调度
    }
}
```

## 📊 仓储模式

### 1. 任务仓储（TaskRepository）
```java
// 仓储接口 - 任务
public interface TaskRepository {
    DataImportTask findImportTaskById(TaskId taskId);
    DataCleanupTask findCleanupTaskById(TaskId taskId);
    void saveImportTask(DataImportTask task);
    void saveCleanupTask(DataCleanupTask task);
    List<DataImportTask> findImportTasksByStatus(TaskStatus status);
    List<DataCleanupTask> findCleanupTasksByCreator(Creator creator);
}
```

### 2. 调度仓储（ScheduleRepository）
```java
// 仓储接口 - 调度
public interface ScheduleRepository {
    TaskSchedule findById(ScheduleId scheduleId);
    void save(TaskSchedule schedule);
    List<TaskSchedule> findActiveSchedules();
    List<TaskSchedule> findSchedulesByTaskId(TaskId taskId);
}
```

## 🏛️ 应用服务层

### 1. 数据导入应用服务
```java
// 应用服务 - 数据导入
public class DataImportApplicationService {
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataImportService dataImportService;
    
    @Inject
    private TaskScheduleService taskScheduleService;
    
    public TaskId createImportTask(CreateImportTaskCommand command) {
        // 1. 创建聚合根
        DataImportTask task = new DataImportTask(
            new TaskId(UUID.randomUUID().toString()),
            command.getTaskName(),
            command.getCreator(),
            command.getDataSource(),
            command.getStrategy(),
            command.getDataRange()
        );
        
        // 2. 保存聚合根
        taskRepository.saveImportTask(task);
        
        // 3. 如果是定时任务，创建调度
        if (command.getStrategy().isScheduled()) {
            TaskSchedule schedule = createSchedule(task);
            taskScheduleService.scheduleTask(schedule);
        }
        
        return task.getTaskId();
    }
    
    public void executeImportTask(TaskId taskId) {
        DataImportTask task = taskRepository.findImportTaskById(taskId);
        dataImportService.importData(task);
        taskRepository.saveImportTask(task);
    }
}
```

### 2. 数据清除应用服务
```java
// 应用服务 - 数据清除
public class DataCleanupApplicationService {
    @Inject
    private TaskRepository taskRepository;
    
    @Inject
    private DataCleanupService dataCleanupService;
    
    public TaskId createCleanupTask(CreateCleanupTaskCommand command) {
        DataCleanupTask task = new DataCleanupTask(
            new TaskId(UUID.randomUUID().toString()),
            command.getTaskName(),
            command.getCreator(),
            command.getTargetDataSource(),
            command.getCleanupStrategy()
        );
        
        taskRepository.saveCleanupTask(task);
        return task.getTaskId();
    }
    
    public void executeCleanupTask(TaskId taskId) {
        DataCleanupTask task = taskRepository.findCleanupTaskById(taskId);
        dataCleanupService.cleanupData(task);
        taskRepository.saveCleanupTask(task);
    }
}
```

## 🎯 限界上下文

### 1. 数据管理上下文（Data Management Context）
- **范围**: 数据导入、清除、转换的核心业务逻辑
- **核心模型**: DataImportTask, DataCleanupTask, DataSource
- **主要职责**: 数据生命周期管理

### 2. 任务调度上下文（Task Scheduling Context）
- **范围**: 任务调度、执行、监控
- **核心模型**: TaskSchedule, ExecutionStrategy
- **主要职责**: 任务执行管理

### 3. 数据源上下文（Data Source Context）
- **范围**: 数据源管理、连接、配置
- **核心模型**: DataSource, ConnectionInfo
- **主要职责**: 数据源抽象和管理

### 4. 监控告警上下文（Monitoring Context）
- **范围**: 系统监控、告警处理
- **核心模型**: Alert, Monitor
- **主要职责**: 系统健康状态管理

## 🔄 集成模式

### 1. 上下文映射
- **共享内核**: TaskId, TaskStatus 等核心概念
- **防腐层**: 与外部系统（NDS、PM、CM、FM）的集成
- **开放主机服务**: 提供REST API供其他系统调用
- **发布者-订阅者**: 任务状态变更通知

### 2. 事件驱动架构
```java
// 领域事件
public class TaskCompletedEvent {
    private final TaskId taskId;
    private final TaskType taskType;
    private final LocalDateTime completionTime;
    private final ExecutionResult result;
}

public class TaskFailedEvent {
    private final TaskId taskId;
    private final TaskType taskType;
    private final LocalDateTime failureTime;
    private final String failureReason;
}
```

## 📋 实施建议

### 1. 重构优先级
1. **高优先级**: DataImportTask聚合根和核心业务逻辑
2. **中优先级**: DataCleanupTask聚合和相关服务
3. **低优先级**: 任务调度和监控功能

### 2. 技术实现
- 使用Spring Boot作为应用框架
- 使用Spring Data JPA实现仓储模式
- 使用Spring Events实现领域事件
- 使用MapStruct进行DTO转换

### 3. 测试策略
- **单元测试**: 聚合根和值对象的业务逻辑
- **集成测试**: 仓储和领域服务
- **端到端测试**: 完整的业务流程

这个DDD设计为数据管理工程提供了清晰的领域模型和架构指导，有助于提高代码的可维护性、可扩展性和业务对齐度。