#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZTE OKR系统自动化脚本 - 使用Playwright
用于自动登录并操作人员视图、考核定级等
"""

import asyncio
import datetime
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext
import aiohttp
import json

class ZTEOKRAutomationPlaywright:
    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.base_url = "https://unipos.zte.com.cn:18080/#/oct/okr"
        
    async def setup_browser(self):
        """设置Playwright浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--window-size=1920,1080"
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            print("Playwright浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"浏览器初始化失败: {e}")
            return False
    
    async def uac_login(self):
        """UAC一键登录到OKR系统"""
        try:
            print(f"正在访问: {self.base_url}")
            await self.page.goto(self.base_url, wait_until="networkidle")
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 查找UAC一键登录按钮
            uac_button = await self.page.wait_for_selector("button:has-text('UAC一键登录')", timeout=3000)
            
            if uac_button:
                # 点击UAC一键登录
                await uac_button.click()
                print("UAC一键登录按钮已点击")
                
                # 等待登录完成
                await asyncio.sleep(8)
                
                # 检查登录后的页面状态
                current_url = self.page.url
                print(f"登录后当前URL: {current_url}")
                
                # 如果不在OKR页面，尝试导航到OKR
                if "okr" not in current_url.lower():
                    print("登录成功，但不在OKR页面，尝试导航到OKR...")
                    await self.navigate_to_okr()
                
                print("UAC一键登录成功！")
                return True
            else:
                print("未找到UAC一键登录按钮")
                return False
                
        except Exception as e:
            print(f"UAC一键登录过程中出现错误: {e}")
            return False
    
    async def navigate_to_okr(self):
        """导航到OKR页面"""
        try:
            print("正在尝试导航到OKR页面...")
            
            # 直接访问OKR页面
            print(f"尝试访问: {self.base_url}")
            await self.page.goto(self.base_url, wait_until="networkidle")
            await asyncio.sleep(3)
            
            current_url = self.page.url
            if "okr" in current_url.lower():
                print(f"成功导航到OKR页面: {current_url}")
                return True
            else:
                print("无法导航到OKR页面")
                return False
                
        except Exception as e:
            print(f"导航到OKR页面时出现错误: {e}")
            return False
    
    async def find_personnel_view(self):
        """查找人员视图"""
        try:
            print("正在查找人员视图...")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 精确查找人员视图元素
            personnel_element = await self.page.wait_for_selector("span:has-text('人员')", timeout=2000)
            
            if personnel_element:
                # 点击人员视图
                await personnel_element.click()
                print("人员视图已点击")
                await asyncio.sleep(3)
                return True
            else:
                print("未找到人员视图")
                return False
                
        except Exception as e:
            print(f"查找人员视图时出现错误: {e}")
            return False
    
    async def select_month_in_personnel_view(self):
        """在人员视图中选择上一个月"""
        try:
            print("正在人员视图中查找月份选择器...")
            
            # 获取当前月份
            current_month = datetime.datetime.now().month
            previous_month = current_month - 1 if current_month > 1 else 12
            
            print(f"当前月份: {current_month}, 选择月份: {previous_month}")
            
            # 等待页面加载
            await asyncio.sleep(1)
            
            # 精确查找月份元素（使用数字格式）
            month_element = await self.page.wait_for_selector(f"span:has-text('{previous_month:02d}')", timeout=1000)
            
            if month_element:
                await month_element.click()
                print(f"已选择月份: {previous_month}")
                await asyncio.sleep(1)
                return True
            else:
                print("未找到月份选择器")
                return False
                
        except Exception as e:
            print(f"在人员视图中选择月份时出现错误: {e}")
            return False
    
    async def click_assessment_grading(self):
        """点击考核定级"""
        try:
            print("正在查找考核定级按钮...")
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 精确查找考核定级元素
            assessment_element = await self.page.wait_for_selector("span:has-text('考核定级')", timeout=2000)
            
            if assessment_element:
                # 记录当前页面数量
                pages_before = len(self.context.pages)
                print(f"点击考核定级前，当前有 {pages_before} 个页面")
                
                # 点击考核定级
                await assessment_element.click()
                print("考核定级已点击")
                
                # 等待新页面打开
                print("等待新页面打开...")
                await asyncio.sleep(3)
                
                # 检查是否有新页面打开
                pages_after = len(self.context.pages)
                print(f"点击考核定级后，当前有 {pages_after} 个页面")
                
                if pages_after > pages_before:
                    # 有新页面打开，切换到新页面
                    new_page = self.context.pages[-1]  # 获取最后一个页面（新页面）
                    await new_page.bring_to_front()  # 将新页面带到前台
                    self.page = new_page  # 更新当前页面引用
                    print("已切换到新页面")
                    
                    # 等待新页面加载
                    await asyncio.sleep(3)
                    
                    # 检查新页面的URL
                    new_url = self.page.url
                    print(f"新页面URL: {new_url}")
                    
                    # 如果新页面URL包含assessment，说明跳转成功
                    if "assessment" in new_url.lower():
                        print("成功跳转到考核定级页面")
                        return True
                    else:
                        print("新页面URL不包含assessment，可能跳转失败")
                        return False
                else:
                    print("没有新页面打开")
                    return False
            else:
                print("未找到考核定级按钮")
                return False
                
        except Exception as e:
            print(f"点击考核定级时出现错误: {e}")
            return False
    
    async def update_assessment_comments(self):
        """
        遍历表格数据行，获取"考核S/A2需提供评语"列的内容
        将评语内容与提示词拼凑，组成完整的提示词供大模型调用
        并将更新后的评语写回到表格中
        """
        print("开始遍历表格数据行...")
        
        # 查找表格中的所有数据行
        data_rows = await self.page.query_selector_all("tbody tr")
        print(f"找到 {len(data_rows)} 行数据")
        
        if len(data_rows) == 0:
            print("未找到数据行")
            return True
        
        # 查找"考核S/A2需提供评语"列标题，确定列索引
        print("查找'考核S/A2需提供评语'列...")
        all_headers = await self.page.query_selector_all("thead th")
        
        comment_column_index = -1
        for i, header in enumerate(all_headers):
            try:
                header_text = await header.text_content()
                if header_text and "考核S/A2需提供评语" in header_text:
                    comment_column_index = i
                    print(f"找到'考核S/A2需提供评语'列，索引位置: {i}")
                    break
            except Exception as e:
                print(f"检查列标题 {i} 时出错: {e}")
                continue
        
        if comment_column_index == -1:
            print("未找到'考核S/A2需提供评语'列")
            return True
        
        # 读取提示词模板
        prompt_template = self.read_prompt_template()
        
        # 遍历每一行数据，获取评语列的内容并调用大模型生成评语
        print("开始获取每行评语内容并调用大模型生成评语...")
        for row_index, row in enumerate(data_rows):
            try:
                # 获取该行的所有单元格
                cells = await row.query_selector_all("td")
                
                if len(cells) > comment_column_index:
                    # 获取评语列的内容
                    comment_cell = cells[comment_column_index]
                    comment_text = await comment_cell.text_content()
                    
                    # 获取该行的基本信息（比如工号或姓名）用于标识
                    identifier = "未知"
                    if len(cells) > 0:
                        try:
                            identifier = await cells[0].text_content()  # 第一列通常是工号或姓名
                            identifier = identifier.strip() if identifier else "未知"
                        except:
                            pass
                    
                    # 清理评语内容并用引号引起来
                    cleaned_comment = comment_text.strip() if comment_text else "无评语"
                    quoted_comment = f'"{cleaned_comment}"'
                    
                    print(f"第 {row_index + 1} 行 - {identifier}: 评语内容 = {quoted_comment}")
                    
                    # 生成完整的提示词
                    full_prompt = self.generate_full_prompt(prompt_template, identifier, quoted_comment)
                    
                    # 调用大模型API生成评语
                    print(f"正在调用大模型为 {identifier} 生成评语...")
                    generated_comment = await self.call_llm_api(full_prompt)
                    
                    if generated_comment:
                        print(f"=== {identifier} 的生成评语 ===")
                        print(generated_comment)
                        print("=" * 50)
                        
                        # 将更新后的评语写回到表格中
                        print(f"正在将更新后的评语写回到表格中...")
                        if await self.write_comment_back_to_table(comment_cell, generated_comment):
                            print(f"成功将评语写回到表格中")
                        else:
                            print(f"将评语写回到表格中失败")
                        
                        # 等待一下确保写入完成
                        await asyncio.sleep(1)
                        
                        # 保存评语并关闭弹框
                        print("正在保存评语...")
                        try:
                            # 先点击"保存"按钮保存评语
                            print("查找并点击'保存'按钮...")
                            save_button = await self.page.wait_for_selector(
                                "div[role='dialog'] button:has-text('保存'), .ant-modal button:has-text('保存'), [class*='modal'] button:has-text('保存')", 
                                timeout=3000
                            )
                            if save_button:
                                await save_button.click()
                                print("已点击'保存'按钮，评语已保存")
                                # 等待保存操作完成
                                await asyncio.sleep(2)
                            else:
                                print("未找到'保存'按钮，尝试直接关闭弹框")
                        except Exception as save_e:
                            print(f"保存评语时出错: {save_e}")
                            print("保存失败，但继续关闭弹框")
                        
                        # 无论保存是否成功，都要确保弹框被关闭
                        print("正在关闭弹框...")
                        try:
                            await self.page.keyboard.press("Escape")
                            print("已按ESC键关闭弹框")
                            # 等待弹框关闭动画完成
                            await asyncio.sleep(2)
                            
                            # 验证弹框是否已关闭
                            try:
                                # 尝试查找弹框，如果找不到说明已关闭
                                modal = await self.page.wait_for_selector("div[role='dialog'], .ant-modal, [class*='modal']", timeout=1000)
                                if modal:
                                    print("弹框仍然存在，再次尝试ESC键关闭...")
                                    await self.page.keyboard.press("Escape")
                                    await asyncio.sleep(1)
                                else:
                                    print("弹框已成功关闭")
                            except:
                                print("弹框已成功关闭")
                                
                        except Exception as close_e:
                            print(f"关闭弹框时出错: {close_e}")
                            print("弹框关闭失败，可能影响后续处理")
                            # 尝试强制关闭弹框
                            try:
                                await self.page.keyboard.press("Escape")
                                await asyncio.sleep(1)
                                print("已尝试强制关闭弹框")
                            except:
                                print("强制关闭弹框也失败")
                    else:
                        print(f"为 {identifier} 生成评语失败")
                    
                else:
                    print(f"第 {row_index + 1} 行: 列数不足，无法获取评语内容")
                    
            except Exception as e:
                print(f"处理第 {row_index + 1} 行时出错: {e}")
                continue
        
        print("表格数据遍历完成，所有评语已通过大模型生成并写回到表格中")
        return True
    
    async def call_llm_api(self, prompt):
        """调用大模型API生成评语"""
        api_url = "http://nebulacoder.dev.zte.com.cn:40081/v1/chat/completions"
        api_key = "10072775"
        model_id = "nebulacoder-lite-v7.0"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=60)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            generated_text = result["choices"][0]["message"]["content"]
                            return generated_text
                        else:
                            print(f"API响应格式异常: {result}")
                            return None
                    else:
                        print(f"API调用失败，状态码: {response.status}")
                        error_text = await response.text()
                        print(f"错误信息: {error_text}")
                        return None
        except Exception as e:
            print(f"调用大模型API时出错: {e}")
            return None

    async def write_comment_back_to_table(self, comment_cell, new_comment):
        """将更新后的评语写回到表格中"""
        try:
            # 检查单元格是否可编辑
            is_editable = await comment_cell.get_attribute("contenteditable")
            
            if is_editable == "true":
                # 如果单元格是可编辑的，直接设置内容
                await comment_cell.fill(new_comment)
                print("通过fill方法写入评语")
                return True
            else:
                # 尝试双击单元格使其变为可编辑状态
                await comment_cell.dblclick()
                await asyncio.sleep(1)
                
                # 检查是否变为可编辑状态
                is_editable_after_click = await comment_cell.get_attribute("contenteditable")
                if is_editable_after_click == "true":
                    await comment_cell.fill(new_comment)
                    print("通过双击后fill方法写入评语")
                    return True
                else:
                    # 尝试使用type方法直接输入
                    await comment_cell.click()
                    await asyncio.sleep(0.5)
                    await comment_cell.type(new_comment)
                    print("通过type方法写入评语")
                    return True
                    
        except Exception as e:
            print(f"写入评语到表格时出错: {e}")
            return False

    def read_prompt_template(self):
        """读取提示词模板"""
        try:
            with open("kaohe-prompt.md", "r", encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            print("警告: 未找到kaohe-prompt.md文件，使用默认提示词")
            return self.get_default_prompt()
    
    def get_default_prompt(self):
        """获取默认提示词模板"""
        return """#**Given
## 【**角色/Roles**】
你是一个软件研发部门的中层管理者部长，有14年的管理和教练经验，学习过很多的管理理论，善于通过言语激励下属，也关注下属的成长

## 【**场景/Scenarios**】
###**术语**
部长：一个部门的中层管理者，下面还有一些APO和SM，一般管理幅度60-200人左右
APO：领域的Product Owner，是基层管理者，其上级是部长，下级是SCRUM团队，一般管理规模10-30人左右。他们承担交付职责和管理职责

###**填写月度考核评语**
根据APO日常工作的过程记录的内容以及后面的**输出要求**填写APO的月度评语

## 【**目的/Purposes**】
为了方便下属了解自己的业绩和改进点，以便他们清楚自己的工作绩效情况，并且认可自己的绩效等级，同时通过评语中的改进点更好的改进自己的工作

## 【**准则/Oracles**】
**输出格式**
按照如下格式输入评语
工作业绩：xxx
工作亮点：xxx
改进建议：xxx

**通用要求**
该APO的考核评语内容只能从对应姓名所在行的记录中提取，不能从其他行中提取
只需要处理考核组为部门公共的行，其他的行请忽略

**工作亮点**
对于业绩比较突出的部分，进一步提炼变成亮点
关注一些正向的词汇，作为亮点内容的参考
注意这部分不要和工作业绩重复

**改进建议**
保持*改进建议*的准确性，不要进行随意合并
每行记录中不再需要"改进建议"这个前缀
注意评语中不要包含"XX评议"的文字
保持*改进建议*的准确性，不要进行随意合并
不要忽略或者合并不同的**改进建议**
如果没有找到改进建议，直接填写"无"

#**when**
##【**Input/输入**】
{comment_content}

#**then**
##【**Output/输出**】
请根据上述输入内容，按照准则要求生成APO的月度考核评语。"""
    
    def generate_full_prompt(self, template, identifier, comment_content):
        """生成完整的提示词"""
        # 在模板的Input部分插入评语内容
        if "#**when**" in template and "##【**Input/输入**】" in template:
            # 找到Input部分并插入评语内容
            parts = template.split("##【**Input/输入**】")
            if len(parts) >= 2:
                # 在Input部分后添加姓名和评语内容
                input_section = f"##【**Input/输入**】\n姓名：{identifier}\n评语内容：\n{comment_content}\n"
                full_prompt = parts[0] + input_section + parts[1]
                return full_prompt
        
        # 如果模板格式不匹配，使用默认格式
        return template.replace("{comment_content}", f"姓名：{identifier}\n评语内容：\n{comment_content}")
    
    def save_prompt_to_file(self, filename, content):
        """保存提示词到文件"""
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
        except Exception as e:
            print(f"保存文件 {filename} 时出错: {e}")

    async def filter_assessment_group(self):
        """在考核晋级页面筛选考核组为"智能化五部" """
        try:
            print("开始筛选考核组...")
            await asyncio.sleep(3)
            
            # 直接查找所有下拉框，找到包含"智能化五部"选项的
            print("查找包含'智能化五部'选项的下拉框...")
            
            # 使用通用选择器查找所有可能的下拉框
            all_dropdowns = await self.page.query_selector_all(
                "select, .ant-select, .ant-select-selector, [class*='select'], [class*='dropdown'], input[readonly], input[type='text'], div[role='combobox'], div[tabindex], button[role='combobox']"
            )
            
            print(f"找到 {len(all_dropdowns)} 个下拉框元素")
            
            # 遍历所有下拉框，找到包含"智能化五部"选项的
            dropdown = None
            for i, dd in enumerate(all_dropdowns):
                try:
                    print(f"检查下拉框 {i+1}...")
                    
                    # 点击下拉框展开选项
                    await dd.click()
                    await asyncio.sleep(0.5)
                    
                    # 查找是否包含"智能化五部"选项
                    option = await self.page.wait_for_selector(
                        "text=智能化五部", 
                        timeout=1000
                    )
                    
                    if option:
                        print(f"找到包含'智能化五部'选项的下拉框 {i+1}")
                        dropdown = dd
                        break
                    else:
                        # 点击其他地方关闭下拉框
                        await self.page.click("body")
                        await asyncio.sleep(0.2)
                except Exception as e:
                    print(f"检查下拉框 {i+1} 时出错: {e}")
                    continue
            
            if not dropdown:
                print("未找到包含'智能化五部'选项的下拉框")
                return False
            
            # 选择"智能化五部"选项
            print("准备选择'智能化五部'...")
            await dropdown.click()
            await asyncio.sleep(1)
            
            option = await self.page.wait_for_selector("text=智能化五部", timeout=2000)
            if option:
                await option.click()
                print("已选择'智能化五部'")
                await asyncio.sleep(2)
            else:
                print("未找到'智能化五部'选项")
                return False
            
            # 等待数据加载完成后，再进行表格筛选
            print("等待数据加载...")
            await asyncio.sleep(3)
            
            # 查找表格中的考核组列标题
            assessment_group_header = await self.page.wait_for_selector(
                "th:has-text('考核组')", 
                timeout=5000
            )
            
            if not assessment_group_header:
                print("未找到考核组列标题")
                return False
            
            print("找到考核组列标题")
            
            # 在考核组列标题中查找漏斗图标
            filter_icon = await assessment_group_header.query_selector(
                "i[class*='filter'], i[class*='funnel'], span[class*='filter'], span[class*='funnel']"
            )
            
            if not filter_icon:
                print("未找到考核组列的漏斗图标")
                return False
            
            print("找到考核组列的漏斗图标，准备点击...")
            
            # 点击漏斗图标
            await filter_icon.click()
            print("漏斗图标已点击")
            
            # 等待过滤框完全展开
            await asyncio.sleep(2)
            
            # 精确查找输入框
            filter_input = await self.page.wait_for_selector("input[placeholder*='搜索考核组']", timeout=2000)
            
            if not filter_input:
                print("未找到搜索输入框")
                return False
            
            print("找到搜索输入框，准备输入'公共'...")
            
            # 直接使用fill输入
            await filter_input.fill("公共")
            print("已输入'公共'")
            
            # 验证输入是否成功
            input_value = await filter_input.input_value()
            print(f"输入框当前值: '{input_value}'")
            
            if input_value != "公共":
                print("输入失败")
                return False
            
            print("输入成功！")
            
            # 等待过滤结果加载
            await asyncio.sleep(2)
            
            # 精确查找可见的Search按钮
            search_button = await self.page.wait_for_selector("button:has-text('Search'):visible", timeout=3000)
            
            if not search_button:
                print("未找到可见的Search按钮")
                return False
            
            print("找到Search按钮，准备点击...")
            await search_button.click()
            print("Search按钮已点击，筛选完成")
            
            # 等待筛选结果加载
            await asyncio.sleep(3)
            
            # 关闭过滤框（点击其他地方或按ESC键）
            try:
                # 尝试点击页面其他地方关闭过滤框
                await self.page.click("body")
                await asyncio.sleep(1)
                print("已关闭过滤框")
            except Exception as e:
                print(f"关闭过滤框时出错: {e}")
            
            return True
            
        except Exception as e:
            print(f"筛选考核组时出现错误: {e}")
            return False
    
    async def run_automation(self):
        """运行完整的自动化流程"""
        try:
            if not await self.setup_browser():
                return False
            
            # 直接使用UAC一键登录
            print("正在使用UAC一键登录...")
            if not await self.uac_login():
                return False
            
            # 第一步：查找人员视图
            if not await self.find_personnel_view():
                print("人员视图查找失败")
                return False
            
            # 第二步：在人员视图中选择上一个月
            if not await self.select_month_in_personnel_view():
                print("人员视图月份选择失败")
                return False
            
            # 第三步：点击考核定级按钮
            if not await self.click_assessment_grading():
                print("考核定级点击失败")
                return False
            
            # 第四步：在考核定级页面查找表格中的考核组列标题并筛选
            if not await self.filter_assessment_group():
                print("考核组筛选失败")
                return False
            
            # 第五步：更新考核评语
            if not await self.update_assessment_comments():
                print("更新考核评语失败")
                return False
            
            print("自动化流程执行完成！")
            return True
            
        except Exception as e:
            print(f"自动化流程执行失败: {e}")
            return False
        finally:
            if self.page:
                print("按回车键关闭浏览器...")
                input()
                await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            print("浏览器已关闭")
        except Exception as e:
            print(f"清理资源时出现错误: {e}")

async def main():
    """主函数"""
    print("=== ZTE OKR系统自动化脚本 (Playwright版本) ===")
    print("请确保已安装Playwright: pip install playwright")
    print("首次运行需要安装浏览器: playwright install")
    print()
    
    # 创建自动化实例
    automation = ZTEOKRAutomationPlaywright("", "")    
    
    try:
        # 运行自动化流程
        success = await automation.run_automation()
        
        if success:
            print("所有操作已完成！")
        else:
            print("部分操作失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断了操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        await automation.cleanup()

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
