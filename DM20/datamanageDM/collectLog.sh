#!/bin/bash

# 参数1: 结果文件路径, 如 /home/<USER>/ngi-gpdb/ext/dm/collectLogResult/datamangeLog_20230821155640691_1898.zip
# 参数2: 开始时间, 如 20230818
# 参数3: 结束时间, 如 20230820
# 获取指定日期范围内的主从机日志
if [ $# -lt 3 ];then
    exit
fi

if [ "$USER" != "gpadmin" ];then
    echo "ERROR: Must gpadmin to run the script" >&2
    exit
fi

cd $(dirname $0)
curScriptDir=$PWD
logFile="collectLog.$(date +'%Y-%m-%d').log"
pid=$$

{
    echo "[$(date +'%F %T')] INFO [$pid] Begin collect log, arguments: $*"
    resultDir=$(dirname $1)
    retultFileName=$(basename $1)
    startDay=$2
    endDay=$3

    if [ ! -d "$resultDir" ];then
        mkdir -p "$resultDir"
    fi

    cd "$resultDir"
    if [ $? -ne 0 ];then
        echo "[$(date +'%F %T')] [$pid] ERROR Script exiting..."
        exit
    fi

    tempDir=$(mktemp -d "${retultFileName}_XXX")
    tempDirAbs="${resultDir}/${tempDir}"
    echo "[$(date +'%F %T')] INFO [$pid] Generate temp directory $tempDirAbs"
    
    cd "$curScriptDir/logs"
    if [ $? -ne 0 ];then
        echo "[$(date +'%F %T')] ERROR [$pid] Script exiting..."
        exit
    fi

    # 查找符合条件的主机日志 
    # 按日志文件内包含的时间得到日志文件列表
    logFileList1=$(find . -maxdepth 1 \( -name "MasterImport.[^d]*.zip" -o -name "SlaveDownload.[^d]*.zip" \) -type f -printf "%P\n" \
        | xargs -i bash -c 'unzip -p {} | grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
    logFileList2=$(ls SlaveDownload.log MasterImport.log 2>/dev/null | xargs -i bash -c 'grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" {} | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
    # datamanage-20*.gz 及 datamanage-20*.log 取第一行和最后一行的日志时间
    logFileList3=$(find . -maxdepth 1 -name "datamanage-20*.gz" -type f -printf "%P\n" \
        | xargs -i bash -c 'gunzip -c {} | sed -n "1p;$p" | grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
    logFileList4=$(find . -maxdepth 1 -name "datamanage-20*.log" -type f -printf "%P\n" \
        | xargs -i bash -c 'sed -n "1p;$p" {} | grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
    # 汇总日志文件列表，按日期选出符合条件的日志文件
    logFileList=$(echo "$logFileList1"$'\n'"$logFileList2"$'\n'"$logFileList3"$'\n'"$logFileList4" \
        | awk '{print $0}END{print '"${startDay:-20230101}"'":###";print '"${endDay:-20991231}"'":zzz"}' \
        | sort | sed -n '/:###$/,/:zzz$/p' | sed -n '1b;$b;s/^[0-9]*://p' | sort | uniq)
        
    if [ "$logFileList" ];then
        echo "[$(date +'%F %T')] INFO [$pid] Copy valid master logs to ${tempDirAbs}..."
        echo "$logFileList" | xargs \cp -av -t "$tempDirAbs"
    else
        echo "[$(date +'%F %T')] WARN [$pid] No valid master logs found"
        touch "$tempDirAbs/empty"
    fi

    cd "$tempDirAbs"
    # 获取符合条件的从机日志
    if [ -s /home/<USER>/paas_hosts_seg.cc ];then
        echo "[$(date +'%F %T')] INFO [$pid] Begin to collect slave logs ..."
        cat /home/<USER>/paas_hosts_seg.cc
        slaveNum=$(cat /home/<USER>/paas_hosts_seg.cc  | sed '/^$/d' | wc -l)
        cat /home/<USER>/paas_hosts_seg.cc | xargs -P $slaveNum -i mkdir -p "$tempDirAbs/slave_{}"
        cat /home/<USER>/paas_hosts_seg.cc | xargs -P $slaveNum -i timeout 1m rsync -rt -hiv gpadmin@{}:/home/<USER>/ngi-gpdb/ext/dm/logs/SlaveDownload.[^d]* "$tempDirAbs/slave_{}/"
        # 保留符合日期范围内的日志
        slaveLogFileList1=$(find . -mindepth 2 -name "SlaveDownload.[^d]*.zip" -type f -printf "%P\n" \
            | xargs -i bash -c 'unzip -p {} | grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
        slaveLogFileList2=$(find . -mindepth 2 -name "SlaveDownload.log" -type f -printf "%P\n" \
            | xargs -i bash -c 'grep -oP "^20[2-9][0-9]-[01][0-9]-[0-3][0-9](?= )" {} | uniq | sed "s/-//g" | xargs -I"<>" echo "<>:{}"')
        validSlaveLogFileList=$(echo "$slaveLogFileList1"$'\n'"$slaveLogFileList2" \
            | awk '{print $0}END{print '"${startDay:-20230101}"'":###";print '"${endDay:-20991231}"'":zzz"}' \
            | sort | sed -n '/:###$/,/:zzz$/p' | sed -n '1b;$b;s/^[0-9]*://p' | sort | uniq)      

        if [ "$validSlaveLogFileList" ];then
            echo "[$(date +'%F %T')] INFO [$pid] Remove invalid slave logs ..."
            echo "$validSlaveLogFileList" | xargs tar -cf validSlaveLog.tar
            # 删除不符合时间范围的日志
            invalidLogFileList=$(find . -mindepth 2 -name "SlaveDownload.[^d]*" -type f | xargs rm -vf)
            # 恢复符合时间范围的日志
            tar -xf validSlaveLog.tar
            rm -f validSlaveLog.tar
        else
            echo "[$(date +'%F %T')] WARN [$pid] No valid slave logs found"
            find . -mindepth 2 -name "SlaveDownload.[^d]*" -type f | xargs rm -f
        fi
    fi

    # 解压所有zip文件

    # 压缩所有日志到结果文件中
    echo "[$(date +'%F %T')] INFO [$pid] Package all logs to $resultDir/$retultFileName ..."
    zip -r "$resultDir/$retultFileName" *

    # 删除临时日志目录
    cd "$resultDir"
    echo "[$(date +'%F %T')] INFO [$pid] Delete temp directory $resultDir/$tempDir ..."
    rm -rf "$tempDir"
} >>"$curScriptDir/logs/$logFile" 2>&1
