#!/bin/bash
#----------------------------------------------------
# Function    : show_msg 
# Description : print formatted message
# Parameter   : $1 - message
#----------------------------------------------------
show_msg()
{
    echo "***********************************************************************"
    echo "                    $1"
    echo "***********************************************************************"
}



curPath=$(pwd)
cd /home/<USER>/ngi-gpdb/ext/dm/ 
./gpsshDatamanageStop.sh -987789
./gpsshDatamanageStart.sh -987789
cd $curPath
exit 0
