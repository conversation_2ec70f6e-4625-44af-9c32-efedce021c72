#!/bin/bash
# Program : start NRUMEPreProcess module

DIRNAME=`dirname $0`
HOME=`cd $DIRNAME/; pwd`
MODE=${1:-"run"}

#������������ģʽ�Ȳ���run,Ҳ����debug,�˳��ű�
if [ "$MODE" != "run" ]&&[ "$MODE" != "debug" ]; then
   echo "$MODE is not valid,please input \"run\" or \"debug\""
   exit 1
fi

echo Mode=$MODE

OSNAME=`uname`
echo OS=$OSNAME

echo HOME=$HOME

#ͨ������ϵͳ���жϾ���jdkλ��
JAVA_HOME=""
JRE_HOME=""

# if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../CDT/jdk/linux/" ] ; then
   # JAVA_HOME=`cd $HOME/../../CDT/jdk/linux/; pwd`
# fi
if [ $OSNAME = "Linux" ]&&[ -d "$HOME/../../jdk/" ] ; then
   JAVA_HOME=`cd $HOME/../../jdk/; pwd`
fi

if [ $OSNAME = "SunOS" ]&&[ -d "$HOME/../../CDT/jdk/solaris/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/solaris/; pwd`
fi

if [ $OSNAME = "AIX" ]&&[ -d "$HOME/../../CDT/jdk/aix/" ] ; then
   JAVA_HOME=`cd $HOME/../../CDT/jdk/aix/; pwd`
fi

#���û���ҵ�JAVA_HOME,ֱ���˳�

if [ -z $JAVA_HOME ] ; then
   echo "can not find JAVA_HOME"
   exit 1
fi

#����JRE_HOME
JRE_HOME=$JAVA_HOME/jre

echo JAVA_HOME=$JAVA_HOME
echo JRE_HOME=$JRE_HOME

#��solaris��4λϵͳ�������⴦��
if [ $OSNAME != "SunOS" ]; then
    #����Runģʽ�µ�JVM
    _RUNJAVA="$JRE_HOME/bin/java"

    #����Debugģʽ�µ�JVM
    _RUNJDB="$JAVA_HOME/bin/java"
else
    #����Runģʽ�µ�JVM
    _RUNJAVA="$JRE_HOME/bin/sparcv9/java"

    #����Debugģʽ�µ�JVM
    _RUNJDB="$JAVA_HOME/bin/sparcv9/java"
fi



#����JVM��JVM����
JAVA_OPTS="$JAVA_OPTS -Xms2048M -Xmx20480M"
if [ "$MODE" = "run" ] ; then
   JAVA=${_RUNJAVA}
else
   JAVA=${_RUNJDB}
   #JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,address=10001,server=y,suspend=n"
fi

#����javaִ��Ȩ��
chmod a+x $JAVA
echo JAVA=$JAVA

#��ȡ��װ·��
cd $HOME
echo $HOME
echo "starting DataDownloadMonitor process....." 
file="DataDownloadMonitor-1.0-SNAPSHOT.jar"
if [ -f "$file" ]
then  
    pid=`ps -ef |grep $file | grep -v grep | awk '{print $2}'`
    kill -9 $pid &> /dev/null
    chmod +x $file
    nohup $JAVA -jar $file &> /dev/null &
fi
   
echo "start DataDownloadMonitor completely"
