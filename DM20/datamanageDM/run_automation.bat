@echo off
chcp 65001 >nul
echo ========================================
echo ZTE OKR系统自动化脚本启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查Playwright是否安装
python -c "import playwright" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Playwright...
    pip install playwright
    if errorlevel 1 (
        echo 安装Playwright失败，请手动安装
        pause
        exit /b 1
    )
    echo 正在安装浏览器引擎...
    playwright install
)

echo.
echo 启动自动化脚本...
echo.
python zte_okr_playwright.py

echo.
echo 脚本执行完成，按任意键退出...
pause >nul
