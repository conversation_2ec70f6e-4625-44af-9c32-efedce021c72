#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面结构分析脚本 - 用于找到PBC和月份选择按钮的正确选择器
"""

import asyncio
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

class PageStructureAnalyzer:
    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.playwright = None
        self.base_url = "https://unipos.zte.com.cn:18080/#/oct/okr"
        
    async def setup_browser(self):
        """设置Playwright浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--window-size=1920,1080"
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            print("Playwright浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"浏览器初始化失败: {e}")
            return False
    
    async def login(self):
        """登录到OKR系统"""
        try:
            print(f"正在访问: {self.base_url}")
            await self.page.goto(self.base_url, wait_until="networkidle")
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 查找用户名输入框
            username_input = await self.page.wait_for_selector("input[name='username'], input[type='text']", timeout=10000)
            if username_input:
                await username_input.fill("")
                await username_input.type(self.username)
                print("用户名输入完成")
            else:
                print("未找到用户名输入框")
                return False
            
            # 查找密码输入框
            password_input = await self.page.wait_for_selector("input[name='password'], input[type='password']", timeout=5000)
            if password_input:
                await password_input.fill("")
                await password_input.type(self.password)
                print("密码输入完成")
            else:
                print("未找到密码输入框")
                return False
            
            # 查找登录按钮并点击
            login_button = await self.page.wait_for_selector("button:has-text('登录'), button:has-text('Login'), input[type='submit']", timeout=5000)
            if login_button:
                await login_button.click()
                print("登录按钮已点击")
            else:
                print("未找到登录按钮")
                return False
            
            # 等待登录完成
            await asyncio.sleep(5)
            
            # 检查登录后的页面状态
            current_url = self.page.url
            print(f"登录后当前URL: {current_url}")
            
            # 如果不在OKR页面，尝试导航到OKR
            if "okr" not in current_url.lower():
                print("登录成功，但不在OKR页面，尝试导航到OKR...")
                await self.navigate_to_okr()
            
            print("登录成功！")
            return True
                
        except Exception as e:
            print(f"登录过程中出现错误: {e}")
            return False
    
    async def navigate_to_okr(self):
        """导航到OKR页面"""
        try:
            print("正在尝试导航到OKR页面...")
            
            # 尝试多种方式导航到OKR
            okr_urls = [
                "https://unipos.zte.com.cn:18080/#/oct/okr",
                "https://unipos.zte.com.cn:18080/#/okr",
                "https://unipos.zte.com.cn:18080/#/oct"
            ]
            
            for url in okr_urls:
                try:
                    print(f"尝试访问: {url}")
                    await self.page.goto(url, wait_until="networkidle")
                    await asyncio.sleep(3)
                    
                    current_url = self.page.url
                    if "okr" in current_url.lower():
                        print(f"成功导航到OKR页面: {current_url}")
                        return True
                except Exception as e:
                    print(f"访问 {url} 失败: {e}")
                    continue
            
            print("无法导航到OKR页面，将在当前页面继续操作")
            return False
            
        except Exception as e:
            print(f"导航到OKR页面时出现错误: {e}")
            return False
    
    async def analyze_page_structure(self):
        """分析页面结构，找到PBC和月份选择按钮"""
        try:
            print("正在分析页面结构...")
            
            # 等待页面完全加载
            await asyncio.sleep(5)
            
            # 1. 查找所有包含"PBC"文本的元素
            print("\n=== 查找PBC相关元素 ===")
            pbc_elements = await self.page.query_selector_all("*:has-text('PBC')")
            print(f"找到 {len(pbc_elements)} 个包含PBC文本的元素")
            
            for i, elem in enumerate(pbc_elements):
                try:
                    text = await elem.text_content()
                    tag_name = await elem.evaluate("el => el.tagName")
                    class_name = await elem.get_attribute("class")
                    id_name = await elem.get_attribute("id")
                    print(f"  PBC元素 {i+1}: {tag_name}, 文本: {text.strip()}, 类名: {class_name}, ID: {id_name}")
                except Exception as e:
                    print(f"  分析PBC元素 {i+1} 失败: {e}")
            
            # 2. 查找所有可能包含"月"字的元素
            print("\n=== 查找月份相关元素 ===")
            month_elements = await self.page.query_selector_all("*:has-text('月'), *:has-text('Month'), [class*='month'], [class*='date'], [class*='picker']")
            print(f"找到 {len(month_elements)} 个月份相关元素")
            
            for i, elem in enumerate(month_elements[:10]):  # 只显示前10个
                try:
                    text = await elem.text_content()
                    tag_name = await elem.evaluate("el => el.tagName")
                    class_name = await elem.get_attribute("class")
                    id_name = await elem.get_attribute("id")
                    print(f"  月份元素 {i+1}: {tag_name}, 文本: {text.strip() if text else 'N/A'}, 类名: {class_name}, ID: {id_name}")
                except Exception as e:
                    print(f"  分析月份元素 {i+1} 失败: {e}")
            
            # 3. 查找所有按钮和输入框
            print("\n=== 查找所有按钮和输入框 ===")
            interactive_elements = await self.page.query_selector_all("button, input, select, [role='button'], [tabindex]")
            print(f"找到 {len(interactive_elements)} 个交互元素")
            
            for i, elem in enumerate(interactive_elements[:15]):  # 只显示前15个
                try:
                    tag_name = await elem.evaluate("el => el.tagName")
                    class_name = await elem.get_attribute("class")
                    id_name = await elem.get_attribute("id")
                    placeholder = await elem.get_attribute("placeholder")
                    value = await elem.get_attribute("value")
                    text = await elem.text_content()
                    
                    print(f"  交互元素 {i+1}: {tag_name}, 类名: {class_name}, ID: {id_name}")
                    print(f"    占位符: {placeholder}, 值: {value}, 文本: {text.strip() if text else 'N/A'}")
                except Exception as e:
                    print(f"  分析交互元素 {i+1} 失败: {e}")
            
            # 4. 尝试找到PBC附近的月份选择器
            print("\n=== 尝试找到PBC附近的月份选择器 ===")
            if pbc_elements:
                pbc_elem = pbc_elements[0]  # 使用第一个PBC元素
                
                # 尝试多种方式查找附近的月份选择器
                nearby_selectors = [
                    "xpath=./following-sibling::*",
                    "xpath=./following-sibling::*[1]",
                    "xpath=./following-sibling::*[2]",
                    "xpath=./following-sibling::*[3]",
                    "xpath=./..//*[contains(@class, 'month') or contains(@class, 'date') or contains(@class, 'picker')]",
                    "xpath=./..//*[contains(text(), '月') or contains(text(), 'Month')]",
                    "xpath=./..//button",
                    "xpath=./..//input",
                    "xpath=./..//select"
                ]
                
                for selector in nearby_selectors:
                    try:
                        if selector.startswith("xpath="):
                            xpath = selector.replace("xpath=", "")
                            nearby_elem = await pbc_elem.query_selector(f"xpath={xpath}")
                            if nearby_elem:
                                text = await nearby_elem.text_content()
                                tag_name = await nearby_elem.evaluate("el => el.tagName")
                                class_name = await nearby_elem.get_attribute("class")
                                print(f"  找到附近元素: {selector}")
                                print(f"    标签: {tag_name}, 文本: {text.strip() if text else 'N/A'}, 类名: {class_name}")
                    except Exception as e:
                        continue
            
            # 5. 截图保存当前页面状态
            print("\n=== 保存页面截图 ===")
            try:
                await self.page.screenshot(path="page_analysis.png", full_page=True)
                print("页面截图已保存为: page_analysis.png")
            except Exception as e:
                print(f"截图保存失败: {e}")
            
            print("\n页面结构分析完成！")
            print("请查看上面的输出，找到正确的元素选择器")
            
            return True
                
        except Exception as e:
            print(f"页面结构分析时出现错误: {e}")
            return False
    
    async def run_analysis(self):
        """运行页面结构分析"""
        try:
            if not await self.setup_browser():
                return False
            
            if not await self.login():
                return False
            
            if not await self.analyze_page_structure():
                return False
            
            print("\n分析完成！请查看输出结果")
            print("按回车键关闭浏览器...")
            input()
            
            return True
            
        except Exception as e:
            print(f"分析过程中出现错误: {e}")
            return False
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                try:
                    await self.page.close()
                except:
                    pass
            if self.context:
                try:
                    await self.context.close()
                except:
                    pass
            if self.browser:
                try:
                    await self.browser.close()
                except:
                    pass
            if self.playwright:
                try:
                    await self.playwright.stop()
                except:
                    pass
            print("浏览器已关闭")
        except Exception as e:
            print(f"清理资源时出现错误: {e}")

async def main():
    """主函数"""
    print("=== 页面结构分析脚本 ===")
    print("用于找到PBC和月份选择按钮的正确选择器")
    print()
    
    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("用户名和密码不能为空！")
        return
    
    # 创建分析器实例
    analyzer = PageStructureAnalyzer(username, password)
    
    try:
        # 运行分析
        success = await analyzer.run_analysis()
        
        if success:
            print("页面结构分析完成！")
        else:
            print("页面结构分析失败")
            
    except KeyboardInterrupt:
        print("\n用户中断了操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        await analyzer.cleanup()

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
